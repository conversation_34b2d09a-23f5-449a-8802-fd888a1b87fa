<template>
  <div class="node-vform-manager">
    <div class="manager-header">
      <h3>
        <i class="el-icon-s-order"></i>
        NPI流程节点表单配置
      </h3>
      <el-button type="primary" @click="addNodeForm">
        <i class="el-icon-plus"></i>
        添加节点表单
      </el-button>
    </div>

    <div class="node-forms-list">
      <el-collapse v-model="activeNodes" accordion>
        <el-collapse-item 
          v-for="(nodeForm, index) in nodeForms" 
          :key="nodeForm.id"
          :name="nodeForm.id"
        >
          <template slot="title">
            <div class="node-title">
              <i class="el-icon-document"></i>
              <span class="node-name">{{ nodeForm.nodeName }}</span>
              <el-tag :type="getNodeTypeTag(nodeForm.nodeType)" size="mini">
                {{ getNodeTypeText(nodeForm.nodeType) }}
              </el-tag>
              <span class="form-status">{{ nodeForm.formJson ? '已配置' : '未配置' }}</span>
            </div>
          </template>

          <div class="node-form-content">
            <div class="node-config">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="节点名称">
                    <el-input v-model="nodeForm.nodeName" placeholder="请输入节点名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="节点类型">
                    <el-select v-model="nodeForm.nodeType" style="width: 100%">
                      <el-option label="NPI申请" value="npi_apply" />
                      <el-option label="技术评审" value="tech_review" />
                      <el-option label="工艺评审" value="process_review" />
                      <el-option label="质量评审" value="quality_review" />
                      <el-option label="成本评审" value="cost_review" />
                      <el-option label="最终审批" value="final_approval" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="节点标识">
                    <el-input v-model="nodeForm.nodeKey" placeholder="流程图中的节点ID" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="操作">
                    <el-button type="primary" size="small" @click="designForm(nodeForm)">
                      设计表单
                    </el-button>
                    <el-button type="danger" size="small" @click="removeNodeForm(index)">
                      删除
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 表单预览 -->
            <div v-if="nodeForm.formJson" class="form-preview">
              <h5>表单预览</h5>
              <div class="preview-container">
                <v-form-render 
                  :ref="`preview_${nodeForm.id}`"
                  :key="`preview_${nodeForm.id}_${nodeForm.previewKey || 0}`"
                />
              </div>
            </div>
            <div v-else class="no-form">
              <i class="el-icon-document-add"></i>
              <p>暂未配置表单，点击"设计表单"开始配置</p>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>

      <div v-if="nodeForms.length === 0" class="empty-state">
        <i class="el-icon-document-add"></i>
        <p>暂无节点表单，点击"添加节点表单"开始创建</p>
      </div>
    </div>

    <div class="manager-footer">
      <el-button @click="previewAllForms">预览所有表单</el-button>
      <el-button type="primary" @click="saveConfig">保存配置</el-button>
      <el-button @click="exportConfig">导出配置</el-button>
      <el-button @click="importConfig">导入配置</el-button>
    </div>

    <!-- VForm设计器对话框 -->
    <el-dialog 
      :title="`设计表单 - ${currentEditNode.nodeName}`"
      :visible.sync="showDesigner" 
      width="90%" 
      top="5vh"
      :close-on-click-modal="false"
    >
      <div class="vform-designer-container">
        <v-form-designer 
          ref="vfDesigner" 
          :designer-config="designerConfig"
          @form-json-change="onFormJsonChange"
        >
          <template #customToolButtons>
            <el-button type="primary" @click="saveFormDesign">保存表单</el-button>
            <el-button @click="previewForm">预览表单</el-button>
          </template>
        </v-form-designer>
      </div>
    </el-dialog>

    <!-- 表单预览对话框 -->
    <el-dialog title="表单预览" :visible.sync="showPreview" width="60%">
      <div class="form-preview-dialog">
        <v-form-render ref="previewFormRef" />
      </div>
      <div slot="footer">
        <el-button @click="showPreview = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 所有表单预览 -->
    <el-dialog title="所有表单预览" :visible.sync="showAllPreview" width="80%">
      <el-tabs v-model="previewActiveTab" type="card">
        <el-tab-pane 
          v-for="nodeForm in nodeForms.filter(n => n.formJson)" 
          :key="nodeForm.id"
          :label="nodeForm.nodeName"
          :name="nodeForm.id"
        >
          <v-form-render 
            :ref="`allPreview_${nodeForm.id}`"
            :key="`allPreview_${nodeForm.id}_${nodeForm.previewKey || 0}`"
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 导入文件 -->
    <input 
      ref="fileInput" 
      type="file" 
      accept=".json" 
      style="display: none" 
      @change="handleFileImport"
    />
  </div>
</template>

<script>
export default {
  name: 'NodeVFormManager',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    processKey: {
      type: String,
      default: 'npi_process'
    }
  },
  data() {
    return {
      nodeForms: [],
      activeNodes: '',
      showDesigner: false,
      showPreview: false,
      showAllPreview: false,
      previewActiveTab: '',
      currentEditNode: {},
      designerConfig: {
        languageMenu: false,
        externalLink: false,
        formTemplates: true,
        eventCollapse: false,
        widgetCollapse: false,
        clearDesignerButton: true,
        previewFormButton: false,
        importJsonButton: true,
        exportJsonButton: true,
        exportCodeButton: false,
        generateSFCButton: false
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.nodeForms = newVal || [];
        this.loadFormPreviews();
      },
      immediate: true,
      deep: true
    },
    nodeForms: {
      handler(newVal) {
        this.$emit('input', newVal);
      },
      deep: true
    }
  },
  methods: {
    /** 添加节点表单 */
    addNodeForm() {
      const newNodeForm = {
        id: `node_${Date.now()}`,
        nodeName: `NPI节点${this.nodeForms.length + 1}`,
        nodeType: 'npi_apply',
        nodeKey: '',
        formJson: null,
        previewKey: 0
      };
      
      this.nodeForms.push(newNodeForm);
      this.activeNodes = newNodeForm.id;
    },

    /** 删除节点表单 */
    removeNodeForm(index) {
      this.$confirm('确定要删除这个节点表单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.nodeForms.splice(index, 1);
        this.$message.success('删除成功');
      }).catch(() => {});
    },

    /** 设计表单 */
    designForm(nodeForm) {
      this.currentEditNode = nodeForm;
      this.showDesigner = true;
      
      this.$nextTick(() => {
        if (nodeForm.formJson) {
          this.$refs.vfDesigner.setFormJson(nodeForm.formJson);
        } else {
          // 设置默认的空表单
          this.$refs.vfDesigner.setFormJson({
            widgetList: [],
            formConfig: {
              modelName: 'formData',
              refName: 'vForm',
              rulesName: 'rules',
              labelWidth: 80,
              labelPosition: 'left',
              size: '',
              labelAlign: 'label-left-align',
              cssCode: '',
              customClass: '',
              functions: '',
              layoutType: 'PC'
            }
          });
        }
      });
    },

    /** 表单JSON变化 */
    onFormJsonChange(formJson) {
      // 实时保存表单设计
      if (this.currentEditNode.id) {
        const nodeForm = this.nodeForms.find(n => n.id === this.currentEditNode.id);
        if (nodeForm) {
          nodeForm.formJson = formJson;
          nodeForm.previewKey = Date.now();
        }
      }
    },

    /** 保存表单设计 */
    saveFormDesign() {
      const formJson = this.$refs.vfDesigner.getFormJson();
      const nodeForm = this.nodeForms.find(n => n.id === this.currentEditNode.id);
      if (nodeForm) {
        nodeForm.formJson = formJson;
        nodeForm.previewKey = Date.now();
        this.$message.success('表单保存成功');
        this.loadFormPreviews();
      }
    },

    /** 预览表单 */
    previewForm() {
      const formJson = this.$refs.vfDesigner.getFormJson();
      this.showPreview = true;
      this.$nextTick(() => {
        this.$refs.previewFormRef.setFormJson(formJson);
      });
    },

    /** 预览所有表单 */
    previewAllForms() {
      const formsWithJson = this.nodeForms.filter(n => n.formJson);
      if (formsWithJson.length === 0) {
        this.$message.warning('暂无已配置的表单');
        return;
      }
      
      this.previewActiveTab = formsWithJson[0].id;
      this.showAllPreview = true;
      
      this.$nextTick(() => {
        formsWithJson.forEach(nodeForm => {
          const ref = this.$refs[`allPreview_${nodeForm.id}`];
          if (ref && ref[0]) {
            ref[0].setFormJson(nodeForm.formJson);
          }
        });
      });
    },

    /** 加载表单预览 */
    loadFormPreviews() {
      this.$nextTick(() => {
        this.nodeForms.forEach(nodeForm => {
          if (nodeForm.formJson) {
            const ref = this.$refs[`preview_${nodeForm.id}`];
            if (ref && ref[0]) {
              ref[0].setFormJson(nodeForm.formJson);
              ref[0].disableForm();
            }
          }
        });
      });
    },

    /** 获取节点类型标签 */
    getNodeTypeTag(type) {
      const tagMap = {
        npi_apply: 'primary',
        tech_review: 'success',
        process_review: 'warning',
        quality_review: 'danger',
        cost_review: 'info',
        final_approval: 'primary'
      };
      return tagMap[type] || 'primary';
    },

    /** 获取节点类型文本 */
    getNodeTypeText(type) {
      const textMap = {
        npi_apply: 'NPI申请',
        tech_review: '技术评审',
        process_review: '工艺评审',
        quality_review: '质量评审',
        cost_review: '成本评审',
        final_approval: '最终审批'
      };
      return textMap[type] || type;
    },

    /** 保存配置 */
    saveConfig() {
      const config = {
        processKey: this.processKey,
        nodeForms: this.nodeForms,
        createTime: new Date().toISOString(),
        version: '1.0'
      };
      
      // 保存到本地存储
      localStorage.setItem(`node_vform_config_${this.processKey}`, JSON.stringify(config));
      
      this.$message.success('配置保存成功');
      this.$emit('save', config);
    },

    /** 导出配置 */
    exportConfig() {
      const config = {
        processKey: this.processKey,
        nodeForms: this.nodeForms,
        createTime: new Date().toISOString(),
        version: '1.0'
      };
      
      const blob = new Blob([JSON.stringify(config, null, 2)], { 
        type: 'application/json' 
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${this.processKey}_node_forms_${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      this.$message.success('导出成功');
    },

    /** 导入配置 */
    importConfig() {
      this.$refs.fileInput.click();
    },

    /** 处理文件导入 */
    handleFileImport(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const config = JSON.parse(e.target.result);
          if (config.nodeForms && Array.isArray(config.nodeForms)) {
            this.nodeForms = config.nodeForms;
            this.$message.success('导入成功');
            this.loadFormPreviews();
          } else {
            this.$message.error('文件格式不正确');
          }
        } catch (error) {
          this.$message.error('文件解析失败');
        }
      };
      reader.readAsText(file);
      
      event.target.value = '';
    },

    /** 根据节点标识获取表单配置 */
    getFormByNodeKey(nodeKey) {
      return this.nodeForms.find(form => form.nodeKey === nodeKey);
    },

    /** 根据节点类型获取表单配置 */
    getFormByNodeType(nodeType) {
      return this.nodeForms.find(form => form.nodeType === nodeType);
    }
  }
}
</script>

<style lang="scss" scoped>
.node-vform-manager {
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
}

.manager-header {
  background-color: #F5F7FA;
  padding: 16px 20px;
  border-bottom: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    color: #303133;
    font-size: 16px;

    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
}

.node-forms-list {
  padding: 20px;
}

.node-title {
  display: flex;
  align-items: center;
  width: 100%;

  i {
    margin-right: 8px;
    color: #409EFF;
  }

  .node-name {
    font-weight: 600;
    margin-right: 12px;
  }

  .form-status {
    margin-left: auto;
    color: #909399;
    font-size: 12px;
  }
}

.node-form-content {
  padding: 16px 0;
}

.node-config {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #FAFAFA;
  border-radius: 4px;
}

.form-preview {
  margin-top: 16px;
  
  h5 {
    margin: 0 0 12px 0;
    color: #606266;
  }
}

.preview-container {
  border: 1px solid #E4E7ED;
  border-radius: 4px;
  padding: 16px;
  background-color: white;
}

.no-form {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  border: 1px dashed #E4E7ED;
  border-radius: 4px;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.manager-footer {
  padding: 16px 20px;
  border-top: 1px solid #EBEEF5;
  background-color: #FAFAFA;
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;

  i {
    font-size: 64px;
    margin-bottom: 16px;
    display: block;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.vform-designer-container {
  height: 70vh;
  overflow: hidden;
}

.form-preview-dialog {
  min-height: 300px;
  padding: 20px;
  border: 1px solid #E4E7ED;
  border-radius: 4px;
}
</style>
