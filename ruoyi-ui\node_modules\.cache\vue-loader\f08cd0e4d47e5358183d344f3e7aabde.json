{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeFormManager\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeFormManager\\index.vue", "mtime": 1752410071006}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkHA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/NodeFormManager", "sourcesContent": ["<template>\n  <div class=\"node-form-manager\">\n    <div class=\"manager-header\">\n      <h3>\n        <i class=\"el-icon-s-order\"></i>\n        流程节点表单管理\n      </h3>\n      <el-button type=\"primary\" @click=\"addNodeForm\">\n        <i class=\"el-icon-plus\"></i>\n        添加节点表单\n      </el-button>\n    </div>\n\n    <div class=\"node-forms-list\">\n      <el-collapse v-model=\"activeNodes\" accordion>\n        <el-collapse-item \n          v-for=\"(nodeForm, index) in nodeForms\" \n          :key=\"nodeForm.id\"\n          :name=\"nodeForm.id\"\n        >\n          <template slot=\"title\">\n            <div class=\"node-title\">\n              <i class=\"el-icon-document\"></i>\n              <span class=\"node-name\">{{ nodeForm.nodeName }}</span>\n              <el-tag :type=\"getNodeTypeTag(nodeForm.nodeType)\" size=\"mini\">\n                {{ getNodeTypeText(nodeForm.nodeType) }}\n              </el-tag>\n              <span class=\"field-count\">{{ nodeForm.fields.length }} 个字段</span>\n            </div>\n          </template>\n\n          <div class=\"node-form-content\">\n            <div class=\"node-config\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"8\">\n                  <el-form-item label=\"节点名称\">\n                    <el-input v-model=\"nodeForm.nodeName\" placeholder=\"请输入节点名称\" />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"节点类型\">\n                    <el-select v-model=\"nodeForm.nodeType\" style=\"width: 100%\">\n                      <el-option label=\"开始节点\" value=\"start\" />\n                      <el-option label=\"用户任务\" value=\"userTask\" />\n                      <el-option label=\"审批节点\" value=\"approval\" />\n                      <el-option label=\"结束节点\" value=\"end\" />\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"操作\">\n                    <el-button type=\"danger\" size=\"small\" @click=\"removeNodeForm(index)\">\n                      删除节点\n                    </el-button>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </div>\n\n            <div class=\"form-designer\">\n              <node-form \n                v-model=\"nodeForm.fields\" \n                :title=\"`${nodeForm.nodeName} - 表单设计`\"\n              />\n            </div>\n          </div>\n        </el-collapse-item>\n      </el-collapse>\n\n      <div v-if=\"nodeForms.length === 0\" class=\"empty-state\">\n        <i class=\"el-icon-document-add\"></i>\n        <p>暂无节点表单，点击\"添加节点表单\"开始创建</p>\n      </div>\n    </div>\n\n    <div class=\"manager-footer\">\n      <el-button @click=\"previewForms\">预览表单</el-button>\n      <el-button type=\"primary\" @click=\"saveForms\">保存配置</el-button>\n      <el-button @click=\"exportForms\">导出配置</el-button>\n      <el-button @click=\"importForms\">导入配置</el-button>\n    </div>\n\n    <!-- 预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"showPreview\" width=\"80%\">\n      <div class=\"preview-content\">\n        <el-tabs v-model=\"previewActiveTab\" type=\"card\">\n          <el-tab-pane \n            v-for=\"nodeForm in nodeForms\" \n            :key=\"nodeForm.id\"\n            :label=\"nodeForm.nodeName\"\n            :name=\"nodeForm.id\"\n          >\n            <node-form \n              :value=\"nodeForm.fields\" \n              :title=\"nodeForm.nodeName\"\n              :readonly=\"true\"\n            />\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n\n    <!-- 导入配置 -->\n    <input \n      ref=\"fileInput\" \n      type=\"file\" \n      accept=\".json\" \n      style=\"display: none\" \n      @change=\"handleFileImport\"\n    />\n  </div>\n</template>\n\n<script>\nimport NodeForm from '@/components/NodeForm'\n\nexport default {\n  name: 'NodeFormManager',\n  components: {\n    NodeForm\n  },\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      nodeForms: [],\n      activeNodes: '',\n      showPreview: false,\n      previewActiveTab: ''\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        this.nodeForms = newVal || [];\n      },\n      immediate: true,\n      deep: true\n    },\n    nodeForms: {\n      handler(newVal) {\n        this.$emit('input', newVal);\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 添加节点表单 */\n    addNodeForm() {\n      const newNodeForm = {\n        id: `node_${Date.now()}`,\n        nodeName: `节点${this.nodeForms.length + 1}`,\n        nodeType: 'userTask',\n        fields: []\n      };\n      \n      this.nodeForms.push(newNodeForm);\n      this.activeNodes = newNodeForm.id;\n    },\n\n    /** 删除节点表单 */\n    removeNodeForm(index) {\n      this.$confirm('确定要删除这个节点表单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.nodeForms.splice(index, 1);\n        this.$message.success('删除成功');\n      }).catch(() => {});\n    },\n\n    /** 获取节点类型标签 */\n    getNodeTypeTag(type) {\n      const tagMap = {\n        start: 'success',\n        userTask: 'primary',\n        approval: 'warning',\n        end: 'info'\n      };\n      return tagMap[type] || 'primary';\n    },\n\n    /** 获取节点类型文本 */\n    getNodeTypeText(type) {\n      const textMap = {\n        start: '开始节点',\n        userTask: '用户任务',\n        approval: '审批节点',\n        end: '结束节点'\n      };\n      return textMap[type] || type;\n    },\n\n    /** 预览表单 */\n    previewForms() {\n      if (this.nodeForms.length === 0) {\n        this.$message.warning('暂无表单可预览');\n        return;\n      }\n      this.previewActiveTab = this.nodeForms[0].id;\n      this.showPreview = true;\n    },\n\n    /** 保存配置 */\n    saveForms() {\n      // 这里可以调用API保存到后端\n      const config = {\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      console.log('保存表单配置:', config);\n      this.$message.success('保存成功');\n      \n      // 触发保存事件\n      this.$emit('save', config);\n    },\n\n    /** 导出配置 */\n    exportForms() {\n      const config = {\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      const blob = new Blob([JSON.stringify(config, null, 2)], { \n        type: 'application/json' \n      });\n      \n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `node-forms-${Date.now()}.json`;\n      a.click();\n      URL.revokeObjectURL(url);\n      \n      this.$message.success('导出成功');\n    },\n\n    /** 导入配置 */\n    importForms() {\n      this.$refs.fileInput.click();\n    },\n\n    /** 处理文件导入 */\n    handleFileImport(event) {\n      const file = event.target.files[0];\n      if (!file) return;\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const config = JSON.parse(e.target.result);\n          if (config.nodeForms && Array.isArray(config.nodeForms)) {\n            this.nodeForms = config.nodeForms;\n            this.$message.success('导入成功');\n          } else {\n            this.$message.error('文件格式不正确');\n          }\n        } catch (error) {\n          this.$message.error('文件解析失败');\n        }\n      };\n      reader.readAsText(file);\n      \n      // 清空文件输入\n      event.target.value = '';\n    },\n\n    /** 根据节点类型获取表单 */\n    getFormByNodeType(nodeType) {\n      return this.nodeForms.find(form => form.nodeType === nodeType);\n    },\n\n    /** 根据节点名称获取表单 */\n    getFormByNodeName(nodeName) {\n      return this.nodeForms.find(form => form.nodeName === nodeName);\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.node-form-manager {\n  background-color: white;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.manager-header {\n  background-color: #F5F7FA;\n  padding: 16px 20px;\n  border-bottom: 1px solid #EBEEF5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h3 {\n    margin: 0;\n    color: #303133;\n    font-size: 16px;\n\n    i {\n      margin-right: 8px;\n      color: #409EFF;\n    }\n  }\n}\n\n.node-forms-list {\n  padding: 20px;\n}\n\n.node-title {\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  i {\n    margin-right: 8px;\n    color: #409EFF;\n  }\n\n  .node-name {\n    font-weight: 600;\n    margin-right: 12px;\n  }\n\n  .field-count {\n    margin-left: auto;\n    color: #909399;\n    font-size: 12px;\n  }\n}\n\n.node-form-content {\n  padding: 16px 0;\n}\n\n.node-config {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #FAFAFA;\n  border-radius: 4px;\n}\n\n.form-designer {\n  margin-top: 16px;\n}\n\n.manager-footer {\n  padding: 16px 20px;\n  border-top: 1px solid #EBEEF5;\n  background-color: #FAFAFA;\n  text-align: right;\n\n  .el-button {\n    margin-left: 8px;\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n\n  i {\n    font-size: 64px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.preview-content {\n  min-height: 400px;\n}\n</style>\n"]}]}