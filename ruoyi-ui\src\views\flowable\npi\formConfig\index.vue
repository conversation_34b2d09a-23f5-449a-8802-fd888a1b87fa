<template>
  <div class="npi-form-config-page">
    <div class="page-header">
      <div class="header-content">
        <h2>
          <i class="el-icon-setting"></i>
          NPI流程表单配置
        </h2>
        <p>为NPI审核流程的每个节点配置专属的VForm表单</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="quickSetup">
          <i class="el-icon-magic-stick"></i>
          快速配置
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <el-card>
        <div slot="header" class="card-header">
          <span>NPI流程节点表单管理</span>
          <div class="header-actions">
            <el-button type="text" @click="showHelp">
              <i class="el-icon-question"></i>
              配置说明
            </el-button>
          </div>
        </div>

        <node-v-form-manager 
          v-model="npiFormConfig" 
          process-key="npi_process"
          @save="handleSave"
        />
      </el-card>
    </div>

    <!-- 快速配置对话框 -->
    <el-dialog title="NPI流程快速配置" :visible.sync="showQuickSetup" width="600px">
      <div class="quick-setup-content">
        <p>将为您创建标准的NPI审核流程节点配置：</p>
        <ul>
          <li><strong>NPI申请节点</strong>：产品信息、技术规格、市场需求等</li>
          <li><strong>技术评审节点</strong>：技术可行性、设计评估、风险分析等</li>
          <li><strong>工艺评审节点</strong>：生产工艺、制造难度、设备需求等</li>
          <li><strong>质量评审节点</strong>：质量标准、测试方案、认证要求等</li>
          <li><strong>成本评审节点</strong>：成本分析、价格策略、盈利预测等</li>
          <li><strong>最终审批节点</strong>：综合评估、决策意见、后续计划等</li>
        </ul>
        <el-alert 
          title="注意：此操作将覆盖现有配置" 
          type="warning" 
          :closable="false"
          style="margin-top: 16px;"
        />
      </div>
      <div slot="footer">
        <el-button @click="showQuickSetup = false">取消</el-button>
        <el-button type="primary" @click="executeQuickSetup">确定配置</el-button>
      </div>
    </el-dialog>

    <!-- 配置说明对话框 -->
    <el-dialog title="NPI流程表单配置说明" :visible.sync="showHelpDialog" width="700px">
      <div class="help-content">
        <h4>🎯 配置目标</h4>
        <p>为NPI（New Product Introduction）审核流程的每个节点配置专属的VForm表单，实现不同审核阶段的差异化数据收集。</p>
        
        <h4>📋 配置步骤</h4>
        <ol>
          <li><strong>添加节点</strong>：点击"添加节点表单"创建新的审核节点</li>
          <li><strong>配置节点信息</strong>：
            <ul>
              <li>节点名称：显示在界面上的名称</li>
              <li>节点类型：预定义的NPI审核类型</li>
              <li>节点标识：对应流程图中的节点ID（重要！）</li>
            </ul>
          </li>
          <li><strong>设计表单</strong>：点击"设计表单"使用VForm设计器创建专属表单</li>
          <li><strong>保存配置</strong>：完成后保存整体配置</li>
        </ol>
        
        <h4>🔧 节点标识说明</h4>
        <p>节点标识必须与Flowable流程图中的节点ID完全一致，系统将根据此标识匹配对应的表单配置。</p>
        
        <h4>📊 NPI审核节点建议</h4>
        <ul>
          <li><strong>NPI申请</strong>：产品基本信息、市场分析、技术概述</li>
          <li><strong>技术评审</strong>：技术方案、设计文档、技术风险</li>
          <li><strong>工艺评审</strong>：生产工艺、制造成本、产能评估</li>
          <li><strong>质量评审</strong>：质量计划、测试标准、认证需求</li>
          <li><strong>成本评审</strong>：成本结构、定价策略、ROI分析</li>
          <li><strong>最终审批</strong>：综合决策、资源分配、时间计划</li>
        </ul>
        
        <h4>💡 最佳实践</h4>
        <ul>
          <li>每个节点的表单应该专注于该阶段的核心评审内容</li>
          <li>使用合适的字段类型提升用户体验</li>
          <li>为重要字段设置必填验证</li>
          <li>定期备份表单配置</li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import NodeVFormManager from '@/components/NodeVFormManager'

export default {
  name: 'NPIFormConfig',
  components: {
    NodeVFormManager
  },
  data() {
    return {
      npiFormConfig: [],
      showQuickSetup: false,
      showHelpDialog: false
    }
  },
  created() {
    this.loadNPIFormConfig();
  },
  methods: {
    /** 加载NPI表单配置 */
    loadNPIFormConfig() {
      const saved = localStorage.getItem('node_vform_config_npi_process');
      if (saved) {
        try {
          const config = JSON.parse(saved);
          this.npiFormConfig = config.nodeForms || [];
        } catch (error) {
          console.warn('加载NPI表单配置失败:', error);
        }
      }
    },

    /** 处理保存 */
    handleSave(config) {
      // 这里可以调用API保存到后端
      console.log('保存NPI表单配置:', config);
      this.$message.success('NPI表单配置保存成功');
    },

    /** 快速配置 */
    quickSetup() {
      this.showQuickSetup = true;
    },

    /** 执行快速配置 */
    executeQuickSetup() {
      const quickConfig = [
        {
          id: 'npi_apply_node',
          nodeName: 'NPI申请',
          nodeType: 'npi_apply',
          nodeKey: 'npi_apply_task',
          formJson: this.createApplyFormJson(),
          previewKey: Date.now()
        },
        {
          id: 'tech_review_node',
          nodeName: '技术评审',
          nodeType: 'tech_review',
          nodeKey: 'tech_review_task',
          formJson: this.createTechReviewFormJson(),
          previewKey: Date.now()
        },
        {
          id: 'process_review_node',
          nodeName: '工艺评审',
          nodeType: 'process_review',
          nodeKey: 'process_review_task',
          formJson: this.createProcessReviewFormJson(),
          previewKey: Date.now()
        },
        {
          id: 'quality_review_node',
          nodeName: '质量评审',
          nodeType: 'quality_review',
          nodeKey: 'quality_review_task',
          formJson: this.createQualityReviewFormJson(),
          previewKey: Date.now()
        },
        {
          id: 'cost_review_node',
          nodeName: '成本评审',
          nodeType: 'cost_review',
          nodeKey: 'cost_review_task',
          formJson: this.createCostReviewFormJson(),
          previewKey: Date.now()
        },
        {
          id: 'final_approval_node',
          nodeName: '最终审批',
          nodeType: 'final_approval',
          nodeKey: 'final_approval_task',
          formJson: this.createFinalApprovalFormJson(),
          previewKey: Date.now()
        }
      ];

      this.npiFormConfig = quickConfig;
      this.showQuickSetup = false;
      this.$message.success('快速配置完成！');
    },

    /** 创建申请表单JSON */
    createApplyFormJson() {
      return {
        widgetList: [
          {
            type: 'input',
            options: {
              name: 'productName',
              label: '产品名称',
              required: true,
              placeholder: '请输入产品名称'
            }
          },
          {
            type: 'input',
            options: {
              name: 'productCode',
              label: '产品编码',
              required: true,
              placeholder: '请输入产品编码'
            }
          },
          {
            type: 'select',
            options: {
              name: 'productCategory',
              label: '产品类别',
              required: true,
              optionItems: [
                { label: '硬件产品', value: 'hardware' },
                { label: '软件产品', value: 'software' },
                { label: '服务产品', value: 'service' }
              ]
            }
          },
          {
            type: 'textarea',
            options: {
              name: 'productDescription',
              label: '产品描述',
              required: true,
              rows: 4,
              placeholder: '请详细描述产品功能和特性'
            }
          },
          {
            type: 'date',
            options: {
              name: 'expectedLaunchDate',
              label: '预期上市时间',
              required: true
            }
          }
        ],
        formConfig: {
          modelName: 'formData',
          refName: 'vForm',
          rulesName: 'rules',
          labelWidth: 120,
          labelPosition: 'left',
          size: '',
          labelAlign: 'label-left-align',
          cssCode: '',
          customClass: '',
          functions: '',
          layoutType: 'PC'
        }
      };
    },

    /** 创建技术评审表单JSON */
    createTechReviewFormJson() {
      return {
        widgetList: [
          {
            type: 'radio',
            options: {
              name: 'techFeasibility',
              label: '技术可行性',
              required: true,
              optionItems: [
                { label: '完全可行', value: 'feasible' },
                { label: '需要改进', value: 'needs_improvement' },
                { label: '技术风险高', value: 'high_risk' },
                { label: '不可行', value: 'not_feasible' }
              ]
            }
          },
          {
            type: 'textarea',
            options: {
              name: 'techRiskAnalysis',
              label: '技术风险分析',
              required: true,
              rows: 4,
              placeholder: '请分析主要技术风险和应对措施'
            }
          },
          {
            type: 'textarea',
            options: {
              name: 'techRecommendation',
              label: '技术建议',
              required: true,
              rows: 3,
              placeholder: '请提供技术改进建议'
            }
          }
        ],
        formConfig: {
          modelName: 'formData',
          refName: 'vForm',
          rulesName: 'rules',
          labelWidth: 120,
          labelPosition: 'left',
          size: '',
          labelAlign: 'label-left-align',
          cssCode: '',
          customClass: '',
          functions: '',
          layoutType: 'PC'
        }
      };
    },

    /** 创建工艺评审表单JSON */
    createProcessReviewFormJson() {
      return {
        widgetList: [
          {
            type: 'radio',
            options: {
              name: 'processComplexity',
              label: '工艺复杂度',
              required: true,
              optionItems: [
                { label: '简单', value: 'simple' },
                { label: '中等', value: 'medium' },
                { label: '复杂', value: 'complex' },
                { label: '极其复杂', value: 'very_complex' }
              ]
            }
          },
          {
            type: 'number',
            options: {
              name: 'estimatedCost',
              label: '预估制造成本',
              required: true,
              placeholder: '请输入预估成本（元）'
            }
          },
          {
            type: 'textarea',
            options: {
              name: 'processRecommendation',
              label: '工艺建议',
              required: true,
              rows: 4,
              placeholder: '请提供工艺改进建议'
            }
          }
        ],
        formConfig: {
          modelName: 'formData',
          refName: 'vForm',
          rulesName: 'rules',
          labelWidth: 120,
          labelPosition: 'left',
          size: '',
          labelAlign: 'label-left-align',
          cssCode: '',
          customClass: '',
          functions: '',
          layoutType: 'PC'
        }
      };
    },

    /** 创建质量评审表单JSON */
    createQualityReviewFormJson() {
      return {
        widgetList: [
          {
            type: 'checkbox',
            options: {
              name: 'qualityStandards',
              label: '质量标准',
              required: true,
              optionItems: [
                { label: 'ISO 9001', value: 'iso9001' },
                { label: 'ISO 14001', value: 'iso14001' },
                { label: 'CE认证', value: 'ce' },
                { label: 'FCC认证', value: 'fcc' },
                { label: '其他', value: 'other' }
              ]
            }
          },
          {
            type: 'textarea',
            options: {
              name: 'testPlan',
              label: '测试计划',
              required: true,
              rows: 4,
              placeholder: '请描述详细的测试计划'
            }
          },
          {
            type: 'radio',
            options: {
              name: 'qualityRisk',
              label: '质量风险评估',
              required: true,
              optionItems: [
                { label: '低风险', value: 'low' },
                { label: '中等风险', value: 'medium' },
                { label: '高风险', value: 'high' }
              ]
            }
          }
        ],
        formConfig: {
          modelName: 'formData',
          refName: 'vForm',
          rulesName: 'rules',
          labelWidth: 120,
          labelPosition: 'left',
          size: '',
          labelAlign: 'label-left-align',
          cssCode: '',
          customClass: '',
          functions: '',
          layoutType: 'PC'
        }
      };
    },

    /** 创建成本评审表单JSON */
    createCostReviewFormJson() {
      return {
        widgetList: [
          {
            type: 'number',
            options: {
              name: 'developmentCost',
              label: '开发成本',
              required: true,
              placeholder: '请输入开发成本（万元）'
            }
          },
          {
            type: 'number',
            options: {
              name: 'unitCost',
              label: '单位成本',
              required: true,
              placeholder: '请输入单位成本（元）'
            }
          },
          {
            type: 'number',
            options: {
              name: 'expectedPrice',
              label: '预期售价',
              required: true,
              placeholder: '请输入预期售价（元）'
            }
          },
          {
            type: 'textarea',
            options: {
              name: 'costAnalysis',
              label: '成本分析',
              required: true,
              rows: 4,
              placeholder: '请提供详细的成本分析'
            }
          }
        ],
        formConfig: {
          modelName: 'formData',
          refName: 'vForm',
          rulesName: 'rules',
          labelWidth: 120,
          labelPosition: 'left',
          size: '',
          labelAlign: 'label-left-align',
          cssCode: '',
          customClass: '',
          functions: '',
          layoutType: 'PC'
        }
      };
    },

    /** 创建最终审批表单JSON */
    createFinalApprovalFormJson() {
      return {
        widgetList: [
          {
            type: 'radio',
            options: {
              name: 'finalDecision',
              label: '最终决策',
              required: true,
              optionItems: [
                { label: '批准立项', value: 'approved' },
                { label: '有条件批准', value: 'conditional' },
                { label: '需要修改', value: 'needs_revision' },
                { label: '拒绝立项', value: 'rejected' }
              ]
            }
          },
          {
            type: 'textarea',
            options: {
              name: 'decisionReason',
              label: '决策理由',
              required: true,
              rows: 4,
              placeholder: '请说明决策理由'
            }
          },
          {
            type: 'textarea',
            options: {
              name: 'nextSteps',
              label: '后续计划',
              required: false,
              rows: 3,
              placeholder: '请描述后续执行计划'
            }
          }
        ],
        formConfig: {
          modelName: 'formData',
          refName: 'vForm',
          rulesName: 'rules',
          labelWidth: 120,
          labelPosition: 'left',
          size: '',
          labelAlign: 'label-left-align',
          cssCode: '',
          customClass: '',
          functions: '',
          layoutType: 'PC'
        }
      };
    },

    /** 显示帮助 */
    showHelp() {
      this.showHelpDialog = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.npi-form-config-page {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  
  .header-content {
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.page-content {
  .el-card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  span {
    font-weight: 600;
    color: #303133;
  }
}

.quick-setup-content {
  p {
    margin-bottom: 16px;
    color: #606266;
  }
  
  ul {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      color: #606266;
      line-height: 1.6;
      
      strong {
        color: #303133;
      }
    }
  }
}

.help-content {
  h4 {
    color: #303133;
    margin: 16px 0 8px 0;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  p, li {
    color: #606266;
    line-height: 1.6;
  }
  
  ul, ol {
    padding-left: 20px;
  }
  
  li {
    margin-bottom: 4px;
  }
  
  strong {
    color: #303133;
  }
}
</style>
