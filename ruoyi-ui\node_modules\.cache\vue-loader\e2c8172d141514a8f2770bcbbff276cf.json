{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\npi\\formConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\npi\\formConfig\\index.vue", "mtime": 1752411081456}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6GA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/npi/formConfig", "sourcesContent": ["<template>\n  <div class=\"npi-form-config-page\">\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <h2>\n          <i class=\"el-icon-setting\"></i>\n          NPI流程表单配置\n        </h2>\n        <p>为NPI审核流程的每个节点配置专属的VForm表单</p>\n      </div>\n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"quickSetup\">\n          <i class=\"el-icon-magic-stick\"></i>\n          快速配置\n        </el-button>\n      </div>\n    </div>\n\n    <div class=\"page-content\">\n      <el-card>\n        <div slot=\"header\" class=\"card-header\">\n          <span>NPI流程节点表单管理</span>\n          <div class=\"header-actions\">\n            <el-button type=\"text\" @click=\"showHelp\">\n              <i class=\"el-icon-question\"></i>\n              配置说明\n            </el-button>\n          </div>\n        </div>\n\n        <node-v-form-manager \n          v-model=\"npiFormConfig\" \n          process-key=\"npi_process\"\n          @save=\"handleSave\"\n        />\n      </el-card>\n    </div>\n\n    <!-- 快速配置对话框 -->\n    <el-dialog title=\"NPI流程快速配置\" :visible.sync=\"showQuickSetup\" width=\"600px\">\n      <div class=\"quick-setup-content\">\n        <p>将为您创建标准的NPI审核流程节点配置：</p>\n        <ul>\n          <li><strong>NPI申请节点</strong>：产品信息、技术规格、市场需求等</li>\n          <li><strong>技术评审节点</strong>：技术可行性、设计评估、风险分析等</li>\n          <li><strong>工艺评审节点</strong>：生产工艺、制造难度、设备需求等</li>\n          <li><strong>质量评审节点</strong>：质量标准、测试方案、认证要求等</li>\n          <li><strong>成本评审节点</strong>：成本分析、价格策略、盈利预测等</li>\n          <li><strong>最终审批节点</strong>：综合评估、决策意见、后续计划等</li>\n        </ul>\n        <el-alert \n          title=\"注意：此操作将覆盖现有配置\" \n          type=\"warning\" \n          :closable=\"false\"\n          style=\"margin-top: 16px;\"\n        />\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"showQuickSetup = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"executeQuickSetup\">确定配置</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 配置说明对话框 -->\n    <el-dialog title=\"NPI流程表单配置说明\" :visible.sync=\"showHelpDialog\" width=\"700px\">\n      <div class=\"help-content\">\n        <h4>🎯 配置目标</h4>\n        <p>为NPI（New Product Introduction）审核流程的每个节点配置专属的VForm表单，实现不同审核阶段的差异化数据收集。</p>\n        \n        <h4>📋 配置步骤</h4>\n        <ol>\n          <li><strong>添加节点</strong>：点击\"添加节点表单\"创建新的审核节点</li>\n          <li><strong>配置节点信息</strong>：\n            <ul>\n              <li>节点名称：显示在界面上的名称</li>\n              <li>节点类型：预定义的NPI审核类型</li>\n              <li>节点标识：对应流程图中的节点ID（重要！）</li>\n            </ul>\n          </li>\n          <li><strong>设计表单</strong>：点击\"设计表单\"使用VForm设计器创建专属表单</li>\n          <li><strong>保存配置</strong>：完成后保存整体配置</li>\n        </ol>\n        \n        <h4>🔧 节点标识说明</h4>\n        <p>节点标识必须与Flowable流程图中的节点ID完全一致，系统将根据此标识匹配对应的表单配置。</p>\n        \n        <h4>📊 NPI审核节点建议</h4>\n        <ul>\n          <li><strong>NPI申请</strong>：产品基本信息、市场分析、技术概述</li>\n          <li><strong>技术评审</strong>：技术方案、设计文档、技术风险</li>\n          <li><strong>工艺评审</strong>：生产工艺、制造成本、产能评估</li>\n          <li><strong>质量评审</strong>：质量计划、测试标准、认证需求</li>\n          <li><strong>成本评审</strong>：成本结构、定价策略、ROI分析</li>\n          <li><strong>最终审批</strong>：综合决策、资源分配、时间计划</li>\n        </ul>\n        \n        <h4>💡 最佳实践</h4>\n        <ul>\n          <li>每个节点的表单应该专注于该阶段的核心评审内容</li>\n          <li>使用合适的字段类型提升用户体验</li>\n          <li>为重要字段设置必填验证</li>\n          <li>定期备份表单配置</li>\n        </ul>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport NodeVFormManager from '@/components/NodeVFormManager'\n\nexport default {\n  name: 'NPIFormConfig',\n  components: {\n    NodeVFormManager\n  },\n  data() {\n    return {\n      npiFormConfig: [],\n      showQuickSetup: false,\n      showHelpDialog: false\n    }\n  },\n  created() {\n    this.loadNPIFormConfig();\n  },\n  methods: {\n    /** 加载NPI表单配置 */\n    loadNPIFormConfig() {\n      const saved = localStorage.getItem('node_vform_config_npi_process');\n      if (saved) {\n        try {\n          const config = JSON.parse(saved);\n          this.npiFormConfig = config.nodeForms || [];\n        } catch (error) {\n          console.warn('加载NPI表单配置失败:', error);\n        }\n      }\n    },\n\n    /** 处理保存 */\n    handleSave(config) {\n      // 这里可以调用API保存到后端\n      console.log('保存NPI表单配置:', config);\n      this.$message.success('NPI表单配置保存成功');\n    },\n\n    /** 快速配置 */\n    quickSetup() {\n      this.showQuickSetup = true;\n    },\n\n    /** 执行快速配置 */\n    executeQuickSetup() {\n      const quickConfig = [\n        {\n          id: 'npi_apply_node',\n          nodeName: 'NPI申请',\n          nodeType: 'npi_apply',\n          nodeKey: 'npi_apply_task',\n          formJson: this.createApplyFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'tech_review_node',\n          nodeName: '技术评审',\n          nodeType: 'tech_review',\n          nodeKey: 'tech_review_task',\n          formJson: this.createTechReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'process_review_node',\n          nodeName: '工艺评审',\n          nodeType: 'process_review',\n          nodeKey: 'process_review_task',\n          formJson: this.createProcessReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'quality_review_node',\n          nodeName: '质量评审',\n          nodeType: 'quality_review',\n          nodeKey: 'quality_review_task',\n          formJson: this.createQualityReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'cost_review_node',\n          nodeName: '成本评审',\n          nodeType: 'cost_review',\n          nodeKey: 'cost_review_task',\n          formJson: this.createCostReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'final_approval_node',\n          nodeName: '最终审批',\n          nodeType: 'final_approval',\n          nodeKey: 'final_approval_task',\n          formJson: this.createFinalApprovalFormJson(),\n          previewKey: Date.now()\n        }\n      ];\n\n      this.npiFormConfig = quickConfig;\n      this.showQuickSetup = false;\n      this.$message.success('快速配置完成！');\n    },\n\n    /** 创建申请表单JSON */\n    createApplyFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'input',\n            options: {\n              name: 'productName',\n              label: '产品名称',\n              required: true,\n              placeholder: '请输入产品名称'\n            }\n          },\n          {\n            type: 'input',\n            options: {\n              name: 'productCode',\n              label: '产品编码',\n              required: true,\n              placeholder: '请输入产品编码'\n            }\n          },\n          {\n            type: 'select',\n            options: {\n              name: 'productCategory',\n              label: '产品类别',\n              required: true,\n              optionItems: [\n                { label: '硬件产品', value: 'hardware' },\n                { label: '软件产品', value: 'software' },\n                { label: '服务产品', value: 'service' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'productDescription',\n              label: '产品描述',\n              required: true,\n              rows: 4,\n              placeholder: '请详细描述产品功能和特性'\n            }\n          },\n          {\n            type: 'date',\n            options: {\n              name: 'expectedLaunchDate',\n              label: '预期上市时间',\n              required: true\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建技术评审表单JSON */\n    createTechReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'radio',\n            options: {\n              name: 'techFeasibility',\n              label: '技术可行性',\n              required: true,\n              optionItems: [\n                { label: '完全可行', value: 'feasible' },\n                { label: '需要改进', value: 'needs_improvement' },\n                { label: '技术风险高', value: 'high_risk' },\n                { label: '不可行', value: 'not_feasible' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'techRiskAnalysis',\n              label: '技术风险分析',\n              required: true,\n              rows: 4,\n              placeholder: '请分析主要技术风险和应对措施'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'techRecommendation',\n              label: '技术建议',\n              required: true,\n              rows: 3,\n              placeholder: '请提供技术改进建议'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建工艺评审表单JSON */\n    createProcessReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'radio',\n            options: {\n              name: 'processComplexity',\n              label: '工艺复杂度',\n              required: true,\n              optionItems: [\n                { label: '简单', value: 'simple' },\n                { label: '中等', value: 'medium' },\n                { label: '复杂', value: 'complex' },\n                { label: '极其复杂', value: 'very_complex' }\n              ]\n            }\n          },\n          {\n            type: 'number',\n            options: {\n              name: 'estimatedCost',\n              label: '预估制造成本',\n              required: true,\n              placeholder: '请输入预估成本（元）'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'processRecommendation',\n              label: '工艺建议',\n              required: true,\n              rows: 4,\n              placeholder: '请提供工艺改进建议'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建质量评审表单JSON */\n    createQualityReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'checkbox',\n            options: {\n              name: 'qualityStandards',\n              label: '质量标准',\n              required: true,\n              optionItems: [\n                { label: 'ISO 9001', value: 'iso9001' },\n                { label: 'ISO 14001', value: 'iso14001' },\n                { label: 'CE认证', value: 'ce' },\n                { label: 'FCC认证', value: 'fcc' },\n                { label: '其他', value: 'other' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'testPlan',\n              label: '测试计划',\n              required: true,\n              rows: 4,\n              placeholder: '请描述详细的测试计划'\n            }\n          },\n          {\n            type: 'radio',\n            options: {\n              name: 'qualityRisk',\n              label: '质量风险评估',\n              required: true,\n              optionItems: [\n                { label: '低风险', value: 'low' },\n                { label: '中等风险', value: 'medium' },\n                { label: '高风险', value: 'high' }\n              ]\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建成本评审表单JSON */\n    createCostReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'number',\n            options: {\n              name: 'developmentCost',\n              label: '开发成本',\n              required: true,\n              placeholder: '请输入开发成本（万元）'\n            }\n          },\n          {\n            type: 'number',\n            options: {\n              name: 'unitCost',\n              label: '单位成本',\n              required: true,\n              placeholder: '请输入单位成本（元）'\n            }\n          },\n          {\n            type: 'number',\n            options: {\n              name: 'expectedPrice',\n              label: '预期售价',\n              required: true,\n              placeholder: '请输入预期售价（元）'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'costAnalysis',\n              label: '成本分析',\n              required: true,\n              rows: 4,\n              placeholder: '请提供详细的成本分析'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建最终审批表单JSON */\n    createFinalApprovalFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'radio',\n            options: {\n              name: 'finalDecision',\n              label: '最终决策',\n              required: true,\n              optionItems: [\n                { label: '批准立项', value: 'approved' },\n                { label: '有条件批准', value: 'conditional' },\n                { label: '需要修改', value: 'needs_revision' },\n                { label: '拒绝立项', value: 'rejected' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'decisionReason',\n              label: '决策理由',\n              required: true,\n              rows: 4,\n              placeholder: '请说明决策理由'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'nextSteps',\n              label: '后续计划',\n              required: false,\n              rows: 3,\n              placeholder: '请描述后续执行计划'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 显示帮助 */\n    showHelp() {\n      this.showHelpDialog = true;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.npi-form-config-page {\n  padding: 20px;\n  background-color: #f0f2f5;\n  min-height: calc(100vh - 84px);\n}\n\n.page-header {\n  margin-bottom: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  \n  .header-content {\n    h2 {\n      margin: 0 0 8px 0;\n      color: #303133;\n      font-size: 24px;\n      \n      i {\n        margin-right: 8px;\n        color: #409EFF;\n      }\n    }\n    \n    p {\n      margin: 0;\n      color: #606266;\n      font-size: 14px;\n    }\n  }\n}\n\n.page-content {\n  .el-card {\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  }\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  \n  span {\n    font-weight: 600;\n    color: #303133;\n  }\n}\n\n.quick-setup-content {\n  p {\n    margin-bottom: 16px;\n    color: #606266;\n  }\n  \n  ul {\n    padding-left: 20px;\n    \n    li {\n      margin-bottom: 8px;\n      color: #606266;\n      line-height: 1.6;\n      \n      strong {\n        color: #303133;\n      }\n    }\n  }\n}\n\n.help-content {\n  h4 {\n    color: #303133;\n    margin: 16px 0 8px 0;\n    \n    &:first-child {\n      margin-top: 0;\n    }\n  }\n  \n  p, li {\n    color: #606266;\n    line-height: 1.6;\n  }\n  \n  ul, ol {\n    padding-left: 20px;\n  }\n  \n  li {\n    margin-bottom: 4px;\n  }\n  \n  strong {\n    color: #303133;\n  }\n}\n</style>\n"]}]}