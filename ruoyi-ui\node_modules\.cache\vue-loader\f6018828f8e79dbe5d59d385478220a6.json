{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\npi\\formConfig\\index.vue?vue&type=style&index=0&id=358f0d4e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\npi\\formConfig\\index.vue", "mtime": 1752411081456}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLm5waS1mb3JtLWNvbmZpZy1wYWdlIHsKICBwYWRkaW5nOiAyMHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmMGYyZjU7CiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDg0cHgpOwp9CgoucGFnZS1oZWFkZXIgewogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGZsZXgtZW5kOwogIAogIC5oZWFkZXItY29udGVudCB7CiAgICBoMiB7CiAgICAgIG1hcmdpbjogMCAwIDhweCAwOwogICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgZm9udC1zaXplOiAyNHB4OwogICAgICAKICAgICAgaSB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgICAgICAgY29sb3I6ICM0MDlFRkY7CiAgICAgIH0KICAgIH0KICAgIAogICAgcCB7CiAgICAgIG1hcmdpbjogMDsKICAgICAgY29sb3I6ICM2MDYyNjY7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIH0KICB9Cn0KCi5wYWdlLWNvbnRlbnQgewogIC5lbC1jYXJkIHsKICAgIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgfQp9CgouY2FyZC1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgCiAgc3BhbiB7CiAgICBmb250LXdlaWdodDogNjAwOwogICAgY29sb3I6ICMzMDMxMzM7CiAgfQp9CgoucXVpY2stc2V0dXAtY29udGVudCB7CiAgcCB7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgY29sb3I6ICM2MDYyNjY7CiAgfQogIAogIHVsIHsKICAgIHBhZGRpbmctbGVmdDogMjBweDsKICAgIAogICAgbGkgewogICAgICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgICAgIGNvbG9yOiAjNjA2MjY2OwogICAgICBsaW5lLWhlaWdodDogMS42OwogICAgICAKICAgICAgc3Ryb25nIHsKICAgICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgfQogICAgfQogIH0KfQoKLmhlbHAtY29udGVudCB7CiAgaDQgewogICAgY29sb3I6ICMzMDMxMzM7CiAgICBtYXJnaW46IDE2cHggMCA4cHggMDsKICAgIAogICAgJjpmaXJzdC1jaGlsZCB7CiAgICAgIG1hcmdpbi10b3A6IDA7CiAgICB9CiAgfQogIAogIHAsIGxpIHsKICAgIGNvbG9yOiAjNjA2MjY2OwogICAgbGluZS1oZWlnaHQ6IDEuNjsKICB9CiAgCiAgdWwsIG9sIHsKICAgIHBhZGRpbmctbGVmdDogMjBweDsKICB9CiAgCiAgbGkgewogICAgbWFyZ2luLWJvdHRvbTogNHB4OwogIH0KICAKICBzdHJvbmcgewogICAgY29sb3I6ICMzMDMxMzM7CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0jBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/npi/formConfig", "sourcesContent": ["<template>\n  <div class=\"npi-form-config-page\">\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <h2>\n          <i class=\"el-icon-setting\"></i>\n          NPI流程表单配置\n        </h2>\n        <p>为NPI审核流程的每个节点配置专属的VForm表单</p>\n      </div>\n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"quickSetup\">\n          <i class=\"el-icon-magic-stick\"></i>\n          快速配置\n        </el-button>\n      </div>\n    </div>\n\n    <div class=\"page-content\">\n      <el-card>\n        <div slot=\"header\" class=\"card-header\">\n          <span>NPI流程节点表单管理</span>\n          <div class=\"header-actions\">\n            <el-button type=\"text\" @click=\"showHelp\">\n              <i class=\"el-icon-question\"></i>\n              配置说明\n            </el-button>\n          </div>\n        </div>\n\n        <node-v-form-manager \n          v-model=\"npiFormConfig\" \n          process-key=\"npi_process\"\n          @save=\"handleSave\"\n        />\n      </el-card>\n    </div>\n\n    <!-- 快速配置对话框 -->\n    <el-dialog title=\"NPI流程快速配置\" :visible.sync=\"showQuickSetup\" width=\"600px\">\n      <div class=\"quick-setup-content\">\n        <p>将为您创建标准的NPI审核流程节点配置：</p>\n        <ul>\n          <li><strong>NPI申请节点</strong>：产品信息、技术规格、市场需求等</li>\n          <li><strong>技术评审节点</strong>：技术可行性、设计评估、风险分析等</li>\n          <li><strong>工艺评审节点</strong>：生产工艺、制造难度、设备需求等</li>\n          <li><strong>质量评审节点</strong>：质量标准、测试方案、认证要求等</li>\n          <li><strong>成本评审节点</strong>：成本分析、价格策略、盈利预测等</li>\n          <li><strong>最终审批节点</strong>：综合评估、决策意见、后续计划等</li>\n        </ul>\n        <el-alert \n          title=\"注意：此操作将覆盖现有配置\" \n          type=\"warning\" \n          :closable=\"false\"\n          style=\"margin-top: 16px;\"\n        />\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"showQuickSetup = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"executeQuickSetup\">确定配置</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 配置说明对话框 -->\n    <el-dialog title=\"NPI流程表单配置说明\" :visible.sync=\"showHelpDialog\" width=\"700px\">\n      <div class=\"help-content\">\n        <h4>🎯 配置目标</h4>\n        <p>为NPI（New Product Introduction）审核流程的每个节点配置专属的VForm表单，实现不同审核阶段的差异化数据收集。</p>\n        \n        <h4>📋 配置步骤</h4>\n        <ol>\n          <li><strong>添加节点</strong>：点击\"添加节点表单\"创建新的审核节点</li>\n          <li><strong>配置节点信息</strong>：\n            <ul>\n              <li>节点名称：显示在界面上的名称</li>\n              <li>节点类型：预定义的NPI审核类型</li>\n              <li>节点标识：对应流程图中的节点ID（重要！）</li>\n            </ul>\n          </li>\n          <li><strong>设计表单</strong>：点击\"设计表单\"使用VForm设计器创建专属表单</li>\n          <li><strong>保存配置</strong>：完成后保存整体配置</li>\n        </ol>\n        \n        <h4>🔧 节点标识说明</h4>\n        <p>节点标识必须与Flowable流程图中的节点ID完全一致，系统将根据此标识匹配对应的表单配置。</p>\n        \n        <h4>📊 NPI审核节点建议</h4>\n        <ul>\n          <li><strong>NPI申请</strong>：产品基本信息、市场分析、技术概述</li>\n          <li><strong>技术评审</strong>：技术方案、设计文档、技术风险</li>\n          <li><strong>工艺评审</strong>：生产工艺、制造成本、产能评估</li>\n          <li><strong>质量评审</strong>：质量计划、测试标准、认证需求</li>\n          <li><strong>成本评审</strong>：成本结构、定价策略、ROI分析</li>\n          <li><strong>最终审批</strong>：综合决策、资源分配、时间计划</li>\n        </ul>\n        \n        <h4>💡 最佳实践</h4>\n        <ul>\n          <li>每个节点的表单应该专注于该阶段的核心评审内容</li>\n          <li>使用合适的字段类型提升用户体验</li>\n          <li>为重要字段设置必填验证</li>\n          <li>定期备份表单配置</li>\n        </ul>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport NodeVFormManager from '@/components/NodeVFormManager'\n\nexport default {\n  name: 'NPIFormConfig',\n  components: {\n    NodeVFormManager\n  },\n  data() {\n    return {\n      npiFormConfig: [],\n      showQuickSetup: false,\n      showHelpDialog: false\n    }\n  },\n  created() {\n    this.loadNPIFormConfig();\n  },\n  methods: {\n    /** 加载NPI表单配置 */\n    loadNPIFormConfig() {\n      const saved = localStorage.getItem('node_vform_config_npi_process');\n      if (saved) {\n        try {\n          const config = JSON.parse(saved);\n          this.npiFormConfig = config.nodeForms || [];\n        } catch (error) {\n          console.warn('加载NPI表单配置失败:', error);\n        }\n      }\n    },\n\n    /** 处理保存 */\n    handleSave(config) {\n      // 这里可以调用API保存到后端\n      console.log('保存NPI表单配置:', config);\n      this.$message.success('NPI表单配置保存成功');\n    },\n\n    /** 快速配置 */\n    quickSetup() {\n      this.showQuickSetup = true;\n    },\n\n    /** 执行快速配置 */\n    executeQuickSetup() {\n      const quickConfig = [\n        {\n          id: 'npi_apply_node',\n          nodeName: 'NPI申请',\n          nodeType: 'npi_apply',\n          nodeKey: 'npi_apply_task',\n          formJson: this.createApplyFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'tech_review_node',\n          nodeName: '技术评审',\n          nodeType: 'tech_review',\n          nodeKey: 'tech_review_task',\n          formJson: this.createTechReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'process_review_node',\n          nodeName: '工艺评审',\n          nodeType: 'process_review',\n          nodeKey: 'process_review_task',\n          formJson: this.createProcessReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'quality_review_node',\n          nodeName: '质量评审',\n          nodeType: 'quality_review',\n          nodeKey: 'quality_review_task',\n          formJson: this.createQualityReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'cost_review_node',\n          nodeName: '成本评审',\n          nodeType: 'cost_review',\n          nodeKey: 'cost_review_task',\n          formJson: this.createCostReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'final_approval_node',\n          nodeName: '最终审批',\n          nodeType: 'final_approval',\n          nodeKey: 'final_approval_task',\n          formJson: this.createFinalApprovalFormJson(),\n          previewKey: Date.now()\n        }\n      ];\n\n      this.npiFormConfig = quickConfig;\n      this.showQuickSetup = false;\n      this.$message.success('快速配置完成！');\n    },\n\n    /** 创建申请表单JSON */\n    createApplyFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'input',\n            options: {\n              name: 'productName',\n              label: '产品名称',\n              required: true,\n              placeholder: '请输入产品名称'\n            }\n          },\n          {\n            type: 'input',\n            options: {\n              name: 'productCode',\n              label: '产品编码',\n              required: true,\n              placeholder: '请输入产品编码'\n            }\n          },\n          {\n            type: 'select',\n            options: {\n              name: 'productCategory',\n              label: '产品类别',\n              required: true,\n              optionItems: [\n                { label: '硬件产品', value: 'hardware' },\n                { label: '软件产品', value: 'software' },\n                { label: '服务产品', value: 'service' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'productDescription',\n              label: '产品描述',\n              required: true,\n              rows: 4,\n              placeholder: '请详细描述产品功能和特性'\n            }\n          },\n          {\n            type: 'date',\n            options: {\n              name: 'expectedLaunchDate',\n              label: '预期上市时间',\n              required: true\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建技术评审表单JSON */\n    createTechReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'radio',\n            options: {\n              name: 'techFeasibility',\n              label: '技术可行性',\n              required: true,\n              optionItems: [\n                { label: '完全可行', value: 'feasible' },\n                { label: '需要改进', value: 'needs_improvement' },\n                { label: '技术风险高', value: 'high_risk' },\n                { label: '不可行', value: 'not_feasible' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'techRiskAnalysis',\n              label: '技术风险分析',\n              required: true,\n              rows: 4,\n              placeholder: '请分析主要技术风险和应对措施'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'techRecommendation',\n              label: '技术建议',\n              required: true,\n              rows: 3,\n              placeholder: '请提供技术改进建议'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建工艺评审表单JSON */\n    createProcessReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'radio',\n            options: {\n              name: 'processComplexity',\n              label: '工艺复杂度',\n              required: true,\n              optionItems: [\n                { label: '简单', value: 'simple' },\n                { label: '中等', value: 'medium' },\n                { label: '复杂', value: 'complex' },\n                { label: '极其复杂', value: 'very_complex' }\n              ]\n            }\n          },\n          {\n            type: 'number',\n            options: {\n              name: 'estimatedCost',\n              label: '预估制造成本',\n              required: true,\n              placeholder: '请输入预估成本（元）'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'processRecommendation',\n              label: '工艺建议',\n              required: true,\n              rows: 4,\n              placeholder: '请提供工艺改进建议'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建质量评审表单JSON */\n    createQualityReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'checkbox',\n            options: {\n              name: 'qualityStandards',\n              label: '质量标准',\n              required: true,\n              optionItems: [\n                { label: 'ISO 9001', value: 'iso9001' },\n                { label: 'ISO 14001', value: 'iso14001' },\n                { label: 'CE认证', value: 'ce' },\n                { label: 'FCC认证', value: 'fcc' },\n                { label: '其他', value: 'other' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'testPlan',\n              label: '测试计划',\n              required: true,\n              rows: 4,\n              placeholder: '请描述详细的测试计划'\n            }\n          },\n          {\n            type: 'radio',\n            options: {\n              name: 'qualityRisk',\n              label: '质量风险评估',\n              required: true,\n              optionItems: [\n                { label: '低风险', value: 'low' },\n                { label: '中等风险', value: 'medium' },\n                { label: '高风险', value: 'high' }\n              ]\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建成本评审表单JSON */\n    createCostReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'number',\n            options: {\n              name: 'developmentCost',\n              label: '开发成本',\n              required: true,\n              placeholder: '请输入开发成本（万元）'\n            }\n          },\n          {\n            type: 'number',\n            options: {\n              name: 'unitCost',\n              label: '单位成本',\n              required: true,\n              placeholder: '请输入单位成本（元）'\n            }\n          },\n          {\n            type: 'number',\n            options: {\n              name: 'expectedPrice',\n              label: '预期售价',\n              required: true,\n              placeholder: '请输入预期售价（元）'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'costAnalysis',\n              label: '成本分析',\n              required: true,\n              rows: 4,\n              placeholder: '请提供详细的成本分析'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建最终审批表单JSON */\n    createFinalApprovalFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'radio',\n            options: {\n              name: 'finalDecision',\n              label: '最终决策',\n              required: true,\n              optionItems: [\n                { label: '批准立项', value: 'approved' },\n                { label: '有条件批准', value: 'conditional' },\n                { label: '需要修改', value: 'needs_revision' },\n                { label: '拒绝立项', value: 'rejected' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'decisionReason',\n              label: '决策理由',\n              required: true,\n              rows: 4,\n              placeholder: '请说明决策理由'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'nextSteps',\n              label: '后续计划',\n              required: false,\n              rows: 3,\n              placeholder: '请描述后续执行计划'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 显示帮助 */\n    showHelp() {\n      this.showHelpDialog = true;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.npi-form-config-page {\n  padding: 20px;\n  background-color: #f0f2f5;\n  min-height: calc(100vh - 84px);\n}\n\n.page-header {\n  margin-bottom: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  \n  .header-content {\n    h2 {\n      margin: 0 0 8px 0;\n      color: #303133;\n      font-size: 24px;\n      \n      i {\n        margin-right: 8px;\n        color: #409EFF;\n      }\n    }\n    \n    p {\n      margin: 0;\n      color: #606266;\n      font-size: 14px;\n    }\n  }\n}\n\n.page-content {\n  .el-card {\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  }\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  \n  span {\n    font-weight: 600;\n    color: #303133;\n  }\n}\n\n.quick-setup-content {\n  p {\n    margin-bottom: 16px;\n    color: #606266;\n  }\n  \n  ul {\n    padding-left: 20px;\n    \n    li {\n      margin-bottom: 8px;\n      color: #606266;\n      line-height: 1.6;\n      \n      strong {\n        color: #303133;\n      }\n    }\n  }\n}\n\n.help-content {\n  h4 {\n    color: #303133;\n    margin: 16px 0 8px 0;\n    \n    &:first-child {\n      margin-top: 0;\n    }\n  }\n  \n  p, li {\n    color: #606266;\n    line-height: 1.6;\n  }\n  \n  ul, ol {\n    padding-left: 20px;\n  }\n  \n  li {\n    margin-bottom: 4px;\n  }\n  \n  strong {\n    color: #303133;\n  }\n}\n</style>\n"]}]}