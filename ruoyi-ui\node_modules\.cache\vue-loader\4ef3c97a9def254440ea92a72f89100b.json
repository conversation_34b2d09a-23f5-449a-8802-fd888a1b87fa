{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormPreview.vue?vue&type=template&id=25976f8b&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormPreview.vue", "mtime": 1752386549087}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752199741460}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}