{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\RuoYi-flowable\\ruoyi-ui\\src\\api\\system\\form.js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\api\\system\\form.js", "mtime": 1752385648190}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1752199756045}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9SdW9ZaS1mbG93YWJsZS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkRm9ybSA9IGFkZEZvcm07CmV4cG9ydHMuZGVsRm9ybSA9IGRlbEZvcm07CmV4cG9ydHMuZ2V0Rm9ybSA9IGdldEZvcm07CmV4cG9ydHMubGlzdEZvcm0gPSBsaXN0Rm9ybTsKZXhwb3J0cy51cGRhdGVGb3JtID0gdXBkYXRlRm9ybTsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouihqOWNleWIl+ihqApmdW5jdGlvbiBsaXN0Rm9ybShxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2Zsb3dhYmxlL2Zvcm0vbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LooajljZXor6bnu4YKZnVuY3Rpb24gZ2V0Rm9ybShmb3JtSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mbG93YWJsZS9mb3JtLycgKyBmb3JtSWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuihqOWNlQpmdW5jdGlvbiBhZGRGb3JtKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mbG93YWJsZS9mb3JtJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnooajljZUKZnVuY3Rpb24gdXBkYXRlRm9ybShkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvZmxvd2FibGUvZm9ybScsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTooajljZUKZnVuY3Rpb24gZGVsRm9ybShmb3JtSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9mbG93YWJsZS9mb3JtLycgKyBmb3JtSWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listForm", "query", "request", "url", "method", "params", "getForm", "formId", "addForm", "data", "updateForm", "delForm"], "sources": ["D:/RuoYi-flowable/ruoyi-ui/src/api/system/form.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询表单列表\nexport function listForm(query) {\n  return request({\n    url: '/flowable/form/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询表单详细\nexport function getForm(formId) {\n  return request({\n    url: '/flowable/form/' + formId,\n    method: 'get'\n  })\n}\n\n// 新增表单\nexport function addForm(data) {\n  return request({\n    url: '/flowable/form',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改表单\nexport function updateForm(data) {\n  return request({\n    url: '/flowable/form',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除表单\nexport function delForm(formId) {\n  return request({\n    url: '/flowable/form/' + formId,\n    method: 'delete'\n  })\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,MAAM;IAC/BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,MAAM;IAC/BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}