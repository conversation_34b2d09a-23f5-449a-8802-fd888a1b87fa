{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\system\\form\\designer.vue?vue&type=style&index=0&id=81ed3fe0&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\system\\form\\designer.vue", "mtime": 1752386648060}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZm9ybS1kZXNpZ25lci1wYWdlIHsKICBoZWlnaHQ6IDEwMHZoOwogIG92ZXJmbG93OiBoaWRkZW47Cn0K"}, {"version": 3, "sources": ["designer.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA;AACA;AACA;AACA", "file": "designer.vue", "sourceRoot": "src/views/system/form", "sourcesContent": ["<template>\n  <div class=\"form-designer-page\">\n    <form-designer \n      :form-data=\"formData\"\n      @save=\"handleSave\"\n      @cancel=\"handleCancel\"\n    />\n  </div>\n</template>\n\n<script>\nimport FormDesigner from '@/components/FormDesigner/index.vue'\n\nexport default {\n  name: 'FormDesignerPage',\n  components: {\n    FormDesigner\n  },\n  data() {\n    return {\n      formData: {\n        formId: null,\n        formName: '新建表单',\n        formContent: '',\n        remark: ''\n      }\n    }\n  },\n  created() {\n    // 如果有传入的表单ID，则加载表单数据\n    const formId = this.$route.query.formId\n    if (formId) {\n      this.loadFormData(formId)\n    }\n  },\n  methods: {\n    loadFormData(formId) {\n      // 这里可以调用API加载表单数据\n      // getForm(formId).then(response => {\n      //   this.formData = response.data\n      // })\n    },\n    \n    handleSave(formData) {\n      console.log('保存表单数据:', formData)\n      this.$message.success('表单保存成功！')\n      // 这里可以调用API保存表单\n      // if (formData.formId) {\n      //   updateForm(formData).then(() => {\n      //     this.$message.success('表单更新成功')\n      //     this.$router.go(-1)\n      //   })\n      // } else {\n      //   addForm(formData).then(() => {\n      //     this.$message.success('表单创建成功')\n      //     this.$router.go(-1)\n      //   })\n      // }\n    },\n    \n    handleCancel() {\n      this.$router.go(-1)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-designer-page {\n  height: 100vh;\n  overflow: hidden;\n}\n</style>\n"]}]}