{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormProperties.vue?vue&type=style&index=0&id=18484580&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormProperties.vue", "mtime": 1752386494977}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5mb3JtLXByb3BlcnRpZXMgewogIGhlaWdodDogMTAwJTsKICAKICAub3B0aW9ucy1jb25maWcsIC52YWxpZGF0aW9uLWNvbmZpZyB7CiAgICAub3B0aW9ucy1oZWFkZXIsIC52YWxpZGF0aW9uLWhlYWRlciB7CiAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICB9CiAgICAKICAgIC5vcHRpb24taXRlbSwgLnJ1bGUtaXRlbSB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICAgIHBhZGRpbmc6IDEwcHg7CiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgCiAgICAgIC5lbC1pbnB1dCB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgICB9CiAgICB9CiAgICAKICAgIC5ydWxlLWl0ZW0gewogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICBhbGlnbi1pdGVtczogc3RyZXRjaDsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["FormProperties.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2aA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "FormProperties.vue", "sourceRoot": "src/components/FormDesigner/components", "sourcesContent": ["<template>\n  <div class=\"form-properties\">\n    <el-tabs v-model=\"activeTab\" type=\"border-card\">\n      <!-- 基础属性 -->\n      <el-tab-pane label=\"基础属性\" name=\"basic\">\n        <el-form :model=\"properties\" label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"字段标识\">\n            <el-input v-model=\"properties.id\" disabled />\n          </el-form-item>\n          \n          <el-form-item label=\"标签文字\">\n            <el-input v-model=\"properties.label\" @input=\"updateProperty('label', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"占位提示\" v-if=\"hasPlaceholder\">\n            <el-input v-model=\"properties.placeholder\" @input=\"updateProperty('placeholder', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"默认值\" v-if=\"hasDefaultValue\">\n            <component\n              :is=\"getDefaultValueComponent()\"\n              v-model=\"properties.defaultValue\"\n              @input=\"updateProperty('defaultValue', $event)\"\n              v-bind=\"getDefaultValueProps()\"\n            />\n          </el-form-item>\n\n          <el-form-item label=\"是否必填\" v-if=\"needsValidation\">\n            <el-switch v-model=\"properties.required\" @change=\"updateProperty('required', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"是否禁用\" v-if=\"hasDisabled\">\n            <el-switch v-model=\"properties.disabled\" @change=\"updateProperty('disabled', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"是否只读\" v-if=\"hasReadonly\">\n            <el-switch v-model=\"properties.readonly\" @change=\"updateProperty('readonly', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"是否可清空\" v-if=\"hasClearable\">\n            <el-switch v-model=\"properties.clearable\" @change=\"updateProperty('clearable', $event)\" />\n          </el-form-item>\n        </el-form>\n      </el-tab-pane>\n\n      <!-- 高级属性 -->\n      <el-tab-pane label=\"高级属性\" name=\"advanced\" v-if=\"hasAdvancedProps\">\n        <el-form :model=\"properties\" label-width=\"80px\" size=\"small\">\n          <!-- 输入框特有属性 -->\n          <template v-if=\"item.type === 'input' || item.type === 'textarea'\">\n            <el-form-item label=\"最大长度\">\n              <el-input-number v-model=\"properties.maxlength\" :min=\"0\" @change=\"updateProperty('maxlength', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示字数\">\n              <el-switch v-model=\"properties.showWordLimit\" @change=\"updateProperty('showWordLimit', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 数字输入框特有属性 -->\n          <template v-if=\"item.type === 'number'\">\n            <el-form-item label=\"最小值\">\n              <el-input-number v-model=\"properties.min\" @change=\"updateProperty('min', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"最大值\">\n              <el-input-number v-model=\"properties.max\" @change=\"updateProperty('max', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"步长\">\n              <el-input-number v-model=\"properties.step\" :min=\"0\" @change=\"updateProperty('step', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"精度\">\n              <el-input-number v-model=\"properties.precision\" :min=\"0\" @change=\"updateProperty('precision', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 选择器特有属性 -->\n          <template v-if=\"item.type === 'select'\">\n            <el-form-item label=\"多选\">\n              <el-switch v-model=\"properties.multiple\" @change=\"updateProperty('multiple', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"可搜索\">\n              <el-switch v-model=\"properties.filterable\" @change=\"updateProperty('filterable', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"允许创建\">\n              <el-switch v-model=\"properties.allowCreate\" @change=\"updateProperty('allowCreate', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 开关特有属性 -->\n          <template v-if=\"item.type === 'switch'\">\n            <el-form-item label=\"开启文字\">\n              <el-input v-model=\"properties.activeText\" @input=\"updateProperty('activeText', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"关闭文字\">\n              <el-input v-model=\"properties.inactiveText\" @input=\"updateProperty('inactiveText', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"开启值\">\n              <el-input v-model=\"properties.activeValue\" @input=\"updateProperty('activeValue', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"关闭值\">\n              <el-input v-model=\"properties.inactiveValue\" @input=\"updateProperty('inactiveValue', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 滑块特有属性 -->\n          <template v-if=\"item.type === 'slider'\">\n            <el-form-item label=\"显示输入框\">\n              <el-switch v-model=\"properties.showInput\" @change=\"updateProperty('showInput', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示间断点\">\n              <el-switch v-model=\"properties.showStops\" @change=\"updateProperty('showStops', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"范围选择\">\n              <el-switch v-model=\"properties.range\" @change=\"updateProperty('range', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 评分特有属性 -->\n          <template v-if=\"item.type === 'rate'\">\n            <el-form-item label=\"最大分值\">\n              <el-input-number v-model=\"properties.max\" :min=\"1\" @change=\"updateProperty('max', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"允许半选\">\n              <el-switch v-model=\"properties.allowHalf\" @change=\"updateProperty('allowHalf', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示文字\">\n              <el-switch v-model=\"properties.showText\" @change=\"updateProperty('showText', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示分数\">\n              <el-switch v-model=\"properties.showScore\" @change=\"updateProperty('showScore', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 上传特有属性 -->\n          <template v-if=\"item.type === 'upload'\">\n            <el-form-item label=\"上传地址\">\n              <el-input v-model=\"properties.action\" @input=\"updateProperty('action', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"多选\">\n              <el-switch v-model=\"properties.multiple\" @change=\"updateProperty('multiple', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"拖拽上传\">\n              <el-switch v-model=\"properties.drag\" @change=\"updateProperty('drag', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"文件类型\">\n              <el-input v-model=\"properties.accept\" @input=\"updateProperty('accept', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"文件个数限制\">\n              <el-input-number v-model=\"properties.limit\" :min=\"0\" @change=\"updateProperty('limit', $event)\" />\n            </el-form-item>\n          </template>\n        </el-form>\n      </el-tab-pane>\n\n      <!-- 选项配置 -->\n      <el-tab-pane label=\"选项配置\" name=\"options\" v-if=\"hasOptions\">\n        <div class=\"options-config\">\n          <div class=\"options-header\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addOption\">添加选项</el-button>\n          </div>\n          <div class=\"options-list\">\n            <div \n              v-for=\"(option, index) in properties.options\" \n              :key=\"index\"\n              class=\"option-item\"\n            >\n              <el-input \n                v-model=\"option.label\" \n                placeholder=\"选项标签\"\n                size=\"small\"\n                @input=\"updateOptions\"\n              />\n              <el-input \n                v-model=\"option.value\" \n                placeholder=\"选项值\"\n                size=\"small\"\n                @input=\"updateOptions\"\n              />\n              <el-button \n                type=\"danger\" \n                size=\"small\" \n                icon=\"el-icon-delete\"\n                @click=\"removeOption(index)\"\n              />\n            </div>\n          </div>\n        </div>\n      </el-tab-pane>\n\n      <!-- 样式配置 -->\n      <el-tab-pane label=\"样式配置\" name=\"style\">\n        <el-form :model=\"properties\" label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"栅格占位\">\n            <el-slider \n              v-model=\"properties.span\" \n              :min=\"1\" \n              :max=\"24\" \n              show-input\n              @change=\"updateProperty('span', $event)\"\n            />\n          </el-form-item>\n\n          <el-form-item label=\"标签宽度\">\n            <el-input v-model=\"properties.labelWidth\" @input=\"updateProperty('labelWidth', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"组件宽度\" v-if=\"hasStyle\">\n            <el-input v-model=\"styleWidth\" @input=\"updateStyleWidth\" placeholder=\"如: 100%, 200px\" />\n          </el-form-item>\n\n          <!-- 文本样式 -->\n          <template v-if=\"item.type === 'text'\">\n            <el-form-item label=\"文字对齐\">\n              <el-select v-model=\"properties.textAlign\" @change=\"updateProperty('textAlign', $event)\">\n                <el-option label=\"左对齐\" value=\"left\" />\n                <el-option label=\"居中\" value=\"center\" />\n                <el-option label=\"右对齐\" value=\"right\" />\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"字体大小\">\n              <el-input v-model=\"properties.fontSize\" @input=\"updateProperty('fontSize', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"字体颜色\">\n              <el-color-picker v-model=\"properties.color\" @change=\"updateProperty('color', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"字体粗细\">\n              <el-select v-model=\"properties.fontWeight\" @change=\"updateProperty('fontWeight', $event)\">\n                <el-option label=\"正常\" value=\"normal\" />\n                <el-option label=\"粗体\" value=\"bold\" />\n              </el-select>\n            </el-form-item>\n          </template>\n        </el-form>\n      </el-tab-pane>\n\n      <!-- 验证规则 -->\n      <el-tab-pane label=\"验证规则\" name=\"validation\" v-if=\"needsValidation\">\n        <div class=\"validation-config\">\n          <div class=\"validation-header\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addRule\">添加规则</el-button>\n          </div>\n          <div class=\"validation-list\">\n            <div \n              v-for=\"(rule, index) in properties.rules\" \n              :key=\"index\"\n              class=\"rule-item\"\n            >\n              <el-form :model=\"rule\" label-width=\"60px\" size=\"small\">\n                <el-form-item label=\"类型\">\n                  <el-select v-model=\"rule.type\" @change=\"updateRules\">\n                    <el-option label=\"必填\" value=\"required\" />\n                    <el-option label=\"最小长度\" value=\"min\" />\n                    <el-option label=\"最大长度\" value=\"max\" />\n                    <el-option label=\"正则\" value=\"pattern\" />\n                    <el-option label=\"自定义\" value=\"validator\" />\n                  </el-select>\n                </el-form-item>\n                <el-form-item label=\"值\" v-if=\"rule.type !== 'required'\">\n                  <el-input v-model=\"rule.value\" @input=\"updateRules\" />\n                </el-form-item>\n                <el-form-item label=\"提示\">\n                  <el-input v-model=\"rule.message\" @input=\"updateRules\" />\n                </el-form-item>\n                <el-form-item>\n                  <el-button type=\"danger\" size=\"small\" @click=\"removeRule(index)\">删除</el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n          </div>\n        </div>\n      </el-tab-pane>\n    </el-tabs>\n  </div>\n</template>\n\n<script>\nimport { isLayoutComponent, needsValidation } from '../utils/index.js'\n\nexport default {\n  name: 'FormProperties',\n  props: {\n    item: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      activeTab: 'basic',\n      properties: {}\n    }\n  },\n  computed: {\n    needsValidation() {\n      return needsValidation(this.item.type)\n    },\n    \n    hasPlaceholder() {\n      return ['input', 'textarea', 'number', 'password', 'select', 'date', 'time', 'cascader'].includes(this.item.type)\n    },\n    \n    hasDefaultValue() {\n      return !isLayoutComponent(this.item.type)\n    },\n    \n    hasDisabled() {\n      return !['divider', 'text', 'html', 'alert'].includes(this.item.type)\n    },\n    \n    hasReadonly() {\n      return ['input', 'textarea', 'password', 'date', 'time'].includes(this.item.type)\n    },\n    \n    hasClearable() {\n      return ['input', 'select', 'date', 'time', 'cascader'].includes(this.item.type)\n    },\n    \n    hasAdvancedProps() {\n      return !['divider', 'text', 'html', 'alert'].includes(this.item.type)\n    },\n    \n    hasOptions() {\n      return ['select', 'radio', 'checkbox'].includes(this.item.type)\n    },\n    \n    hasStyle() {\n      return this.properties.style && typeof this.properties.style === 'object'\n    },\n    \n    styleWidth: {\n      get() {\n        return this.hasStyle ? this.properties.style.width : ''\n      },\n      set(value) {\n        this.updateStyleWidth(value)\n      }\n    }\n  },\n  watch: {\n    item: {\n      handler(newItem) {\n        this.properties = { ...newItem }\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    updateProperty(key, value) {\n      this.properties[key] = value\n      this.$emit('update', { [key]: value })\n    },\n    \n    updateOptions() {\n      this.$emit('update', { options: this.properties.options })\n    },\n    \n    updateRules() {\n      this.$emit('update', { rules: this.properties.rules })\n    },\n    \n    updateStyleWidth(value) {\n      if (!this.properties.style) {\n        this.properties.style = {}\n      }\n      this.properties.style.width = value\n      this.$emit('update', { style: this.properties.style })\n    },\n    \n    addOption() {\n      if (!this.properties.options) {\n        this.properties.options = []\n      }\n      this.properties.options.push({\n        label: `选项${this.properties.options.length + 1}`,\n        value: `${this.properties.options.length + 1}`\n      })\n      this.updateOptions()\n    },\n    \n    removeOption(index) {\n      this.properties.options.splice(index, 1)\n      this.updateOptions()\n    },\n    \n    addRule() {\n      if (!this.properties.rules) {\n        this.properties.rules = []\n      }\n      this.properties.rules.push({\n        type: 'required',\n        message: '此字段为必填项',\n        trigger: 'blur'\n      })\n      this.updateRules()\n    },\n    \n    removeRule(index) {\n      this.properties.rules.splice(index, 1)\n      this.updateRules()\n    },\n    \n    getDefaultValueComponent() {\n      const componentMap = {\n        'input': 'el-input',\n        'textarea': 'el-input',\n        'number': 'el-input-number',\n        'password': 'el-input',\n        'switch': 'el-switch',\n        'slider': 'el-slider',\n        'rate': 'el-rate'\n      }\n      return componentMap[this.item.type] || 'el-input'\n    },\n    \n    getDefaultValueProps() {\n      const propsMap = {\n        'textarea': { type: 'textarea' },\n        'password': { type: 'password', showPassword: true },\n        'number': { controlsPosition: 'right' }\n      }\n      return propsMap[this.item.type] || {}\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-properties {\n  height: 100%;\n  \n  .options-config, .validation-config {\n    .options-header, .validation-header {\n      margin-bottom: 10px;\n    }\n    \n    .option-item, .rule-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 10px;\n      padding: 10px;\n      border: 1px solid #e4e7ed;\n      border-radius: 4px;\n      \n      .el-input {\n        margin-right: 10px;\n      }\n    }\n    \n    .rule-item {\n      flex-direction: column;\n      align-items: stretch;\n    }\n  }\n}\n</style>\n"]}]}