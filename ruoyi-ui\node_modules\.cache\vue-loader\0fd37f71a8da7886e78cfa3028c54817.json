{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue?vue&type=template&id=9469f620&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue", "mtime": 1752396809813}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752199741460}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}