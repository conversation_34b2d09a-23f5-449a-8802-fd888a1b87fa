{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue?vue&type=template&id=9469f620&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue", "mtime": 1752396969884}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752199741460}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}