{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752406353550}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRmxvd0hpc3RvcnknLAogIHByb3BzOiB7CiAgICBmbG93UmVjb3JkTGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0sCiAgICBkZWZhdWx0RXhwYW5kZWQ6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogdHJ1ZQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZUhpc3RvcnlOYW1lczogW10KICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBmbG93UmVjb3JkTGlzdDogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmIChuZXdWYWwgJiYgbmV3VmFsLmxlbmd0aCA+IDAgJiYgdGhpcy5kZWZhdWx0RXhwYW5kZWQpIHsKICAgICAgICAgIC8vIOm7mOiupOWxleW8gOesrOS4gOS4quiKgueCuQogICAgICAgICAgdGhpcy5hY3RpdmVIaXN0b3J5TmFtZXMgPSBbJ2hpc3RvcnktMCddOwogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog6I635Y+W5Y6G5Y+y6IqC54K55Zu+5qCHICovCiAgICBnZXRIaXN0b3J5SWNvbihyZWNvcmQpIHsKICAgICAgaWYgKHJlY29yZC5maW5pc2hUaW1lKSB7CiAgICAgICAgcmV0dXJuICdlbC1pY29uLWNoZWNrJzsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ2VsLWljb24tdGltZSc7CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOiOt+WPluWOhuWPsuiKgueCueminOiJsiAqLwogICAgZ2V0SGlzdG9yeUNvbG9yKHJlY29yZCkgewogICAgICBpZiAocmVjb3JkLmZpbmlzaFRpbWUpIHsKICAgICAgICByZXR1cm4gJyM2N0MyM0EnOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnI0U2QTIzQyc7CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOiOt+WPlueKtuaAgeagh+etvuexu+WeiyAqLwogICAgZ2V0U3RhdHVzVGFnVHlwZShyZWNvcmQpIHsKICAgICAgaWYgKHJlY29yZC5maW5pc2hUaW1lKSB7CiAgICAgICAgcmV0dXJuICdzdWNjZXNzJzsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ3dhcm5pbmcnOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDojrflj5bnirbmgIHmlofmnKwgKi8KICAgIGdldFN0YXR1c1RleHQocmVjb3JkKSB7CiAgICAgIGlmIChyZWNvcmQuZmluaXNoVGltZSkgewogICAgICAgIHJldHVybiAn5bey5a6M5oiQJzsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ+WkhOeQhuS4rSc7CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FlowHistory", "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlowHistory',\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: []\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0 && this.defaultExpanded) {\n          // 默认展开第一个节点\n          this.activeHistoryNames = ['history-0'];\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n</style>\n"]}]}