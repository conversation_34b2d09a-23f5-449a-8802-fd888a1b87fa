{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752412433836}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFByb2Nlc3NWYXJpYWJsZXMgfSBmcm9tICdAL2FwaS9mbG93YWJsZS9kZWZpbml0aW9uJwppbXBvcnQgRmllbGRSZW5kZXJlciBmcm9tICdAL2NvbXBvbmVudHMvRmllbGRSZW5kZXJlcicKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRmxvd0hpc3RvcnknLAogIGNvbXBvbmVudHM6IHsKICAgIEZpZWxkUmVuZGVyZXIKICB9LAogIHByb3BzOiB7CiAgICBmbG93UmVjb3JkTGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0sCiAgICBkZWZhdWx0RXhwYW5kZWQ6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogdHJ1ZQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZUhpc3RvcnlOYW1lczogW10sCiAgICAgIG5vZGVGb3JtRGF0YToge30sIC8vIOWtmOWCqOavj+S4quiKgueCueeahOihqOWNleaVsOaNrgogICAgICBsb2FkaW5nTm9kZXM6IG5ldyBTZXQoKSwgLy8g5q2j5Zyo5Yqg6L2955qE6IqC54K5CiAgICAgIG5vZGVWRm9ybUNvbmZpZ3M6IFtdLCAvLyDoioLngrlWRm9ybemFjee9rgogICAgICBub2RlRm9ybU1vZGVzOiB7fSAvLyDoioLngrnooajljZXmmL7npLrmqKHlvI/vvJpmb3JtKOihqOWNleinhuWbvikg5oiWIGRhdGEo5pWw5o2u6KeG5Zu+KQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIGZsb3dSZWNvcmRMaXN0OiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgaWYgKG5ld1ZhbCAmJiBuZXdWYWwubGVuZ3RoID4gMCkgewogICAgICAgICAgaWYgKHRoaXMuZGVmYXVsdEV4cGFuZGVkKSB7CiAgICAgICAgICAgIC8vIOm7mOiupOWxleW8gOesrOS4gOS4quiKgueCuQogICAgICAgICAgICB0aGlzLmFjdGl2ZUhpc3RvcnlOYW1lcyA9IFsnaGlzdG9yeS0wJ107CiAgICAgICAgICB9CiAgICAgICAgICAvLyDliqDovb3miYDmnInoioLngrnnmoTooajljZXmlbDmja4KICAgICAgICAgIHRoaXMubG9hZEFsbE5vZGVGb3JtcygpOwogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9LAogICAgYWN0aXZlSGlzdG9yeU5hbWVzOiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgLy8g5b2T5bGV5byA6IqC54K55pe277yM5Yqg6L295a+55bqU55qE6KGo5Y2V5pWw5o2uCiAgICAgICAgbmV3VmFsLmZvckVhY2gobmFtZSA9PiB7CiAgICAgICAgICBjb25zdCBpbmRleCA9IHBhcnNlSW50KG5hbWUucmVwbGFjZSgnaGlzdG9yeS0nLCAnJykpOwogICAgICAgICAgY29uc3QgcmVjb3JkID0gdGhpcy5mbG93UmVjb3JkTGlzdFtpbmRleF07CiAgICAgICAgICBpZiAocmVjb3JkICYmIHJlY29yZC50YXNrSWQgJiYgIXRoaXMubm9kZUZvcm1EYXRhW3JlY29yZC50YXNrSWRdICYmICF0aGlzLmxvYWRpbmdOb2Rlcy5oYXMocmVjb3JkLnRhc2tJZCkpIHsKICAgICAgICAgICAgdGhpcy5sb2FkTm9kZUZvcm0ocmVjb3JkLnRhc2tJZCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDojrflj5bljoblj7LoioLngrnlm77moIcgKi8KICAgIGdldEhpc3RvcnlJY29uKHJlY29yZCkgewogICAgICBpZiAocmVjb3JkLmZpbmlzaFRpbWUpIHsKICAgICAgICByZXR1cm4gJ2VsLWljb24tY2hlY2snOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnZWwtaWNvbi10aW1lJzsKICAgICAgfQogICAgfSwKCiAgICAvKiog6I635Y+W5Y6G5Y+y6IqC54K56aKc6ImyICovCiAgICBnZXRIaXN0b3J5Q29sb3IocmVjb3JkKSB7CiAgICAgIGlmIChyZWNvcmQuZmluaXNoVGltZSkgewogICAgICAgIHJldHVybiAnIzY3QzIzQSc7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICcjRTZBMjNDJzsKICAgICAgfQogICAgfSwKCiAgICAvKiog6I635Y+W54q25oCB5qCH562+57G75Z6LICovCiAgICBnZXRTdGF0dXNUYWdUeXBlKHJlY29yZCkgewogICAgICBpZiAocmVjb3JkLmZpbmlzaFRpbWUpIHsKICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnd2FybmluZyc7CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOiOt+WPlueKtuaAgeaWh+acrCAqLwogICAgZ2V0U3RhdHVzVGV4dChyZWNvcmQpIHsKICAgICAgaWYgKHJlY29yZC5maW5pc2hUaW1lKSB7CiAgICAgICAgcmV0dXJuICflt7LlrozmiJAnOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAn5aSE55CG5LitJzsKICAgICAgfQogICAgfSwKCiAgICAvKiog5Yqg6L295omA5pyJ6IqC54K555qE6KGo5Y2V5pWw5o2uICovCiAgICBsb2FkQWxsTm9kZUZvcm1zKCkgewogICAgICB0aGlzLmZsb3dSZWNvcmRMaXN0LmZvckVhY2gocmVjb3JkID0+IHsKICAgICAgICBpZiAocmVjb3JkLnRhc2tJZCAmJiAhdGhpcy5ub2RlRm9ybURhdGFbcmVjb3JkLnRhc2tJZF0gJiYgIXRoaXMubG9hZGluZ05vZGVzLmhhcyhyZWNvcmQudGFza0lkKSkgewogICAgICAgICAgdGhpcy5sb2FkTm9kZUZvcm0ocmVjb3JkLnRhc2tJZCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLyoqIOWKoOi9veWNleS4quiKgueCueeahOihqOWNleaVsOaNriAqLwogICAgbG9hZE5vZGVGb3JtKHRhc2tJZCkgewogICAgICBpZiAoIXRhc2tJZCB8fCB0aGlzLmxvYWRpbmdOb2Rlcy5oYXModGFza0lkKSkgewogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdGhpcy5sb2FkaW5nTm9kZXMuYWRkKHRhc2tJZCk7CgogICAgICBnZXRQcm9jZXNzVmFyaWFibGVzKHRhc2tJZCkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuZGF0YSkgewogICAgICAgICAgLy8g6K6+572u6KGo5Y2V5pWw5o2uCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5ub2RlRm9ybURhdGEsIHRhc2tJZCwgcmVzLmRhdGEpOwoKICAgICAgICAgIC8vIOWmguaenOacieWvueW6lOeahFZGb3Jt6YWN572u77yM5YiZ6K6+572uVkZvcm0KICAgICAgICAgIGNvbnN0IHJlY29yZCA9IHRoaXMuZmxvd1JlY29yZExpc3QuZmluZChyID0+IHIudGFza0lkID09PSB0YXNrSWQpOwogICAgICAgICAgaWYgKHJlY29yZCkgewogICAgICAgICAgICBjb25zdCB2Zm9ybUNvbmZpZyA9IHRoaXMuZ2V0Tm9kZVZGb3JtQ29uZmlnKHJlY29yZCk7CiAgICAgICAgICAgIGlmICh2Zm9ybUNvbmZpZyAmJiB2Zm9ybUNvbmZpZy5mb3JtSnNvbikgewogICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnZmb3JtS2V5cywgdGFza0lkLCBEYXRlLm5vdygpKTsKCiAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAgICAgY29uc3QgdmZvcm1SZWYgPSB0aGlzLiRyZWZzW2Bub2RlVkZvcm1fJHt0YXNrSWR9YF07CiAgICAgICAgICAgICAgICBpZiAodmZvcm1SZWYgJiYgdmZvcm1SZWZbMF0pIHsKICAgICAgICAgICAgICAgICAgLy8g6K6+572u6KGo5Y2VSlNPTgogICAgICAgICAgICAgICAgICB2Zm9ybVJlZlswXS5zZXRGb3JtSnNvbih2Zm9ybUNvbmZpZy5mb3JtSnNvbik7CiAgICAgICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICAvLyDorr7nva7ooajljZXmlbDmja4KICAgICAgICAgICAgICAgICAgICB2Zm9ybVJlZlswXS5zZXRGb3JtRGF0YShyZXMuZGF0YSk7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgLy8g56aB55So6KGo5Y2VCiAgICAgICAgICAgICAgICAgICAgICB2Zm9ybVJlZlswXS5kaXNhYmxlRm9ybSgpOwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIGNvbnNvbGUud2Fybihg5Yqg6L296IqC54K5ICR7dGFza0lkfSDnmoTooajljZXmlbDmja7lpLHotKU6YCwgZXJyb3IpOwogICAgICB9KS5maW5hbGx5KCgpID0+IHsKICAgICAgICB0aGlzLmxvYWRpbmdOb2Rlcy5kZWxldGUodGFza0lkKTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDojrflj5booajljZXmlbDmja7nlKjkuo7mmL7npLogKi8KICAgIGdldEZvcm1EYXRhRGlzcGxheSh0YXNrSWQpIHsKICAgICAgY29uc3Qgbm9kZURhdGEgPSB0aGlzLm5vZGVGb3JtRGF0YVt0YXNrSWRdOwogICAgICBpZiAoIW5vZGVEYXRhKSByZXR1cm4ge307CgogICAgICBjb25zb2xlLmxvZygn6IqC54K55pWw5o2uOicsIHRhc2tJZCwgbm9kZURhdGEpOyAvLyDosIPor5Xkv6Hmga8KCiAgICAgIC8vIOezu+e7n+Wtl+auteWIl+ihqAogICAgICBjb25zdCBzeXN0ZW1GaWVsZHMgPSBbJ2Zvcm1Kc29uJywgJ3Rhc2tJZCcsICdwcm9jSW5zSWQnLCAnZGVwbG95SWQnLCAncHJvY0RlZklkJywgJ2luc3RhbmNlSWQnXTsKCiAgICAgIGNvbnN0IGZpbHRlcmVkRGF0YSA9IHt9OwogICAgICBPYmplY3Qua2V5cyhub2RlRGF0YSkuZm9yRWFjaChrZXkgPT4gewogICAgICAgIGNvbnN0IHZhbHVlID0gbm9kZURhdGFba2V5XTsKCiAgICAgICAgLy8g6Lez6L+H57O757uf5a2X5q61CiAgICAgICAgaWYgKHN5c3RlbUZpZWxkcy5pbmNsdWRlcyhrZXkpKSB7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQoKICAgICAgICAvLyDljIXlkKvmiYDmnInpnZ7nqbrlgLzvvIzljIXmi6zmlbDlrZcw44CBZmFsc2XnrYnmnInmhI/kuYnnmoTlgLwKICAgICAgICBpZiAodmFsdWUgIT09IG51bGwgJiYgdmFsdWUgIT09IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPT0gJycpIHsKICAgICAgICAgIGNvbnN0IGxhYmVsID0gdGhpcy5nZXRGaWVsZExhYmVsKGtleSwgbm9kZURhdGEuZm9ybUpzb24pOwogICAgICAgICAgZmlsdGVyZWREYXRhW2xhYmVsXSA9IHZhbHVlOwogICAgICAgIH0KICAgICAgfSk7CgogICAgICBjb25zb2xlLmxvZygn6L+H5ruk5ZCO55qE5pWw5o2uOicsIGZpbHRlcmVkRGF0YSk7IC8vIOiwg+ivleS/oeaBrwogICAgICByZXR1cm4gZmlsdGVyZWREYXRhOwogICAgfSwKCiAgICAvKiog6I635Y+W5a2X5q615qCH562+ICovCiAgICBnZXRGaWVsZExhYmVsKGZpZWxkS2V5LCBmb3JtSnNvbikgewogICAgICBpZiAoIWZvcm1Kc29uKSB7CiAgICAgICAgcmV0dXJuIGZpZWxkS2V5OwogICAgICB9CgogICAgICAvLyDlsJ3or5XlpJrnp43lj6/og73nmoTooajljZXnu5PmnoQKICAgICAgbGV0IHdpZGdldHMgPSBbXTsKCiAgICAgIGlmIChmb3JtSnNvbi53aWRnZXRMaXN0KSB7CiAgICAgICAgd2lkZ2V0cyA9IGZvcm1Kc29uLndpZGdldExpc3Q7CiAgICAgIH0gZWxzZSBpZiAoZm9ybUpzb24uZm9ybUNvbmZpZyAmJiBmb3JtSnNvbi5mb3JtQ29uZmlnLndpZGdldExpc3QpIHsKICAgICAgICB3aWRnZXRzID0gZm9ybUpzb24uZm9ybUNvbmZpZy53aWRnZXRMaXN0OwogICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoZm9ybUpzb24pKSB7CiAgICAgICAgd2lkZ2V0cyA9IGZvcm1Kc29uOwogICAgICB9CgogICAgICBjb25zb2xlLmxvZygn5p+l5om+5a2X5q615qCH562+OicsIGZpZWxkS2V5LCAn5Zyod2lkZ2V0czonLCB3aWRnZXRzKTsgLy8g6LCD6K+V5L+h5oGvCgogICAgICAvLyDlnKjooajljZXnu4Tku7bkuK3mn6Xmib7lrZfmrrXmoIfnrb4KICAgICAgY29uc3Qgd2lkZ2V0ID0gd2lkZ2V0cy5maW5kKHcgPT4gewogICAgICAgIGlmICh3Lm9wdGlvbnMgJiYgdy5vcHRpb25zLm5hbWUgPT09IGZpZWxkS2V5KSByZXR1cm4gdHJ1ZTsKICAgICAgICBpZiAody5fX2NvbmZpZ19fICYmIHcuX19jb25maWdfXy5mb3JtSWQgPT09IGZpZWxkS2V5KSByZXR1cm4gdHJ1ZTsKICAgICAgICBpZiAody5fX3ZNb2RlbF9fID09PSBmaWVsZEtleSkgcmV0dXJuIHRydWU7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9KTsKCiAgICAgIGlmICh3aWRnZXQpIHsKICAgICAgICAvLyDlsJ3or5XlpJrnp43lj6/og73nmoTmoIfnrb7lrZfmrrUKICAgICAgICBjb25zdCBsYWJlbCA9IHdpZGdldC5vcHRpb25zPy5sYWJlbCB8fAogICAgICAgICAgICAgICAgICAgICB3aWRnZXQuX19jb25maWdfXz8ubGFiZWwgfHwKICAgICAgICAgICAgICAgICAgICAgd2lkZ2V0LmxhYmVsIHx8CiAgICAgICAgICAgICAgICAgICAgIGZpZWxkS2V5OwogICAgICAgIGNvbnNvbGUubG9nKCfmib7liLDmoIfnrb46JywgZmllbGRLZXksICctPicsIGxhYmVsKTsgLy8g6LCD6K+V5L+h5oGvCiAgICAgICAgcmV0dXJuIGxhYmVsOwogICAgICB9CgogICAgICByZXR1cm4gZmllbGRLZXk7CiAgICB9LAoKICAgIC8qKiDmoLzlvI/ljJblrZfmrrXlgLwgKi8KICAgIGZvcm1hdEZpZWxkVmFsdWUodmFsdWUpIHsKICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7CiAgICAgICAgcmV0dXJuIHZhbHVlLmpvaW4oJywgJyk7CiAgICAgIH0KICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcpIHsKICAgICAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkodmFsdWUpOwogICAgICB9CiAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdib29sZWFuJykgewogICAgICAgIHJldHVybiB2YWx1ZSA/ICfmmK8nIDogJ+WQpic7CiAgICAgIH0KICAgICAgcmV0dXJuIFN0cmluZyh2YWx1ZSk7CiAgICB9LAoKICAgIC8qKiDojrflj5blrZfmrrXot6jluqYgKi8KICAgIGdldEZpZWxkU3Bhbih2YWx1ZSkgewogICAgICBjb25zdCB2YWx1ZVN0ciA9IHRoaXMuZm9ybWF0RmllbGRWYWx1ZSh2YWx1ZSk7CiAgICAgIC8vIOmVv+aWh+acrOWNoOeUqOS4pOWIlwogICAgICByZXR1cm4gdmFsdWVTdHIubGVuZ3RoID4gMjAgPyAyIDogMTsKICAgIH0sCgogICAgLyoqIOiOt+WPluWtl+auteagt+W8j+exuyAqLwogICAgZ2V0RmllbGRDbGFzcyh2YWx1ZSkgewogICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnYm9vbGVhbicpIHsKICAgICAgICByZXR1cm4gdmFsdWUgPyAnZmllbGQtYm9vbGVhbi10cnVlJyA6ICdmaWVsZC1ib29sZWFuLWZhbHNlJzsKICAgICAgfQogICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJykgewogICAgICAgIHJldHVybiAnZmllbGQtbnVtYmVyJzsKICAgICAgfQogICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHsKICAgICAgICByZXR1cm4gJ2ZpZWxkLWFycmF5JzsKICAgICAgfQogICAgICByZXR1cm4gJ2ZpZWxkLXRleHQnOwogICAgfSwKCiAgICAvKiog6I635Y+W6IqC54K5VkZvcm3phY3nva4gKi8KICAgIGdldE5vZGVWRm9ybUNvbmZpZyhyZWNvcmQpIHsKICAgICAgLy8g5LuO5pys5Zyw5a2Y5YKo5Yqg6L29VkZvcm3phY3nva4KICAgICAgaWYgKHRoaXMubm9kZVZGb3JtQ29uZmlncy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLmxvYWROb2RlVkZvcm1Db25maWdzKCk7CiAgICAgIH0KCiAgICAgIC8vIOagueaNruiKgueCueagh+ivhuaIluS7u+WKoeWQjeensOafpeaJvuWvueW6lOeahFZGb3Jt6YWN572uCiAgICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMubm9kZVZGb3JtQ29uZmlncy5maW5kKGNvbmZpZyA9PgogICAgICAgIGNvbmZpZy5ub2RlS2V5ID09PSByZWNvcmQudGFza0RlZktleSB8fAogICAgICAgIGNvbmZpZy5ub2RlTmFtZSA9PT0gcmVjb3JkLnRhc2tOYW1lIHx8CiAgICAgICAgdGhpcy5tYXRjaE5vZGVCeVR5cGUoY29uZmlnLCByZWNvcmQpCiAgICAgICk7CgogICAgICByZXR1cm4gY29uZmlnOwogICAgfSwKCiAgICAvKiog5qC55o2u57G75Z6L5Yy56YWN6IqC54K5ICovCiAgICBtYXRjaE5vZGVCeVR5cGUoY29uZmlnLCByZWNvcmQpIHsKICAgICAgY29uc3QgdGFza05hbWUgPSByZWNvcmQudGFza05hbWUgfHwgJyc7CiAgICAgIGNvbnN0IG5vZGVUeXBlTWFwID0gewogICAgICAgICducGlfYXBwbHknOiBbJ+eUs+ivtycsICdOUEnnlLPor7cnXSwKICAgICAgICAndGVjaF9yZXZpZXcnOiBbJ+aKgOacr+ivhOWuoScsICfmioDmnK/lrqHmoLgnXSwKICAgICAgICAncHJvY2Vzc19yZXZpZXcnOiBbJ+W3peiJuuivhOWuoScsICflt6XoibrlrqHmoLgnXSwKICAgICAgICAncXVhbGl0eV9yZXZpZXcnOiBbJ+i0qOmHj+ivhOWuoScsICfotKjph4/lrqHmoLgnXSwKICAgICAgICAnY29zdF9yZXZpZXcnOiBbJ+aIkOacrOivhOWuoScsICfmiJDmnKzlrqHmoLgnXSwKICAgICAgICAnZmluYWxfYXBwcm92YWwnOiBbJ+acgOe7iOWuoeaJuScsICfnu4jlrqEnXQogICAgICB9OwoKICAgICAgY29uc3Qga2V5d29yZHMgPSBub2RlVHlwZU1hcFtjb25maWcubm9kZVR5cGVdIHx8IFtdOwogICAgICByZXR1cm4ga2V5d29yZHMuc29tZShrZXl3b3JkID0+IHRhc2tOYW1lLmluY2x1ZGVzKGtleXdvcmQpKTsKICAgIH0sCgogICAgLyoqIOWKoOi9veiKgueCuVZGb3Jt6YWN572uICovCiAgICBsb2FkTm9kZVZGb3JtQ29uZmlncygpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyDliqDovb1OUEnmtYHnqIvnmoRWRm9ybemFjee9rgogICAgICAgIGNvbnN0IHNhdmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ25vZGVfdmZvcm1fY29uZmlnX25waV9wcm9jZXNzJyk7CiAgICAgICAgaWYgKHNhdmVkKSB7CiAgICAgICAgICBjb25zdCBjb25maWcgPSBKU09OLnBhcnNlKHNhdmVkKTsKICAgICAgICAgIHRoaXMubm9kZVZGb3JtQ29uZmlncyA9IGNvbmZpZy5ub2RlRm9ybXMgfHwgW107CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUud2Fybign5Yqg6L296IqC54K5VkZvcm3phY3nva7lpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMubm9kZVZGb3JtQ29uZmlncyA9IFtdOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDliIfmjaLoioLngrnooajljZXmmL7npLrmqKHlvI8gKi8KICAgIHRvZ2dsZU5vZGVGb3JtTW9kZSh0YXNrSWQpIHsKICAgICAgY29uc3QgY3VycmVudE1vZGUgPSB0aGlzLm5vZGVGb3JtTW9kZXNbdGFza0lkXSB8fCAnZm9ybSc7CiAgICAgIHRoaXMuJHNldCh0aGlzLm5vZGVGb3JtTW9kZXMsIHRhc2tJZCwgY3VycmVudE1vZGUgPT09ICdmb3JtJyA/ICdkYXRhJyA6ICdmb3JtJyk7CiAgICB9LAoKICAgIC8qKiDojrflj5boioLngrnooajljZXlrZfmrrUgKi8KICAgIGdldE5vZGVGb3JtRmllbGRzKHJlY29yZCkgewogICAgICBjb25zdCB2Zm9ybUNvbmZpZyA9IHRoaXMuZ2V0Tm9kZVZGb3JtQ29uZmlnKHJlY29yZCk7CiAgICAgIGlmICghdmZvcm1Db25maWcgfHwgIXZmb3JtQ29uZmlnLmZvcm1Kc29uIHx8ICF2Zm9ybUNvbmZpZy5mb3JtSnNvbi53aWRnZXRMaXN0KSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CgogICAgICByZXR1cm4gdmZvcm1Db25maWcuZm9ybUpzb24ud2lkZ2V0TGlzdC5tYXAod2lkZ2V0ID0+ICh7CiAgICAgICAgbmFtZTogd2lkZ2V0Lm9wdGlvbnM/Lm5hbWUgfHwgd2lkZ2V0Lm5hbWUsCiAgICAgICAgbGFiZWw6IHdpZGdldC5vcHRpb25zPy5sYWJlbCB8fCB3aWRnZXQubGFiZWwgfHwgd2lkZ2V0Lm9wdGlvbnM/Lm5hbWUsCiAgICAgICAgdHlwZTogd2lkZ2V0LnR5cGUsCiAgICAgICAgb3B0aW9uczogd2lkZ2V0Lm9wdGlvbnMKICAgICAgfSkpOwogICAgfSwKCiAgICAvKiog6I635Y+W5a2X5q615YC8ICovCiAgICBnZXRGaWVsZFZhbHVlKHRhc2tJZCwgZmllbGROYW1lKSB7CiAgICAgIGNvbnN0IG5vZGVEYXRhID0gdGhpcy5ub2RlRm9ybURhdGFbdGFza0lkXTsKICAgICAgaWYgKCFub2RlRGF0YSkgcmV0dXJuIG51bGw7CiAgICAgIHJldHVybiBub2RlRGF0YVtmaWVsZE5hbWVdOwogICAgfSwKCiAgICAvKiog6I635Y+W5a2X5q6157uE5Lu2ICovCiAgICBnZXRGaWVsZENvbXBvbmVudChmaWVsZFR5cGUpIHsKICAgICAgcmV0dXJuICdGaWVsZFJlbmRlcmVyJzsKICAgIH0sCgogICAgLyoqIOiOt+WPluiKgueCueebuOWFs+aVsOaNriAqLwogICAgZ2V0Tm9kZVJlbGF0ZWREYXRhKHJlY29yZCkgewogICAgICBjb25zdCBub2RlRGF0YSA9IHRoaXMubm9kZUZvcm1EYXRhW3JlY29yZC50YXNrSWRdOwogICAgICBpZiAoIW5vZGVEYXRhKSByZXR1cm4ge307CgogICAgICBjb25zdCB2Zm9ybUNvbmZpZyA9IHRoaXMuZ2V0Tm9kZVZGb3JtQ29uZmlnKHJlY29yZCk7CiAgICAgIGlmICghdmZvcm1Db25maWcgfHwgIXZmb3JtQ29uZmlnLmZvcm1Kc29uIHx8ICF2Zm9ybUNvbmZpZy5mb3JtSnNvbi53aWRnZXRMaXN0KSB7CiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ6KGo5Y2V6YWN572u77yM6L+U5Zue5omA5pyJ5pWw5o2uCiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0Rm9ybURhdGFEaXNwbGF5KHJlY29yZC50YXNrSWQpOwogICAgICB9CgogICAgICAvLyDlj6rov5Tlm57lvZPliY3oioLngrnooajljZXkuK3lrprkuYnnmoTlrZfmrrXmlbDmja4KICAgICAgY29uc3Qgbm9kZUZpZWxkcyA9IHZmb3JtQ29uZmlnLmZvcm1Kc29uLndpZGdldExpc3QubWFwKHcgPT4gdy5vcHRpb25zPy5uYW1lIHx8IHcubmFtZSk7CiAgICAgIGNvbnN0IGZpbHRlcmVkRGF0YSA9IHt9OwoKICAgICAgbm9kZUZpZWxkcy5mb3JFYWNoKGZpZWxkTmFtZSA9PiB7CiAgICAgICAgaWYgKG5vZGVEYXRhLmhhc093blByb3BlcnR5KGZpZWxkTmFtZSkgJiYKICAgICAgICAgICAgbm9kZURhdGFbZmllbGROYW1lXSAhPT0gbnVsbCAmJgogICAgICAgICAgICBub2RlRGF0YVtmaWVsZE5hbWVdICE9PSB1bmRlZmluZWQgJiYKICAgICAgICAgICAgbm9kZURhdGFbZmllbGROYW1lXSAhPT0gJycpIHsKICAgICAgICAgIGNvbnN0IHdpZGdldCA9IHZmb3JtQ29uZmlnLmZvcm1Kc29uLndpZGdldExpc3QuZmluZCh3ID0+CiAgICAgICAgICAgICh3Lm9wdGlvbnM/Lm5hbWUgfHwgdy5uYW1lKSA9PT0gZmllbGROYW1lCiAgICAgICAgICApOwogICAgICAgICAgY29uc3QgbGFiZWwgPSB3aWRnZXQ/Lm9wdGlvbnM/LmxhYmVsIHx8IHdpZGdldD8ubGFiZWwgfHwgZmllbGROYW1lOwogICAgICAgICAgZmlsdGVyZWREYXRhW2xhYmVsXSA9IG5vZGVEYXRhW2ZpZWxkTmFtZV07CiAgICAgICAgfQogICAgICB9KTsKCiAgICAgIHJldHVybiBmaWx0ZXJlZERhdGE7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+IA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FlowHistory", "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <!-- 节点专属表单显示 -->\n          <div v-if=\"getNodeVFormConfig(record)\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> {{ record.taskName }} - 专属表单\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"toggleNodeFormMode(record.taskId)\"\n                style=\"float: right; margin-top: -2px;\"\n              >\n                {{ nodeFormModes[record.taskId] === 'form' ? '切换到数据视图' : '切换到表单视图' }}\n              </el-button>\n            </h5>\n            <div class=\"form-display-container\">\n              <!-- 表单视图：只显示当前节点的字段 -->\n              <div v-if=\"nodeFormModes[record.taskId] === 'form'\" class=\"node-form-view\">\n                <div class=\"form-fields\">\n                  <div\n                    v-for=\"field in getNodeFormFields(record)\"\n                    :key=\"field.name\"\n                    class=\"form-field-item\"\n                  >\n                    <div class=\"field-label\">{{ field.label }}</div>\n                    <div class=\"field-content\">\n                      <!-- 根据字段类型渲染不同的组件 -->\n                      <component\n                        :is=\"getFieldComponent(field.type)\"\n                        :value=\"getFieldValue(record.taskId, field.name)\"\n                        :field=\"field\"\n                        :disabled=\"true\"\n                      />\n                    </div>\n                  </div>\n                </div>\n                <div v-if=\"getNodeFormFields(record).length === 0\" class=\"no-fields\">\n                  该节点暂无专属表单字段\n                </div>\n              </div>\n\n              <!-- 数据视图：显示该节点相关的所有数据 -->\n              <div v-else class=\"node-data-view\">\n                <el-descriptions :column=\"2\" size=\"small\" border class=\"form-data-descriptions\">\n                  <el-descriptions-item\n                    v-for=\"(value, key) in getNodeRelatedData(record)\"\n                    :key=\"key\"\n                    :label=\"key\"\n                    :span=\"getFieldSpan(value)\"\n                  >\n                    <div class=\"form-field-value\" :class=\"getFieldClass(value)\">\n                      {{ formatFieldValue(value) }}\n                    </div>\n                  </el-descriptions-item>\n                </el-descriptions>\n                <div v-if=\"Object.keys(getNodeRelatedData(record)).length === 0\" class=\"no-data\">\n                  暂无相关数据\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 通用数据视图（当没有专属表单时） -->\n          <div v-else-if=\"record.taskId && nodeFormData[record.taskId]\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> {{ record.taskName }} - 数据记录\n            </h5>\n            <div class=\"form-data-container\">\n              <el-descriptions :column=\"2\" size=\"small\" border class=\"form-data-descriptions\">\n                <el-descriptions-item\n                  v-for=\"(value, key) in getFormDataDisplay(record.taskId)\"\n                  :key=\"key\"\n                  :label=\"key\"\n                  :span=\"getFieldSpan(value)\"\n                >\n                  <div class=\"form-field-value\" :class=\"getFieldClass(value)\">\n                    {{ formatFieldValue(value) }}\n                  </div>\n                </el-descriptions-item>\n              </el-descriptions>\n              <div v-if=\"Object.keys(getFormDataDisplay(record.taskId)).length === 0\" class=\"no-data\">\n                暂无表单数据\n              </div>\n            </div>\n          </div>\n\n          <!-- 办理信息 -->\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nimport { getProcessVariables } from '@/api/flowable/definition'\nimport FieldRenderer from '@/components/FieldRenderer'\n\nexport default {\n  name: 'FlowHistory',\n  components: {\n    FieldRenderer\n  },\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: [],\n      nodeFormData: {}, // 存储每个节点的表单数据\n      loadingNodes: new Set(), // 正在加载的节点\n      nodeVFormConfigs: [], // 节点VForm配置\n      nodeFormModes: {} // 节点表单显示模式：form(表单视图) 或 data(数据视图)\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0) {\n          if (this.defaultExpanded) {\n            // 默认展开第一个节点\n            this.activeHistoryNames = ['history-0'];\n          }\n          // 加载所有节点的表单数据\n          this.loadAllNodeForms();\n        }\n      },\n      immediate: true\n    },\n    activeHistoryNames: {\n      handler(newVal) {\n        // 当展开节点时，加载对应的表单数据\n        newVal.forEach(name => {\n          const index = parseInt(name.replace('history-', ''));\n          const record = this.flowRecordList[index];\n          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n            this.loadNodeForm(record.taskId);\n          }\n        });\n      }\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    },\n\n    /** 加载所有节点的表单数据 */\n    loadAllNodeForms() {\n      this.flowRecordList.forEach(record => {\n        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n          this.loadNodeForm(record.taskId);\n        }\n      });\n    },\n\n    /** 加载单个节点的表单数据 */\n    loadNodeForm(taskId) {\n      if (!taskId || this.loadingNodes.has(taskId)) {\n        return;\n      }\n\n      this.loadingNodes.add(taskId);\n\n      getProcessVariables(taskId).then(res => {\n        if (res.data) {\n          // 设置表单数据\n          this.$set(this.nodeFormData, taskId, res.data);\n\n          // 如果有对应的VForm配置，则设置VForm\n          const record = this.flowRecordList.find(r => r.taskId === taskId);\n          if (record) {\n            const vformConfig = this.getNodeVFormConfig(record);\n            if (vformConfig && vformConfig.formJson) {\n              this.$set(this.vformKeys, taskId, Date.now());\n\n              this.$nextTick(() => {\n                const vformRef = this.$refs[`nodeVForm_${taskId}`];\n                if (vformRef && vformRef[0]) {\n                  // 设置表单JSON\n                  vformRef[0].setFormJson(vformConfig.formJson);\n                  this.$nextTick(() => {\n                    // 设置表单数据\n                    vformRef[0].setFormData(res.data);\n                    this.$nextTick(() => {\n                      // 禁用表单\n                      vformRef[0].disableForm();\n                    });\n                  });\n                }\n              });\n            }\n          }\n        }\n      }).catch(error => {\n        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);\n      }).finally(() => {\n        this.loadingNodes.delete(taskId);\n      });\n    },\n\n    /** 获取表单数据用于显示 */\n    getFormDataDisplay(taskId) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return {};\n\n      console.log('节点数据:', taskId, nodeData); // 调试信息\n\n      // 系统字段列表\n      const systemFields = ['formJson', 'taskId', 'procInsId', 'deployId', 'procDefId', 'instanceId'];\n\n      const filteredData = {};\n      Object.keys(nodeData).forEach(key => {\n        const value = nodeData[key];\n\n        // 跳过系统字段\n        if (systemFields.includes(key)) {\n          return;\n        }\n\n        // 包含所有非空值，包括数字0、false等有意义的值\n        if (value !== null && value !== undefined && value !== '') {\n          const label = this.getFieldLabel(key, nodeData.formJson);\n          filteredData[label] = value;\n        }\n      });\n\n      console.log('过滤后的数据:', filteredData); // 调试信息\n      return filteredData;\n    },\n\n    /** 获取字段标签 */\n    getFieldLabel(fieldKey, formJson) {\n      if (!formJson) {\n        return fieldKey;\n      }\n\n      // 尝试多种可能的表单结构\n      let widgets = [];\n\n      if (formJson.widgetList) {\n        widgets = formJson.widgetList;\n      } else if (formJson.formConfig && formJson.formConfig.widgetList) {\n        widgets = formJson.formConfig.widgetList;\n      } else if (Array.isArray(formJson)) {\n        widgets = formJson;\n      }\n\n      console.log('查找字段标签:', fieldKey, '在widgets:', widgets); // 调试信息\n\n      // 在表单组件中查找字段标签\n      const widget = widgets.find(w => {\n        if (w.options && w.options.name === fieldKey) return true;\n        if (w.__config__ && w.__config__.formId === fieldKey) return true;\n        if (w.__vModel__ === fieldKey) return true;\n        return false;\n      });\n\n      if (widget) {\n        // 尝试多种可能的标签字段\n        const label = widget.options?.label ||\n                     widget.__config__?.label ||\n                     widget.label ||\n                     fieldKey;\n        console.log('找到标签:', fieldKey, '->', label); // 调试信息\n        return label;\n      }\n\n      return fieldKey;\n    },\n\n    /** 格式化字段值 */\n    formatFieldValue(value) {\n      if (Array.isArray(value)) {\n        return value.join(', ');\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      if (typeof value === 'boolean') {\n        return value ? '是' : '否';\n      }\n      return String(value);\n    },\n\n    /** 获取字段跨度 */\n    getFieldSpan(value) {\n      const valueStr = this.formatFieldValue(value);\n      // 长文本占用两列\n      return valueStr.length > 20 ? 2 : 1;\n    },\n\n    /** 获取字段样式类 */\n    getFieldClass(value) {\n      if (typeof value === 'boolean') {\n        return value ? 'field-boolean-true' : 'field-boolean-false';\n      }\n      if (typeof value === 'number') {\n        return 'field-number';\n      }\n      if (Array.isArray(value)) {\n        return 'field-array';\n      }\n      return 'field-text';\n    },\n\n    /** 获取节点VForm配置 */\n    getNodeVFormConfig(record) {\n      // 从本地存储加载VForm配置\n      if (this.nodeVFormConfigs.length === 0) {\n        this.loadNodeVFormConfigs();\n      }\n\n      // 根据节点标识或任务名称查找对应的VForm配置\n      const config = this.nodeVFormConfigs.find(config =>\n        config.nodeKey === record.taskDefKey ||\n        config.nodeName === record.taskName ||\n        this.matchNodeByType(config, record)\n      );\n\n      return config;\n    },\n\n    /** 根据类型匹配节点 */\n    matchNodeByType(config, record) {\n      const taskName = record.taskName || '';\n      const nodeTypeMap = {\n        'npi_apply': ['申请', 'NPI申请'],\n        'tech_review': ['技术评审', '技术审核'],\n        'process_review': ['工艺评审', '工艺审核'],\n        'quality_review': ['质量评审', '质量审核'],\n        'cost_review': ['成本评审', '成本审核'],\n        'final_approval': ['最终审批', '终审']\n      };\n\n      const keywords = nodeTypeMap[config.nodeType] || [];\n      return keywords.some(keyword => taskName.includes(keyword));\n    },\n\n    /** 加载节点VForm配置 */\n    loadNodeVFormConfigs() {\n      try {\n        // 加载NPI流程的VForm配置\n        const saved = localStorage.getItem('node_vform_config_npi_process');\n        if (saved) {\n          const config = JSON.parse(saved);\n          this.nodeVFormConfigs = config.nodeForms || [];\n        }\n      } catch (error) {\n        console.warn('加载节点VForm配置失败:', error);\n        this.nodeVFormConfigs = [];\n      }\n    },\n\n    /** 切换节点表单显示模式 */\n    toggleNodeFormMode(taskId) {\n      const currentMode = this.nodeFormModes[taskId] || 'form';\n      this.$set(this.nodeFormModes, taskId, currentMode === 'form' ? 'data' : 'form');\n    },\n\n    /** 获取节点表单字段 */\n    getNodeFormFields(record) {\n      const vformConfig = this.getNodeVFormConfig(record);\n      if (!vformConfig || !vformConfig.formJson || !vformConfig.formJson.widgetList) {\n        return [];\n      }\n\n      return vformConfig.formJson.widgetList.map(widget => ({\n        name: widget.options?.name || widget.name,\n        label: widget.options?.label || widget.label || widget.options?.name,\n        type: widget.type,\n        options: widget.options\n      }));\n    },\n\n    /** 获取字段值 */\n    getFieldValue(taskId, fieldName) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return null;\n      return nodeData[fieldName];\n    },\n\n    /** 获取字段组件 */\n    getFieldComponent(fieldType) {\n      return 'FieldRenderer';\n    },\n\n    /** 获取节点相关数据 */\n    getNodeRelatedData(record) {\n      const nodeData = this.nodeFormData[record.taskId];\n      if (!nodeData) return {};\n\n      const vformConfig = this.getNodeVFormConfig(record);\n      if (!vformConfig || !vformConfig.formJson || !vformConfig.formJson.widgetList) {\n        // 如果没有表单配置，返回所有数据\n        return this.getFormDataDisplay(record.taskId);\n      }\n\n      // 只返回当前节点表单中定义的字段数据\n      const nodeFields = vformConfig.formJson.widgetList.map(w => w.options?.name || w.name);\n      const filteredData = {};\n\n      nodeFields.forEach(fieldName => {\n        if (nodeData.hasOwnProperty(fieldName) &&\n            nodeData[fieldName] !== null &&\n            nodeData[fieldName] !== undefined &&\n            nodeData[fieldName] !== '') {\n          const widget = vformConfig.formJson.widgetList.find(w =>\n            (w.options?.name || w.name) === fieldName\n          );\n          const label = widget?.options?.label || widget?.label || fieldName;\n          filteredData[label] = nodeData[fieldName];\n        }\n      });\n\n      return filteredData;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n\n/* 节点表单样式 */\n.node-form-section {\n  margin-bottom: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-section-title {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #606266;\n  border-bottom: 1px solid #E4E7ED;\n}\n\n.form-section-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n/* 表单显示容器样式 */\n.form-display-container {\n  background-color: white;\n}\n\n/* 节点表单视图样式 */\n.node-form-view {\n  padding: 16px;\n}\n\n.form-fields {\n  .form-field-item {\n    margin-bottom: 20px;\n    display: flex;\n    align-items: flex-start;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .field-label {\n    width: 120px;\n    min-width: 120px;\n    padding: 8px 12px 8px 0;\n    color: #606266;\n    font-weight: 500;\n    text-align: right;\n    line-height: 32px;\n  }\n\n  .field-content {\n    flex: 1;\n    padding: 4px 0;\n  }\n}\n\n.no-fields {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n  font-style: italic;\n}\n\n/* 节点数据视图样式 */\n.node-data-view {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n/* 通用数据容器样式 */\n.form-data-container {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n.form-data-descriptions {\n  background-color: white;\n}\n\n/* 表单字段值样式 */\n.form-field-value {\n  word-break: break-word;\n  line-height: 1.5;\n}\n\n.field-boolean-true {\n  color: #67C23A;\n  font-weight: 600;\n}\n\n.field-boolean-false {\n  color: #F56C6C;\n  font-weight: 600;\n}\n\n.field-number {\n  color: #E6A23C;\n  font-weight: 500;\n}\n\n.field-array {\n  color: #909399;\n  font-style: italic;\n}\n\n.field-text {\n  color: #606266;\n}\n\n/* 原始数据显示样式 */\n.raw-data-container {\n  background-color: #f8f8f8;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 12px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.raw-data-container pre {\n  margin: 0;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  color: #333;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.no-data {\n  text-align: center;\n  color: #909399;\n  padding: 20px;\n  font-style: italic;\n}\n</style>\n"]}]}