{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752410174898}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FlowHistory", "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <!-- 节点表单 -->\n          <div v-if=\"getNodeFormConfig(record)\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> {{ record.taskName }} - 表单\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"toggleFormMode(record.taskId)\"\n                style=\"float: right; margin-top: -2px;\"\n              >\n                {{ formModes[record.taskId] === 'design' ? '切换到数据视图' : '切换到表单视图' }}\n              </el-button>\n            </h5>\n            <div class=\"form-container\">\n              <!-- 表单设计视图 -->\n              <div v-if=\"formModes[record.taskId] === 'design'\">\n                <node-form\n                  :value=\"getNodeFormConfig(record)\"\n                  :title=\"record.taskName\"\n                  :readonly=\"true\"\n                />\n              </div>\n\n              <!-- 数据视图 -->\n              <div v-else>\n                <el-descriptions :column=\"2\" size=\"small\" border class=\"form-data-descriptions\">\n                  <el-descriptions-item\n                    v-for=\"(value, key) in getFormDataDisplay(record.taskId)\"\n                    :key=\"key\"\n                    :label=\"key\"\n                    :span=\"getFieldSpan(value)\"\n                  >\n                    <div class=\"form-field-value\" :class=\"getFieldClass(value)\">\n                      {{ formatFieldValue(value) }}\n                    </div>\n                  </el-descriptions-item>\n                </el-descriptions>\n                <div v-if=\"Object.keys(getFormDataDisplay(record.taskId)).length === 0\" class=\"no-data\">\n                  暂无表单数据\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 办理信息 -->\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nimport { getProcessVariables } from '@/api/flowable/definition'\nimport NodeForm from '@/components/NodeForm'\n\nexport default {\n  name: 'FlowHistory',\n  components: {\n    NodeForm\n  },\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: [],\n      nodeFormData: {}, // 存储每个节点的表单数据\n      formKeys: {}, // 存储每个表单的key，用于强制重新渲染\n      loadingNodes: new Set(), // 正在加载的节点\n      formModes: {}, // 控制表单显示模式：design(表单视图) 或 data(数据视图)\n      nodeFormConfigs: [] // 节点表单配置\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0) {\n          if (this.defaultExpanded) {\n            // 默认展开第一个节点\n            this.activeHistoryNames = ['history-0'];\n          }\n          // 加载所有节点的表单数据\n          this.loadAllNodeForms();\n        }\n      },\n      immediate: true\n    },\n    activeHistoryNames: {\n      handler(newVal) {\n        // 当展开节点时，加载对应的表单数据\n        newVal.forEach(name => {\n          const index = parseInt(name.replace('history-', ''));\n          const record = this.flowRecordList[index];\n          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n            this.loadNodeForm(record.taskId);\n          }\n        });\n      }\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    },\n\n    /** 加载所有节点的表单数据 */\n    loadAllNodeForms() {\n      this.flowRecordList.forEach(record => {\n        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n          this.loadNodeForm(record.taskId);\n        }\n      });\n    },\n\n    /** 加载单个节点的表单数据 */\n    loadNodeForm(taskId) {\n      if (!taskId || this.loadingNodes.has(taskId)) {\n        return;\n      }\n\n      this.loadingNodes.add(taskId);\n\n      getProcessVariables(taskId).then(res => {\n        if (res.data) {\n          // 设置表单数据\n          this.$set(this.nodeFormData, taskId, res.data);\n        }\n      }).catch(error => {\n        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);\n      }).finally(() => {\n        this.loadingNodes.delete(taskId);\n      });\n    },\n\n    /** 获取表单数据用于显示 */\n    getFormDataDisplay(taskId) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return {};\n\n      console.log('节点数据:', taskId, nodeData); // 调试信息\n\n      // 系统字段列表\n      const systemFields = ['formJson', 'taskId', 'procInsId', 'deployId', 'procDefId', 'instanceId'];\n\n      const filteredData = {};\n      Object.keys(nodeData).forEach(key => {\n        const value = nodeData[key];\n\n        // 跳过系统字段\n        if (systemFields.includes(key)) {\n          return;\n        }\n\n        // 包含所有非空值，包括数字0、false等有意义的值\n        if (value !== null && value !== undefined && value !== '') {\n          const label = this.getFieldLabel(key, nodeData.formJson);\n          filteredData[label] = value;\n        }\n      });\n\n      console.log('过滤后的数据:', filteredData); // 调试信息\n      return filteredData;\n    },\n\n    /** 获取字段标签 */\n    getFieldLabel(fieldKey, formJson) {\n      if (!formJson) {\n        return fieldKey;\n      }\n\n      // 尝试多种可能的表单结构\n      let widgets = [];\n\n      if (formJson.widgetList) {\n        widgets = formJson.widgetList;\n      } else if (formJson.formConfig && formJson.formConfig.widgetList) {\n        widgets = formJson.formConfig.widgetList;\n      } else if (Array.isArray(formJson)) {\n        widgets = formJson;\n      }\n\n      console.log('查找字段标签:', fieldKey, '在widgets:', widgets); // 调试信息\n\n      // 在表单组件中查找字段标签\n      const widget = widgets.find(w => {\n        if (w.options && w.options.name === fieldKey) return true;\n        if (w.__config__ && w.__config__.formId === fieldKey) return true;\n        if (w.__vModel__ === fieldKey) return true;\n        return false;\n      });\n\n      if (widget) {\n        // 尝试多种可能的标签字段\n        const label = widget.options?.label ||\n                     widget.__config__?.label ||\n                     widget.label ||\n                     fieldKey;\n        console.log('找到标签:', fieldKey, '->', label); // 调试信息\n        return label;\n      }\n\n      return fieldKey;\n    },\n\n    /** 格式化字段值 */\n    formatFieldValue(value) {\n      if (Array.isArray(value)) {\n        return value.join(', ');\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      if (typeof value === 'boolean') {\n        return value ? '是' : '否';\n      }\n      return String(value);\n    },\n\n    /** 获取字段跨度 */\n    getFieldSpan(value) {\n      const valueStr = this.formatFieldValue(value);\n      // 长文本占用两列\n      return valueStr.length > 20 ? 2 : 1;\n    },\n\n    /** 获取字段样式类 */\n    getFieldClass(value) {\n      if (typeof value === 'boolean') {\n        return value ? 'field-boolean-true' : 'field-boolean-false';\n      }\n      if (typeof value === 'number') {\n        return 'field-number';\n      }\n      if (Array.isArray(value)) {\n        return 'field-array';\n      }\n      return 'field-text';\n    },\n\n    /** 切换表单显示模式 */\n    toggleFormMode(taskId) {\n      const currentMode = this.formModes[taskId] || 'design';\n      this.$set(this.formModes, taskId, currentMode === 'design' ? 'data' : 'design');\n    },\n\n    /** 获取节点表单配置 */\n    getNodeFormConfig(record) {\n      // 从本地存储加载表单配置\n      if (this.nodeFormConfigs.length === 0) {\n        this.loadNodeFormConfigs();\n      }\n\n      // 根据节点名称或任务名称查找对应的表单配置\n      const config = this.nodeFormConfigs.find(config =>\n        config.nodeName === record.taskName ||\n        config.nodeType === this.getNodeTypeFromRecord(record)\n      );\n\n      return config ? config.fields : null;\n    },\n\n    /** 从记录中推断节点类型 */\n    getNodeTypeFromRecord(record) {\n      // 根据任务名称推断节点类型\n      const taskName = record.taskName || '';\n      if (taskName.includes('开始') || taskName.includes('申请')) {\n        return 'start';\n      } else if (taskName.includes('审批') || taskName.includes('审核')) {\n        return 'approval';\n      } else if (taskName.includes('结束')) {\n        return 'end';\n      }\n      return 'userTask';\n    },\n\n    /** 加载节点表单配置 */\n    loadNodeFormConfigs() {\n      try {\n        const saved = localStorage.getItem('flowable_form_config');\n        if (saved) {\n          const config = JSON.parse(saved);\n          this.nodeFormConfigs = config.nodeForms || [];\n        }\n      } catch (error) {\n        console.warn('加载节点表单配置失败:', error);\n        this.nodeFormConfigs = [];\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n\n/* 节点表单数据样式 */\n.node-form-section {\n  margin-bottom: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-section-title {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #606266;\n  border-bottom: 1px solid #E4E7ED;\n}\n\n.form-section-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n.form-data-container {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n.form-data-descriptions {\n  background-color: white;\n}\n\n/* 表单字段值样式 */\n.form-field-value {\n  word-break: break-word;\n  line-height: 1.5;\n}\n\n.field-boolean-true {\n  color: #67C23A;\n  font-weight: 600;\n}\n\n.field-boolean-false {\n  color: #F56C6C;\n  font-weight: 600;\n}\n\n.field-number {\n  color: #E6A23C;\n  font-weight: 500;\n}\n\n.field-array {\n  color: #909399;\n  font-style: italic;\n}\n\n.field-text {\n  color: #606266;\n}\n\n/* 原始数据显示样式 */\n.raw-data-container {\n  background-color: #f8f8f8;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 12px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.raw-data-container pre {\n  margin: 0;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  color: #333;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.no-data {\n  text-align: center;\n  color: #909399;\n  padding: 20px;\n  font-style: italic;\n}\n</style>\n"]}]}