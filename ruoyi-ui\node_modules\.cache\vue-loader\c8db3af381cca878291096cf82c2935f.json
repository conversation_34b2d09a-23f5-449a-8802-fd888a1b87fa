{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormItemWrapper.vue?vue&type=template&id=5158b843&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormItemWrapper.vue", "mtime": 1752386433114}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752199741460}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}