{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeVFormManager\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeVFormManager\\index.vue", "mtime": 1752411008662}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTm9kZVZGb3JtTWFuYWdlcicsCiAgcHJvcHM6IHsKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiAoKSA9PiBbXQogICAgfSwKICAgIHByb2Nlc3NLZXk6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnbnBpX3Byb2Nlc3MnCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbm9kZUZvcm1zOiBbXSwKICAgICAgYWN0aXZlTm9kZXM6ICcnLAogICAgICBzaG93RGVzaWduZXI6IGZhbHNlLAogICAgICBzaG93UHJldmlldzogZmFsc2UsCiAgICAgIHNob3dBbGxQcmV2aWV3OiBmYWxzZSwKICAgICAgcHJldmlld0FjdGl2ZVRhYjogJycsCiAgICAgIGN1cnJlbnRFZGl0Tm9kZToge30sCiAgICAgIGRlc2lnbmVyQ29uZmlnOiB7CiAgICAgICAgbGFuZ3VhZ2VNZW51OiBmYWxzZSwKICAgICAgICBleHRlcm5hbExpbms6IGZhbHNlLAogICAgICAgIGZvcm1UZW1wbGF0ZXM6IHRydWUsCiAgICAgICAgZXZlbnRDb2xsYXBzZTogZmFsc2UsCiAgICAgICAgd2lkZ2V0Q29sbGFwc2U6IGZhbHNlLAogICAgICAgIGNsZWFyRGVzaWduZXJCdXR0b246IHRydWUsCiAgICAgICAgcHJldmlld0Zvcm1CdXR0b246IGZhbHNlLAogICAgICAgIGltcG9ydEpzb25CdXR0b246IHRydWUsCiAgICAgICAgZXhwb3J0SnNvbkJ1dHRvbjogdHJ1ZSwKICAgICAgICBleHBvcnRDb2RlQnV0dG9uOiBmYWxzZSwKICAgICAgICBnZW5lcmF0ZVNGQ0J1dHRvbjogZmFsc2UKICAgICAgfQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZhbHVlOiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgdGhpcy5ub2RlRm9ybXMgPSBuZXdWYWwgfHwgW107CiAgICAgICAgdGhpcy5sb2FkRm9ybVByZXZpZXdzKCk7CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwKICAgICAgZGVlcDogdHJ1ZQogICAgfSwKICAgIG5vZGVGb3JtczogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIHRoaXMuJGVtaXQoJ2lucHV0JywgbmV3VmFsKTsKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOa3u+WKoOiKgueCueihqOWNlSAqLwogICAgYWRkTm9kZUZvcm0oKSB7CiAgICAgIGNvbnN0IG5ld05vZGVGb3JtID0gewogICAgICAgIGlkOiBgbm9kZV8ke0RhdGUubm93KCl9YCwKICAgICAgICBub2RlTmFtZTogYE5QSeiKgueCuSR7dGhpcy5ub2RlRm9ybXMubGVuZ3RoICsgMX1gLAogICAgICAgIG5vZGVUeXBlOiAnbnBpX2FwcGx5JywKICAgICAgICBub2RlS2V5OiAnJywKICAgICAgICBmb3JtSnNvbjogbnVsbCwKICAgICAgICBwcmV2aWV3S2V5OiAwCiAgICAgIH07CiAgICAgIAogICAgICB0aGlzLm5vZGVGb3Jtcy5wdXNoKG5ld05vZGVGb3JtKTsKICAgICAgdGhpcy5hY3RpdmVOb2RlcyA9IG5ld05vZGVGb3JtLmlkOwogICAgfSwKCiAgICAvKiog5Yig6Zmk6IqC54K56KGo5Y2VICovCiAgICByZW1vdmVOb2RlRm9ybShpbmRleCkgewogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTov5nkuKroioLngrnooajljZXlkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5ub2RlRm9ybXMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAoKICAgIC8qKiDorr7orqHooajljZUgKi8KICAgIGRlc2lnbkZvcm0obm9kZUZvcm0pIHsKICAgICAgdGhpcy5jdXJyZW50RWRpdE5vZGUgPSBub2RlRm9ybTsKICAgICAgdGhpcy5zaG93RGVzaWduZXIgPSB0cnVlOwogICAgICAKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIGlmIChub2RlRm9ybS5mb3JtSnNvbikgewogICAgICAgICAgdGhpcy4kcmVmcy52ZkRlc2lnbmVyLnNldEZvcm1Kc29uKG5vZGVGb3JtLmZvcm1Kc29uKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g6K6+572u6buY6K6k55qE56m66KGo5Y2VCiAgICAgICAgICB0aGlzLiRyZWZzLnZmRGVzaWduZXIuc2V0Rm9ybUpzb24oewogICAgICAgICAgICB3aWRnZXRMaXN0OiBbXSwKICAgICAgICAgICAgZm9ybUNvbmZpZzogewogICAgICAgICAgICAgIG1vZGVsTmFtZTogJ2Zvcm1EYXRhJywKICAgICAgICAgICAgICByZWZOYW1lOiAndkZvcm0nLAogICAgICAgICAgICAgIHJ1bGVzTmFtZTogJ3J1bGVzJywKICAgICAgICAgICAgICBsYWJlbFdpZHRoOiA4MCwKICAgICAgICAgICAgICBsYWJlbFBvc2l0aW9uOiAnbGVmdCcsCiAgICAgICAgICAgICAgc2l6ZTogJycsCiAgICAgICAgICAgICAgbGFiZWxBbGlnbjogJ2xhYmVsLWxlZnQtYWxpZ24nLAogICAgICAgICAgICAgIGNzc0NvZGU6ICcnLAogICAgICAgICAgICAgIGN1c3RvbUNsYXNzOiAnJywKICAgICAgICAgICAgICBmdW5jdGlvbnM6ICcnLAogICAgICAgICAgICAgIGxheW91dFR5cGU6ICdQQycKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLyoqIOihqOWNlUpTT07lj5jljJYgKi8KICAgIG9uRm9ybUpzb25DaGFuZ2UoZm9ybUpzb24pIHsKICAgICAgLy8g5a6e5pe25L+d5a2Y6KGo5Y2V6K6+6K6hCiAgICAgIGlmICh0aGlzLmN1cnJlbnRFZGl0Tm9kZS5pZCkgewogICAgICAgIGNvbnN0IG5vZGVGb3JtID0gdGhpcy5ub2RlRm9ybXMuZmluZChuID0+IG4uaWQgPT09IHRoaXMuY3VycmVudEVkaXROb2RlLmlkKTsKICAgICAgICBpZiAobm9kZUZvcm0pIHsKICAgICAgICAgIG5vZGVGb3JtLmZvcm1Kc29uID0gZm9ybUpzb247CiAgICAgICAgICBub2RlRm9ybS5wcmV2aWV3S2V5ID0gRGF0ZS5ub3coKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOS/neWtmOihqOWNleiuvuiuoSAqLwogICAgc2F2ZUZvcm1EZXNpZ24oKSB7CiAgICAgIGNvbnN0IGZvcm1Kc29uID0gdGhpcy4kcmVmcy52ZkRlc2lnbmVyLmdldEZvcm1Kc29uKCk7CiAgICAgIGNvbnN0IG5vZGVGb3JtID0gdGhpcy5ub2RlRm9ybXMuZmluZChuID0+IG4uaWQgPT09IHRoaXMuY3VycmVudEVkaXROb2RlLmlkKTsKICAgICAgaWYgKG5vZGVGb3JtKSB7CiAgICAgICAgbm9kZUZvcm0uZm9ybUpzb24gPSBmb3JtSnNvbjsKICAgICAgICBub2RlRm9ybS5wcmV2aWV3S2V5ID0gRGF0ZS5ub3coKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+ihqOWNleS/neWtmOaIkOWKnycpOwogICAgICAgIHRoaXMubG9hZEZvcm1QcmV2aWV3cygpOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDpooTop4jooajljZUgKi8KICAgIHByZXZpZXdGb3JtKCkgewogICAgICBjb25zdCBmb3JtSnNvbiA9IHRoaXMuJHJlZnMudmZEZXNpZ25lci5nZXRGb3JtSnNvbigpOwogICAgICB0aGlzLnNob3dQcmV2aWV3ID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuJHJlZnMucHJldmlld0Zvcm1SZWYuc2V0Rm9ybUpzb24oZm9ybUpzb24pOwogICAgICB9KTsKICAgIH0sCgogICAgLyoqIOmihOiniOaJgOacieihqOWNlSAqLwogICAgcHJldmlld0FsbEZvcm1zKCkgewogICAgICBjb25zdCBmb3Jtc1dpdGhKc29uID0gdGhpcy5ub2RlRm9ybXMuZmlsdGVyKG4gPT4gbi5mb3JtSnNvbik7CiAgICAgIGlmIChmb3Jtc1dpdGhKc29uLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pqC5peg5bey6YWN572u55qE6KGo5Y2VJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIAogICAgICB0aGlzLnByZXZpZXdBY3RpdmVUYWIgPSBmb3Jtc1dpdGhKc29uWzBdLmlkOwogICAgICB0aGlzLnNob3dBbGxQcmV2aWV3ID0gdHJ1ZTsKICAgICAgCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICBmb3Jtc1dpdGhKc29uLmZvckVhY2gobm9kZUZvcm0gPT4gewogICAgICAgICAgY29uc3QgcmVmID0gdGhpcy4kcmVmc1tgYWxsUHJldmlld18ke25vZGVGb3JtLmlkfWBdOwogICAgICAgICAgaWYgKHJlZiAmJiByZWZbMF0pIHsKICAgICAgICAgICAgcmVmWzBdLnNldEZvcm1Kc29uKG5vZGVGb3JtLmZvcm1Kc29uKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDliqDovb3ooajljZXpooTop4ggKi8KICAgIGxvYWRGb3JtUHJldmlld3MoKSB7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLm5vZGVGb3Jtcy5mb3JFYWNoKG5vZGVGb3JtID0+IHsKICAgICAgICAgIGlmIChub2RlRm9ybS5mb3JtSnNvbikgewogICAgICAgICAgICBjb25zdCByZWYgPSB0aGlzLiRyZWZzW2BwcmV2aWV3XyR7bm9kZUZvcm0uaWR9YF07CiAgICAgICAgICAgIGlmIChyZWYgJiYgcmVmWzBdKSB7CiAgICAgICAgICAgICAgcmVmWzBdLnNldEZvcm1Kc29uKG5vZGVGb3JtLmZvcm1Kc29uKTsKICAgICAgICAgICAgICByZWZbMF0uZGlzYWJsZUZvcm0oKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCgogICAgLyoqIOiOt+WPluiKgueCueexu+Wei+agh+etviAqLwogICAgZ2V0Tm9kZVR5cGVUYWcodHlwZSkgewogICAgICBjb25zdCB0YWdNYXAgPSB7CiAgICAgICAgbnBpX2FwcGx5OiAncHJpbWFyeScsCiAgICAgICAgdGVjaF9yZXZpZXc6ICdzdWNjZXNzJywKICAgICAgICBwcm9jZXNzX3JldmlldzogJ3dhcm5pbmcnLAogICAgICAgIHF1YWxpdHlfcmV2aWV3OiAnZGFuZ2VyJywKICAgICAgICBjb3N0X3JldmlldzogJ2luZm8nLAogICAgICAgIGZpbmFsX2FwcHJvdmFsOiAncHJpbWFyeScKICAgICAgfTsKICAgICAgcmV0dXJuIHRhZ01hcFt0eXBlXSB8fCAncHJpbWFyeSc7CiAgICB9LAoKICAgIC8qKiDojrflj5boioLngrnnsbvlnovmlofmnKwgKi8KICAgIGdldE5vZGVUeXBlVGV4dCh0eXBlKSB7CiAgICAgIGNvbnN0IHRleHRNYXAgPSB7CiAgICAgICAgbnBpX2FwcGx5OiAnTlBJ55Sz6K+3JywKICAgICAgICB0ZWNoX3JldmlldzogJ+aKgOacr+ivhOWuoScsCiAgICAgICAgcHJvY2Vzc19yZXZpZXc6ICflt6Xoibror4TlrqEnLAogICAgICAgIHF1YWxpdHlfcmV2aWV3OiAn6LSo6YeP6K+E5a6hJywKICAgICAgICBjb3N0X3JldmlldzogJ+aIkOacrOivhOWuoScsCiAgICAgICAgZmluYWxfYXBwcm92YWw6ICfmnIDnu4jlrqHmibknCiAgICAgIH07CiAgICAgIHJldHVybiB0ZXh0TWFwW3R5cGVdIHx8IHR5cGU7CiAgICB9LAoKICAgIC8qKiDkv53lrZjphY3nva4gKi8KICAgIHNhdmVDb25maWcoKSB7CiAgICAgIGNvbnN0IGNvbmZpZyA9IHsKICAgICAgICBwcm9jZXNzS2V5OiB0aGlzLnByb2Nlc3NLZXksCiAgICAgICAgbm9kZUZvcm1zOiB0aGlzLm5vZGVGb3JtcywKICAgICAgICBjcmVhdGVUaW1lOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksCiAgICAgICAgdmVyc2lvbjogJzEuMCcKICAgICAgfTsKICAgICAgCiAgICAgIC8vIOS/neWtmOWIsOacrOWcsOWtmOWCqAogICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShgbm9kZV92Zm9ybV9jb25maWdfJHt0aGlzLnByb2Nlc3NLZXl9YCwgSlNPTi5zdHJpbmdpZnkoY29uZmlnKSk7CiAgICAgIAogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mFjee9ruS/neWtmOaIkOWKnycpOwogICAgICB0aGlzLiRlbWl0KCdzYXZlJywgY29uZmlnKTsKICAgIH0sCgogICAgLyoqIOWvvOWHuumFjee9riAqLwogICAgZXhwb3J0Q29uZmlnKCkgewogICAgICBjb25zdCBjb25maWcgPSB7CiAgICAgICAgcHJvY2Vzc0tleTogdGhpcy5wcm9jZXNzS2V5LAogICAgICAgIG5vZGVGb3JtczogdGhpcy5ub2RlRm9ybXMsCiAgICAgICAgY3JlYXRlVGltZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLAogICAgICAgIHZlcnNpb246ICcxLjAnCiAgICAgIH07CiAgICAgIAogICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW0pTT04uc3RyaW5naWZ5KGNvbmZpZywgbnVsbCwgMildLCB7IAogICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi9qc29uJyAKICAgICAgfSk7CiAgICAgIAogICAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOwogICAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOwogICAgICBhLmhyZWYgPSB1cmw7CiAgICAgIGEuZG93bmxvYWQgPSBgJHt0aGlzLnByb2Nlc3NLZXl9X25vZGVfZm9ybXNfJHtEYXRlLm5vdygpfS5qc29uYDsKICAgICAgYS5jbGljaygpOwogICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybCk7CiAgICAgIAogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WvvOWHuuaIkOWKnycpOwogICAgfSwKCiAgICAvKiog5a+85YWl6YWN572uICovCiAgICBpbXBvcnRDb25maWcoKSB7CiAgICAgIHRoaXMuJHJlZnMuZmlsZUlucHV0LmNsaWNrKCk7CiAgICB9LAoKICAgIC8qKiDlpITnkIbmlofku7blr7zlhaUgKi8KICAgIGhhbmRsZUZpbGVJbXBvcnQoZXZlbnQpIHsKICAgICAgY29uc3QgZmlsZSA9IGV2ZW50LnRhcmdldC5maWxlc1swXTsKICAgICAgaWYgKCFmaWxlKSByZXR1cm47CgogICAgICBjb25zdCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOwogICAgICByZWFkZXIub25sb2FkID0gKGUpID0+IHsKICAgICAgICB0cnkgewogICAgICAgICAgY29uc3QgY29uZmlnID0gSlNPTi5wYXJzZShlLnRhcmdldC5yZXN1bHQpOwogICAgICAgICAgaWYgKGNvbmZpZy5ub2RlRm9ybXMgJiYgQXJyYXkuaXNBcnJheShjb25maWcubm9kZUZvcm1zKSkgewogICAgICAgICAgICB0aGlzLm5vZGVGb3JtcyA9IGNvbmZpZy5ub2RlRm9ybXM7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a+85YWl5oiQ5YqfJyk7CiAgICAgICAgICAgIHRoaXMubG9hZEZvcm1QcmV2aWV3cygpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25qC85byP5LiN5q2j56GuJyk7CiAgICAgICAgICB9CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuino+aekOWksei0pScpOwogICAgICAgIH0KICAgICAgfTsKICAgICAgcmVhZGVyLnJlYWRBc1RleHQoZmlsZSk7CiAgICAgIAogICAgICBldmVudC50YXJnZXQudmFsdWUgPSAnJzsKICAgIH0sCgogICAgLyoqIOagueaNruiKgueCueagh+ivhuiOt+WPluihqOWNlemFjee9riAqLwogICAgZ2V0Rm9ybUJ5Tm9kZUtleShub2RlS2V5KSB7CiAgICAgIHJldHVybiB0aGlzLm5vZGVGb3Jtcy5maW5kKGZvcm0gPT4gZm9ybS5ub2RlS2V5ID09PSBub2RlS2V5KTsKICAgIH0sCgogICAgLyoqIOagueaNruiKgueCueexu+Wei+iOt+WPluihqOWNlemFjee9riAqLwogICAgZ2V0Rm9ybUJ5Tm9kZVR5cGUobm9kZVR5cGUpIHsKICAgICAgcmV0dXJuIHRoaXMubm9kZUZvcm1zLmZpbmQoZm9ybSA9PiBmb3JtLm5vZGVUeXBlID09PSBub2RlVHlwZSk7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/NodeVFormManager", "sourcesContent": ["<template>\n  <div class=\"node-vform-manager\">\n    <div class=\"manager-header\">\n      <h3>\n        <i class=\"el-icon-s-order\"></i>\n        NPI流程节点表单配置\n      </h3>\n      <el-button type=\"primary\" @click=\"addNodeForm\">\n        <i class=\"el-icon-plus\"></i>\n        添加节点表单\n      </el-button>\n    </div>\n\n    <div class=\"node-forms-list\">\n      <el-collapse v-model=\"activeNodes\" accordion>\n        <el-collapse-item \n          v-for=\"(nodeForm, index) in nodeForms\" \n          :key=\"nodeForm.id\"\n          :name=\"nodeForm.id\"\n        >\n          <template slot=\"title\">\n            <div class=\"node-title\">\n              <i class=\"el-icon-document\"></i>\n              <span class=\"node-name\">{{ nodeForm.nodeName }}</span>\n              <el-tag :type=\"getNodeTypeTag(nodeForm.nodeType)\" size=\"mini\">\n                {{ getNodeTypeText(nodeForm.nodeType) }}\n              </el-tag>\n              <span class=\"form-status\">{{ nodeForm.formJson ? '已配置' : '未配置' }}</span>\n            </div>\n          </template>\n\n          <div class=\"node-form-content\">\n            <div class=\"node-config\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"节点名称\">\n                    <el-input v-model=\"nodeForm.nodeName\" placeholder=\"请输入节点名称\" />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"节点类型\">\n                    <el-select v-model=\"nodeForm.nodeType\" style=\"width: 100%\">\n                      <el-option label=\"NPI申请\" value=\"npi_apply\" />\n                      <el-option label=\"技术评审\" value=\"tech_review\" />\n                      <el-option label=\"工艺评审\" value=\"process_review\" />\n                      <el-option label=\"质量评审\" value=\"quality_review\" />\n                      <el-option label=\"成本评审\" value=\"cost_review\" />\n                      <el-option label=\"最终审批\" value=\"final_approval\" />\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"节点标识\">\n                    <el-input v-model=\"nodeForm.nodeKey\" placeholder=\"流程图中的节点ID\" />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"操作\">\n                    <el-button type=\"primary\" size=\"small\" @click=\"designForm(nodeForm)\">\n                      设计表单\n                    </el-button>\n                    <el-button type=\"danger\" size=\"small\" @click=\"removeNodeForm(index)\">\n                      删除\n                    </el-button>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </div>\n\n            <!-- 表单预览 -->\n            <div v-if=\"nodeForm.formJson\" class=\"form-preview\">\n              <h5>表单预览</h5>\n              <div class=\"preview-container\">\n                <v-form-render \n                  :ref=\"`preview_${nodeForm.id}`\"\n                  :key=\"`preview_${nodeForm.id}_${nodeForm.previewKey || 0}`\"\n                />\n              </div>\n            </div>\n            <div v-else class=\"no-form\">\n              <i class=\"el-icon-document-add\"></i>\n              <p>暂未配置表单，点击\"设计表单\"开始配置</p>\n            </div>\n          </div>\n        </el-collapse-item>\n      </el-collapse>\n\n      <div v-if=\"nodeForms.length === 0\" class=\"empty-state\">\n        <i class=\"el-icon-document-add\"></i>\n        <p>暂无节点表单，点击\"添加节点表单\"开始创建</p>\n      </div>\n    </div>\n\n    <div class=\"manager-footer\">\n      <el-button @click=\"previewAllForms\">预览所有表单</el-button>\n      <el-button type=\"primary\" @click=\"saveConfig\">保存配置</el-button>\n      <el-button @click=\"exportConfig\">导出配置</el-button>\n      <el-button @click=\"importConfig\">导入配置</el-button>\n    </div>\n\n    <!-- VForm设计器对话框 -->\n    <el-dialog \n      :title=\"`设计表单 - ${currentEditNode.nodeName}`\"\n      :visible.sync=\"showDesigner\" \n      width=\"90%\" \n      top=\"5vh\"\n      :close-on-click-modal=\"false\"\n    >\n      <div class=\"vform-designer-container\">\n        <v-form-designer \n          ref=\"vfDesigner\" \n          :designer-config=\"designerConfig\"\n          @form-json-change=\"onFormJsonChange\"\n        >\n          <template #customToolButtons>\n            <el-button type=\"primary\" @click=\"saveFormDesign\">保存表单</el-button>\n            <el-button @click=\"previewForm\">预览表单</el-button>\n          </template>\n        </v-form-designer>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"showPreview\" width=\"60%\">\n      <div class=\"form-preview-dialog\">\n        <v-form-render ref=\"previewFormRef\" />\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"showPreview = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 所有表单预览 -->\n    <el-dialog title=\"所有表单预览\" :visible.sync=\"showAllPreview\" width=\"80%\">\n      <el-tabs v-model=\"previewActiveTab\" type=\"card\">\n        <el-tab-pane \n          v-for=\"nodeForm in nodeForms.filter(n => n.formJson)\" \n          :key=\"nodeForm.id\"\n          :label=\"nodeForm.nodeName\"\n          :name=\"nodeForm.id\"\n        >\n          <v-form-render \n            :ref=\"`allPreview_${nodeForm.id}`\"\n            :key=\"`allPreview_${nodeForm.id}_${nodeForm.previewKey || 0}`\"\n          />\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n\n    <!-- 导入文件 -->\n    <input \n      ref=\"fileInput\" \n      type=\"file\" \n      accept=\".json\" \n      style=\"display: none\" \n      @change=\"handleFileImport\"\n    />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'NodeVFormManager',\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    },\n    processKey: {\n      type: String,\n      default: 'npi_process'\n    }\n  },\n  data() {\n    return {\n      nodeForms: [],\n      activeNodes: '',\n      showDesigner: false,\n      showPreview: false,\n      showAllPreview: false,\n      previewActiveTab: '',\n      currentEditNode: {},\n      designerConfig: {\n        languageMenu: false,\n        externalLink: false,\n        formTemplates: true,\n        eventCollapse: false,\n        widgetCollapse: false,\n        clearDesignerButton: true,\n        previewFormButton: false,\n        importJsonButton: true,\n        exportJsonButton: true,\n        exportCodeButton: false,\n        generateSFCButton: false\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        this.nodeForms = newVal || [];\n        this.loadFormPreviews();\n      },\n      immediate: true,\n      deep: true\n    },\n    nodeForms: {\n      handler(newVal) {\n        this.$emit('input', newVal);\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 添加节点表单 */\n    addNodeForm() {\n      const newNodeForm = {\n        id: `node_${Date.now()}`,\n        nodeName: `NPI节点${this.nodeForms.length + 1}`,\n        nodeType: 'npi_apply',\n        nodeKey: '',\n        formJson: null,\n        previewKey: 0\n      };\n      \n      this.nodeForms.push(newNodeForm);\n      this.activeNodes = newNodeForm.id;\n    },\n\n    /** 删除节点表单 */\n    removeNodeForm(index) {\n      this.$confirm('确定要删除这个节点表单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.nodeForms.splice(index, 1);\n        this.$message.success('删除成功');\n      }).catch(() => {});\n    },\n\n    /** 设计表单 */\n    designForm(nodeForm) {\n      this.currentEditNode = nodeForm;\n      this.showDesigner = true;\n      \n      this.$nextTick(() => {\n        if (nodeForm.formJson) {\n          this.$refs.vfDesigner.setFormJson(nodeForm.formJson);\n        } else {\n          // 设置默认的空表单\n          this.$refs.vfDesigner.setFormJson({\n            widgetList: [],\n            formConfig: {\n              modelName: 'formData',\n              refName: 'vForm',\n              rulesName: 'rules',\n              labelWidth: 80,\n              labelPosition: 'left',\n              size: '',\n              labelAlign: 'label-left-align',\n              cssCode: '',\n              customClass: '',\n              functions: '',\n              layoutType: 'PC'\n            }\n          });\n        }\n      });\n    },\n\n    /** 表单JSON变化 */\n    onFormJsonChange(formJson) {\n      // 实时保存表单设计\n      if (this.currentEditNode.id) {\n        const nodeForm = this.nodeForms.find(n => n.id === this.currentEditNode.id);\n        if (nodeForm) {\n          nodeForm.formJson = formJson;\n          nodeForm.previewKey = Date.now();\n        }\n      }\n    },\n\n    /** 保存表单设计 */\n    saveFormDesign() {\n      const formJson = this.$refs.vfDesigner.getFormJson();\n      const nodeForm = this.nodeForms.find(n => n.id === this.currentEditNode.id);\n      if (nodeForm) {\n        nodeForm.formJson = formJson;\n        nodeForm.previewKey = Date.now();\n        this.$message.success('表单保存成功');\n        this.loadFormPreviews();\n      }\n    },\n\n    /** 预览表单 */\n    previewForm() {\n      const formJson = this.$refs.vfDesigner.getFormJson();\n      this.showPreview = true;\n      this.$nextTick(() => {\n        this.$refs.previewFormRef.setFormJson(formJson);\n      });\n    },\n\n    /** 预览所有表单 */\n    previewAllForms() {\n      const formsWithJson = this.nodeForms.filter(n => n.formJson);\n      if (formsWithJson.length === 0) {\n        this.$message.warning('暂无已配置的表单');\n        return;\n      }\n      \n      this.previewActiveTab = formsWithJson[0].id;\n      this.showAllPreview = true;\n      \n      this.$nextTick(() => {\n        formsWithJson.forEach(nodeForm => {\n          const ref = this.$refs[`allPreview_${nodeForm.id}`];\n          if (ref && ref[0]) {\n            ref[0].setFormJson(nodeForm.formJson);\n          }\n        });\n      });\n    },\n\n    /** 加载表单预览 */\n    loadFormPreviews() {\n      this.$nextTick(() => {\n        this.nodeForms.forEach(nodeForm => {\n          if (nodeForm.formJson) {\n            const ref = this.$refs[`preview_${nodeForm.id}`];\n            if (ref && ref[0]) {\n              ref[0].setFormJson(nodeForm.formJson);\n              ref[0].disableForm();\n            }\n          }\n        });\n      });\n    },\n\n    /** 获取节点类型标签 */\n    getNodeTypeTag(type) {\n      const tagMap = {\n        npi_apply: 'primary',\n        tech_review: 'success',\n        process_review: 'warning',\n        quality_review: 'danger',\n        cost_review: 'info',\n        final_approval: 'primary'\n      };\n      return tagMap[type] || 'primary';\n    },\n\n    /** 获取节点类型文本 */\n    getNodeTypeText(type) {\n      const textMap = {\n        npi_apply: 'NPI申请',\n        tech_review: '技术评审',\n        process_review: '工艺评审',\n        quality_review: '质量评审',\n        cost_review: '成本评审',\n        final_approval: '最终审批'\n      };\n      return textMap[type] || type;\n    },\n\n    /** 保存配置 */\n    saveConfig() {\n      const config = {\n        processKey: this.processKey,\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      // 保存到本地存储\n      localStorage.setItem(`node_vform_config_${this.processKey}`, JSON.stringify(config));\n      \n      this.$message.success('配置保存成功');\n      this.$emit('save', config);\n    },\n\n    /** 导出配置 */\n    exportConfig() {\n      const config = {\n        processKey: this.processKey,\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      const blob = new Blob([JSON.stringify(config, null, 2)], { \n        type: 'application/json' \n      });\n      \n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `${this.processKey}_node_forms_${Date.now()}.json`;\n      a.click();\n      URL.revokeObjectURL(url);\n      \n      this.$message.success('导出成功');\n    },\n\n    /** 导入配置 */\n    importConfig() {\n      this.$refs.fileInput.click();\n    },\n\n    /** 处理文件导入 */\n    handleFileImport(event) {\n      const file = event.target.files[0];\n      if (!file) return;\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const config = JSON.parse(e.target.result);\n          if (config.nodeForms && Array.isArray(config.nodeForms)) {\n            this.nodeForms = config.nodeForms;\n            this.$message.success('导入成功');\n            this.loadFormPreviews();\n          } else {\n            this.$message.error('文件格式不正确');\n          }\n        } catch (error) {\n          this.$message.error('文件解析失败');\n        }\n      };\n      reader.readAsText(file);\n      \n      event.target.value = '';\n    },\n\n    /** 根据节点标识获取表单配置 */\n    getFormByNodeKey(nodeKey) {\n      return this.nodeForms.find(form => form.nodeKey === nodeKey);\n    },\n\n    /** 根据节点类型获取表单配置 */\n    getFormByNodeType(nodeType) {\n      return this.nodeForms.find(form => form.nodeType === nodeType);\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.node-vform-manager {\n  background-color: white;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.manager-header {\n  background-color: #F5F7FA;\n  padding: 16px 20px;\n  border-bottom: 1px solid #EBEEF5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h3 {\n    margin: 0;\n    color: #303133;\n    font-size: 16px;\n\n    i {\n      margin-right: 8px;\n      color: #409EFF;\n    }\n  }\n}\n\n.node-forms-list {\n  padding: 20px;\n}\n\n.node-title {\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  i {\n    margin-right: 8px;\n    color: #409EFF;\n  }\n\n  .node-name {\n    font-weight: 600;\n    margin-right: 12px;\n  }\n\n  .form-status {\n    margin-left: auto;\n    color: #909399;\n    font-size: 12px;\n  }\n}\n\n.node-form-content {\n  padding: 16px 0;\n}\n\n.node-config {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #FAFAFA;\n  border-radius: 4px;\n}\n\n.form-preview {\n  margin-top: 16px;\n  \n  h5 {\n    margin: 0 0 12px 0;\n    color: #606266;\n  }\n}\n\n.preview-container {\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  padding: 16px;\n  background-color: white;\n}\n\n.no-form {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n  border: 1px dashed #E4E7ED;\n  border-radius: 4px;\n\n  i {\n    font-size: 48px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.manager-footer {\n  padding: 16px 20px;\n  border-top: 1px solid #EBEEF5;\n  background-color: #FAFAFA;\n  text-align: right;\n\n  .el-button {\n    margin-left: 8px;\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n\n  i {\n    font-size: 64px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.vform-designer-container {\n  height: 70vh;\n  overflow: hidden;\n}\n\n.form-preview-dialog {\n  min-height: 300px;\n  padding: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n}\n</style>\n"]}]}