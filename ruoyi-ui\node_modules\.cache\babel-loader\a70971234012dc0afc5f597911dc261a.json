{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\index.vue", "mtime": 1752386577717}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9SdW9ZaS1mbG93YWJsZS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9SdW9ZaS1mbG93YWJsZS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC1pbmRleC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5qc29uLnN0cmluZ2lmeS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLnVybC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLnVybC50by1qc29uLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIudXJsLXNlYXJjaC1wYXJhbXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi51cmwtc2VhcmNoLXBhcmFtcy5kZWxldGUuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi51cmwtc2VhcmNoLXBhcmFtcy5oYXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi51cmwtc2VhcmNoLXBhcmFtcy5zaXplLmpzIik7CnZhciBfdnVlZHJhZ2dhYmxlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJ2dWVkcmFnZ2FibGUiKSk7CnZhciBfRm9ybUl0ZW1XcmFwcGVyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2NvbXBvbmVudHMvRm9ybUl0ZW1XcmFwcGVyLnZ1ZSIpKTsKdmFyIF9Gb3JtUHJvcGVydGllcyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9jb21wb25lbnRzL0Zvcm1Qcm9wZXJ0aWVzLnZ1ZSIpKTsKdmFyIF9Gb3JtUHJldmlldyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9jb21wb25lbnRzL0Zvcm1QcmV2aWV3LnZ1ZSIpKTsKdmFyIF9pbmRleCA9IHJlcXVpcmUoIi4vdXRpbHMvaW5kZXguanMiKTsKdmFyIF9jb21wb25lbnRzID0gcmVxdWlyZSgiLi9jb25maWcvY29tcG9uZW50cy5qcyIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQyID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdGb3JtRGVzaWduZXInLAogIGNvbXBvbmVudHM6IHsKICAgIGRyYWdnYWJsZTogX3Z1ZWRyYWdnYWJsZS5kZWZhdWx0LAogICAgRm9ybUl0ZW1XcmFwcGVyOiBfRm9ybUl0ZW1XcmFwcGVyLmRlZmF1bHQsCiAgICBGb3JtUHJvcGVydGllczogX0Zvcm1Qcm9wZXJ0aWVzLmRlZmF1bHQsCiAgICBGb3JtUHJldmlldzogX0Zvcm1QcmV2aWV3LmRlZmF1bHQKICB9LAogIHByb3BzOiB7CiAgICBmb3JtRGF0YTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZvcm1Db25maWc6IHsKICAgICAgICBmb3JtTmFtZTogJycsCiAgICAgICAgcmVtYXJrOiAnJwogICAgICB9LAogICAgICBmb3JtSXRlbXM6IFtdLAogICAgICBzZWxlY3RlZEl0ZW06IG51bGwsCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwKICAgICAgaW1wb3J0VmlzaWJsZTogZmFsc2UsCiAgICAgIGltcG9ydEpzb246ICcnLAogICAgICBiYXNpY0NvbXBvbmVudHM6IF9jb21wb25lbnRzLmJhc2ljQ29tcG9uZW50cywKICAgICAgYWR2YW5jZWRDb21wb25lbnRzOiBfY29tcG9uZW50cy5hZHZhbmNlZENvbXBvbmVudHMsCiAgICAgIGxheW91dENvbXBvbmVudHM6IF9jb21wb25lbnRzLmxheW91dENvbXBvbmVudHMKICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5pbml0Rm9ybURhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGluaXRGb3JtRGF0YTogZnVuY3Rpb24gaW5pdEZvcm1EYXRhKCkgewogICAgICBpZiAodGhpcy5mb3JtRGF0YS5mb3JtSWQpIHsKICAgICAgICB0aGlzLmZvcm1Db25maWcuZm9ybU5hbWUgPSB0aGlzLmZvcm1EYXRhLmZvcm1OYW1lIHx8ICcnOwogICAgICAgIHRoaXMuZm9ybUNvbmZpZy5yZW1hcmsgPSB0aGlzLmZvcm1EYXRhLnJlbWFyayB8fCAnJzsKCiAgICAgICAgLy8g6Kej5p6Q6KGo5Y2V5YaF5a65CiAgICAgICAgaWYgKHRoaXMuZm9ybURhdGEuZm9ybUNvbnRlbnQpIHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIHZhciBjb250ZW50ID0gSlNPTi5wYXJzZSh0aGlzLmZvcm1EYXRhLmZvcm1Db250ZW50KTsKICAgICAgICAgICAgdGhpcy5mb3JtSXRlbXMgPSBjb250ZW50LmZvcm1JdGVtcyB8fCBbXTsKICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgY29uc29sZS53YXJuKCfooajljZXlhoXlrrnop6PmnpDlpLHotKU6JywgZSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgaGFuZGxlRHJhZ1N0YXJ0OiBmdW5jdGlvbiBoYW5kbGVEcmFnU3RhcnQoZXZlbnQsIGNvbXBvbmVudCkgewogICAgICBldmVudC5kYXRhVHJhbnNmZXIuc2V0RGF0YSgnY29tcG9uZW50JywgSlNPTi5zdHJpbmdpZnkoY29tcG9uZW50KSk7CiAgICB9LAogICAgaGFuZGxlRHJhZ092ZXI6IGZ1bmN0aW9uIGhhbmRsZURyYWdPdmVyKGV2ZW50KSB7CiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7CiAgICB9LAogICAgaGFuZGxlRHJvcDogZnVuY3Rpb24gaGFuZGxlRHJvcChldmVudCkgewogICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpOwogICAgICB2YXIgY29tcG9uZW50RGF0YSA9IGV2ZW50LmRhdGFUcmFuc2Zlci5nZXREYXRhKCdjb21wb25lbnQnKTsKICAgICAgaWYgKGNvbXBvbmVudERhdGEpIHsKICAgICAgICB2YXIgY29tcG9uZW50ID0gSlNPTi5wYXJzZShjb21wb25lbnREYXRhKTsKICAgICAgICB0aGlzLmFkZEZvcm1JdGVtKGNvbXBvbmVudCk7CiAgICAgIH0KICAgIH0sCiAgICBhZGRGb3JtSXRlbTogZnVuY3Rpb24gYWRkRm9ybUl0ZW0oY29tcG9uZW50KSB7CiAgICAgIHZhciBuZXdJdGVtID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHsKICAgICAgICBpZDogKDAsIF9pbmRleC5nZW5lcmF0ZUlkKSgpLAogICAgICAgIHR5cGU6IGNvbXBvbmVudC50eXBlLAogICAgICAgIGxhYmVsOiBjb21wb25lbnQubGFiZWwsCiAgICAgICAgaWNvbjogY29tcG9uZW50Lmljb24KICAgICAgfSwgY29tcG9uZW50LmRlZmF1bHRQcm9wcyk7CiAgICAgIHRoaXMuZm9ybUl0ZW1zLnB1c2gobmV3SXRlbSk7CiAgICAgIHRoaXMuc2VsZWN0ZWRJdGVtID0gbmV3SXRlbTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3RJdGVtOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3RJdGVtKGl0ZW0pIHsKICAgICAgdGhpcy5zZWxlY3RlZEl0ZW0gPSBpdGVtOwogICAgfSwKICAgIGhhbmRsZURlbGV0ZUl0ZW06IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZUl0ZW0oaW5kZXgpIHsKICAgICAgdmFyIF90aGlzJGZvcm1JdGVtcyRpbmRleDsKICAgICAgdGhpcy5mb3JtSXRlbXMuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRJdGVtICYmIHRoaXMuc2VsZWN0ZWRJdGVtLmlkID09PSAoKF90aGlzJGZvcm1JdGVtcyRpbmRleCA9IHRoaXMuZm9ybUl0ZW1zW2luZGV4XSkgPT09IG51bGwgfHwgX3RoaXMkZm9ybUl0ZW1zJGluZGV4ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpcyRmb3JtSXRlbXMkaW5kZXguaWQpKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEl0ZW0gPSBudWxsOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlQ2xvbmVJdGVtOiBmdW5jdGlvbiBoYW5kbGVDbG9uZUl0ZW0oaXRlbSkgewogICAgICB2YXIgY2xvbmVkSXRlbSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoaXRlbSkpKSwge30sIHsKICAgICAgICBpZDogKDAsIF9pbmRleC5nZW5lcmF0ZUlkKSgpCiAgICAgIH0pOwogICAgICB2YXIgaW5kZXggPSB0aGlzLmZvcm1JdGVtcy5maW5kSW5kZXgoZnVuY3Rpb24gKGkpIHsKICAgICAgICByZXR1cm4gaS5pZCA9PT0gaXRlbS5pZDsKICAgICAgfSk7CiAgICAgIHRoaXMuZm9ybUl0ZW1zLnNwbGljZShpbmRleCArIDEsIDAsIGNsb25lZEl0ZW0pOwogICAgfSwKICAgIGhhbmRsZVVwZGF0ZVByb3BlcnRpZXM6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZVByb3BlcnRpZXMocHJvcGVydGllcykgewogICAgICBpZiAodGhpcy5zZWxlY3RlZEl0ZW0pIHsKICAgICAgICBPYmplY3QuYXNzaWduKHRoaXMuc2VsZWN0ZWRJdGVtLCBwcm9wZXJ0aWVzKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVNvcnRFbmQ6IGZ1bmN0aW9uIGhhbmRsZVNvcnRFbmQoKSB7CiAgICAgIC8vIOaLluaLveaOkuW6j+WujOaIkOWQjueahOWkhOeQhgogICAgfSwKICAgIGNsZWFyU2VsZWN0aW9uOiBmdW5jdGlvbiBjbGVhclNlbGVjdGlvbigpIHsKICAgICAgdGhpcy5zZWxlY3RlZEl0ZW0gPSBudWxsOwogICAgfSwKICAgIGhhbmRsZVNhdmU6IGZ1bmN0aW9uIGhhbmRsZVNhdmUoKSB7CiAgICAgIGlmICghdGhpcy5mb3JtQ29uZmlnLmZvcm1OYW1lKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+36L6T5YWl6KGo5Y2V5ZCN56ewJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHZhciBmb3JtRGF0YSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHRoaXMuZm9ybURhdGEpLCB7fSwgewogICAgICAgIGZvcm1OYW1lOiB0aGlzLmZvcm1Db25maWcuZm9ybU5hbWUsCiAgICAgICAgcmVtYXJrOiB0aGlzLmZvcm1Db25maWcucmVtYXJrLAogICAgICAgIGZvcm1Db250ZW50OiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICBmb3JtSXRlbXM6IHRoaXMuZm9ybUl0ZW1zLAogICAgICAgICAgY29uZmlnOiB0aGlzLmZvcm1Db25maWcKICAgICAgICB9KQogICAgICB9KTsKICAgICAgdGhpcy4kZW1pdCgnc2F2ZScsIGZvcm1EYXRhKTsKICAgIH0sCiAgICBoYW5kbGVDYW5jZWw6IGZ1bmN0aW9uIGhhbmRsZUNhbmNlbCgpIHsKICAgICAgdGhpcy4kZW1pdCgnY2FuY2VsJyk7CiAgICB9LAogICAgaGFuZGxlUHJldmlldzogZnVuY3Rpb24gaGFuZGxlUHJldmlldygpIHsKICAgICAgdGhpcy5wcmV2aWV3VmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlRXhwb3J0SnNvbjogZnVuY3Rpb24gaGFuZGxlRXhwb3J0SnNvbigpIHsKICAgICAgdmFyIGV4cG9ydERhdGEgPSB7CiAgICAgICAgZm9ybUl0ZW1zOiB0aGlzLmZvcm1JdGVtcywKICAgICAgICBjb25maWc6IHRoaXMuZm9ybUNvbmZpZwogICAgICB9OwogICAgICB2YXIgYmxvYiA9IG5ldyBCbG9iKFtKU09OLnN0cmluZ2lmeShleHBvcnREYXRhLCBudWxsLCAyKV0sIHsKICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vanNvbicKICAgICAgfSk7CiAgICAgIHZhciB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOwogICAgICB2YXIgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsKICAgICAgYS5ocmVmID0gdXJsOwogICAgICBhLmRvd25sb2FkID0gIiIuY29uY2F0KHRoaXMuZm9ybUNvbmZpZy5mb3JtTmFtZSB8fCAnZm9ybScsICIuanNvbiIpOwogICAgICBhLmNsaWNrKCk7CiAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTsKICAgIH0sCiAgICBoYW5kbGVJbXBvcnRKc29uOiBmdW5jdGlvbiBoYW5kbGVJbXBvcnRKc29uKCkgewogICAgICB0aGlzLmltcG9ydFZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLmltcG9ydEpzb24gPSAnJzsKICAgIH0sCiAgICBoYW5kbGVJbXBvcnRDb25maXJtOiBmdW5jdGlvbiBoYW5kbGVJbXBvcnRDb25maXJtKCkgewogICAgICB0cnkgewogICAgICAgIHZhciBkYXRhID0gSlNPTi5wYXJzZSh0aGlzLmltcG9ydEpzb24pOwogICAgICAgIGlmIChkYXRhLmZvcm1JdGVtcykgewogICAgICAgICAgdGhpcy5mb3JtSXRlbXMgPSBkYXRhLmZvcm1JdGVtczsKICAgICAgICB9CiAgICAgICAgaWYgKGRhdGEuY29uZmlnKSB7CiAgICAgICAgICB0aGlzLmZvcm1Db25maWcgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLmZvcm1Db25maWcpLCBkYXRhLmNvbmZpZyk7CiAgICAgICAgfQogICAgICAgIHRoaXMuaW1wb3J0VmlzaWJsZSA9IGZhbHNlOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a+85YWl5oiQ5YqfJyk7CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCdKU09O5qC85byP6ZSZ6K+v77yM6K+35qOA5p+l5ZCO6YeN6K+VJyk7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_vuedraggable", "_interopRequireDefault", "require", "_FormItemWrapper", "_FormProperties", "_FormPreview", "_index", "_components", "name", "components", "draggable", "FormItemWrapper", "FormProperties", "FormPreview", "props", "formData", "type", "Object", "default", "data", "formConfig", "formName", "remark", "formItems", "selectedItem", "previewVisible", "importVisible", "importJson", "basicComponents", "advancedComponents", "layoutComponents", "created", "initFormData", "methods", "formId", "formContent", "content", "JSON", "parse", "e", "console", "warn", "handleDragStart", "event", "component", "dataTransfer", "setData", "stringify", "handleDragOver", "preventDefault", "handleDrop", "componentData", "getData", "addFormItem", "newItem", "_objectSpread2", "id", "generateId", "label", "icon", "defaultProps", "push", "handleSelectItem", "item", "handleDeleteItem", "index", "_this$formItems$index", "splice", "handleCloneItem", "clonedItem", "findIndex", "i", "handleUpdateProperties", "properties", "assign", "handleSortEnd", "clearSelection", "handleSave", "$message", "error", "config", "$emit", "handleCancel", "handlePreview", "handleExportJson", "exportData", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "concat", "click", "revokeObjectURL", "handleImportJson", "handleImportConfirm", "success"], "sources": ["src/components/FormDesigner/index.vue"], "sourcesContent": ["<template>\n  <div class=\"form-designer\">\n    <div class=\"designer-header\">\n      <el-form :model=\"formConfig\" :inline=\"true\" size=\"small\">\n        <el-form-item label=\"表单名称\">\n          <el-input v-model=\"formConfig.formName\" placeholder=\"请输入表单名称\" style=\"width: 200px\" />\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"formConfig.remark\" placeholder=\"请输入备注\" style=\"width: 200px\" />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"handleSave\">保存表单</el-button>\n          <el-button @click=\"handleCancel\">取消</el-button>\n          <el-button type=\"success\" @click=\"handlePreview\">预览</el-button>\n          <el-button type=\"info\" @click=\"handleExportJson\">导出JSON</el-button>\n          <el-button type=\"warning\" @click=\"handleImportJson\">导入JSON</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <div class=\"designer-body\">\n      <!-- 左侧组件面板 -->\n      <div class=\"components-panel\">\n        <div class=\"panel-title\">组件库</div>\n        <div class=\"component-groups\">\n          <!-- 基础组件 -->\n          <div class=\"component-group\">\n            <div class=\"group-title\">基础组件</div>\n            <div class=\"component-list\">\n              <div \n                v-for=\"component in basicComponents\" \n                :key=\"component.type\"\n                class=\"component-item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, component)\"\n              >\n                <i :class=\"component.icon\"></i>\n                <span>{{ component.label }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 高级组件 -->\n          <div class=\"component-group\">\n            <div class=\"group-title\">高级组件</div>\n            <div class=\"component-list\">\n              <div \n                v-for=\"component in advancedComponents\" \n                :key=\"component.type\"\n                class=\"component-item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, component)\"\n              >\n                <i :class=\"component.icon\"></i>\n                <span>{{ component.label }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 布局组件 -->\n          <div class=\"component-group\">\n            <div class=\"group-title\">布局组件</div>\n            <div class=\"component-list\">\n              <div \n                v-for=\"component in layoutComponents\" \n                :key=\"component.type\"\n                class=\"component-item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, component)\"\n              >\n                <i :class=\"component.icon\"></i>\n                <span>{{ component.label }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 中间设计区域 -->\n      <div class=\"design-panel\">\n        <div class=\"panel-title\">表单设计区域</div>\n        <div \n          class=\"design-canvas\"\n          @drop=\"handleDrop\"\n          @dragover=\"handleDragOver\"\n          @click=\"clearSelection\"\n        >\n          <div v-if=\"formItems.length === 0\" class=\"empty-canvas\">\n            <i class=\"el-icon-plus\"></i>\n            <p>从左侧拖拽组件到此处开始设计表单</p>\n          </div>\n          \n          <draggable \n            v-model=\"formItems\" \n            group=\"form-items\"\n            :animation=\"200\"\n            ghost-class=\"ghost\"\n            chosen-class=\"chosen\"\n            @end=\"handleSortEnd\"\n          >\n            <form-item-wrapper\n              v-for=\"(item, index) in formItems\"\n              :key=\"item.id\"\n              :item=\"item\"\n              :index=\"index\"\n              :selected=\"selectedItem && selectedItem.id === item.id\"\n              @select=\"handleSelectItem\"\n              @delete=\"handleDeleteItem\"\n              @clone=\"handleCloneItem\"\n            />\n          </draggable>\n        </div>\n      </div>\n\n      <!-- 右侧属性面板 -->\n      <div class=\"properties-panel\">\n        <div class=\"panel-title\">属性配置</div>\n        <div class=\"properties-content\">\n          <form-properties\n            v-if=\"selectedItem\"\n            :item=\"selectedItem\"\n            @update=\"handleUpdateProperties\"\n          />\n          <div v-else class=\"no-selection\">\n            <i class=\"el-icon-info\"></i>\n            <p>请选择一个组件进行配置</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewVisible\" width=\"80%\" append-to-body>\n      <form-preview :form-items=\"formItems\" />\n    </el-dialog>\n\n    <!-- JSON导入对话框 -->\n    <el-dialog title=\"导入JSON\" :visible.sync=\"importVisible\" width=\"60%\" append-to-body>\n      <el-input\n        v-model=\"importJson\"\n        type=\"textarea\"\n        :rows=\"10\"\n        placeholder=\"请粘贴表单JSON配置\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"importVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleImportConfirm\">确定导入</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport draggable from 'vuedraggable'\nimport FormItemWrapper from './components/FormItemWrapper.vue'\nimport FormProperties from './components/FormProperties.vue'\nimport FormPreview from './components/FormPreview.vue'\nimport { generateId } from './utils/index.js'\nimport { basicComponents, advancedComponents, layoutComponents } from './config/components.js'\n\nexport default {\n  name: 'FormDesigner',\n  components: {\n    draggable,\n    FormItemWrapper,\n    FormProperties,\n    FormPreview\n  },\n  props: {\n    formData: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      formConfig: {\n        formName: '',\n        remark: ''\n      },\n      formItems: [],\n      selectedItem: null,\n      previewVisible: false,\n      importVisible: false,\n      importJson: '',\n      basicComponents,\n      advancedComponents,\n      layoutComponents\n    }\n  },\n  created() {\n    this.initFormData()\n  },\n  methods: {\n    initFormData() {\n      if (this.formData.formId) {\n        this.formConfig.formName = this.formData.formName || ''\n        this.formConfig.remark = this.formData.remark || ''\n        \n        // 解析表单内容\n        if (this.formData.formContent) {\n          try {\n            const content = JSON.parse(this.formData.formContent)\n            this.formItems = content.formItems || []\n          } catch (e) {\n            console.warn('表单内容解析失败:', e)\n          }\n        }\n      }\n    },\n\n    handleDragStart(event, component) {\n      event.dataTransfer.setData('component', JSON.stringify(component))\n    },\n\n    handleDragOver(event) {\n      event.preventDefault()\n    },\n\n    handleDrop(event) {\n      event.preventDefault()\n      const componentData = event.dataTransfer.getData('component')\n      if (componentData) {\n        const component = JSON.parse(componentData)\n        this.addFormItem(component)\n      }\n    },\n\n    addFormItem(component) {\n      const newItem = {\n        id: generateId(),\n        type: component.type,\n        label: component.label,\n        icon: component.icon,\n        ...component.defaultProps\n      }\n      this.formItems.push(newItem)\n      this.selectedItem = newItem\n    },\n\n    handleSelectItem(item) {\n      this.selectedItem = item\n    },\n\n    handleDeleteItem(index) {\n      this.formItems.splice(index, 1)\n      if (this.selectedItem && this.selectedItem.id === this.formItems[index]?.id) {\n        this.selectedItem = null\n      }\n    },\n\n    handleCloneItem(item) {\n      const clonedItem = {\n        ...JSON.parse(JSON.stringify(item)),\n        id: generateId()\n      }\n      const index = this.formItems.findIndex(i => i.id === item.id)\n      this.formItems.splice(index + 1, 0, clonedItem)\n    },\n\n    handleUpdateProperties(properties) {\n      if (this.selectedItem) {\n        Object.assign(this.selectedItem, properties)\n      }\n    },\n\n    handleSortEnd() {\n      // 拖拽排序完成后的处理\n    },\n\n    clearSelection() {\n      this.selectedItem = null\n    },\n\n    handleSave() {\n      if (!this.formConfig.formName) {\n        this.$message.error('请输入表单名称')\n        return\n      }\n\n      const formData = {\n        ...this.formData,\n        formName: this.formConfig.formName,\n        remark: this.formConfig.remark,\n        formContent: JSON.stringify({\n          formItems: this.formItems,\n          config: this.formConfig\n        })\n      }\n\n      this.$emit('save', formData)\n    },\n\n    handleCancel() {\n      this.$emit('cancel')\n    },\n\n    handlePreview() {\n      this.previewVisible = true\n    },\n\n    handleExportJson() {\n      const exportData = {\n        formItems: this.formItems,\n        config: this.formConfig\n      }\n      \n      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })\n      const url = URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = `${this.formConfig.formName || 'form'}.json`\n      a.click()\n      URL.revokeObjectURL(url)\n    },\n\n    handleImportJson() {\n      this.importVisible = true\n      this.importJson = ''\n    },\n\n    handleImportConfirm() {\n      try {\n        const data = JSON.parse(this.importJson)\n        if (data.formItems) {\n          this.formItems = data.formItems\n        }\n        if (data.config) {\n          this.formConfig = { ...this.formConfig, ...data.config }\n        }\n        this.importVisible = false\n        this.$message.success('导入成功')\n      } catch (e) {\n        this.$message.error('JSON格式错误，请检查后重试')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-designer {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n\n  .designer-header {\n    background: #fff;\n    padding: 10px 20px;\n    border-bottom: 1px solid #e4e7ed;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  }\n\n  .designer-body {\n    flex: 1;\n    display: flex;\n    height: calc(100vh - 80px);\n    overflow: hidden;\n\n    .components-panel {\n      width: 260px;\n      background: #fff;\n      border-right: 1px solid #e4e7ed;\n      overflow-y: auto;\n\n      .panel-title {\n        padding: 15px;\n        font-size: 16px;\n        font-weight: bold;\n        color: #303133;\n        border-bottom: 1px solid #e4e7ed;\n        background: #fafafa;\n      }\n\n      .component-groups {\n        .component-group {\n          .group-title {\n            padding: 10px 15px;\n            font-size: 14px;\n            font-weight: 600;\n            color: #606266;\n            background: #f8f9fa;\n            border-bottom: 1px solid #e4e7ed;\n          }\n\n          .component-list {\n            padding: 10px;\n\n            .component-item {\n              display: flex;\n              align-items: center;\n              padding: 8px 12px;\n              margin-bottom: 8px;\n              background: #fff;\n              border: 1px solid #e4e7ed;\n              border-radius: 4px;\n              cursor: grab;\n              transition: all 0.3s;\n\n              &:hover {\n                border-color: #409EFF;\n                background: #f0f9ff;\n                transform: translateY(-1px);\n                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n              }\n\n              &:active {\n                cursor: grabbing;\n              }\n\n              i {\n                margin-right: 8px;\n                font-size: 16px;\n                color: #409EFF;\n              }\n\n              span {\n                font-size: 13px;\n                color: #606266;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .design-panel {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      background: #fff;\n      margin: 0 1px;\n\n      .panel-title {\n        padding: 15px;\n        font-size: 16px;\n        font-weight: bold;\n        color: #303133;\n        border-bottom: 1px solid #e4e7ed;\n        background: #fafafa;\n      }\n\n      .design-canvas {\n        flex: 1;\n        padding: 20px;\n        overflow-y: auto;\n        min-height: 400px;\n        position: relative;\n\n        .empty-canvas {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          height: 300px;\n          border: 2px dashed #c0c4cc;\n          border-radius: 8px;\n          color: #909399;\n          background: #fafbfc;\n\n          i {\n            font-size: 48px;\n            margin-bottom: 16px;\n            color: #c0c4cc;\n          }\n\n          p {\n            font-size: 14px;\n            margin: 0;\n          }\n        }\n\n        .ghost {\n          opacity: 0.5;\n          background: #409EFF;\n        }\n\n        .chosen {\n          border: 2px solid #409EFF !important;\n        }\n      }\n    }\n\n    .properties-panel {\n      width: 320px;\n      background: #fff;\n      border-left: 1px solid #e4e7ed;\n      display: flex;\n      flex-direction: column;\n\n      .panel-title {\n        padding: 15px;\n        font-size: 16px;\n        font-weight: bold;\n        color: #303133;\n        border-bottom: 1px solid #e4e7ed;\n        background: #fafafa;\n      }\n\n      .properties-content {\n        flex: 1;\n        overflow-y: auto;\n\n        .no-selection {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          height: 200px;\n          color: #909399;\n\n          i {\n            font-size: 48px;\n            margin-bottom: 16px;\n            color: #c0c4cc;\n          }\n\n          p {\n            font-size: 14px;\n            margin: 0;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 全局样式\n:deep(.el-tabs--border-card) {\n  border: none;\n  box-shadow: none;\n\n  .el-tabs__header {\n    background: #fafafa;\n    border-bottom: 1px solid #e4e7ed;\n    margin: 0;\n  }\n\n  .el-tabs__content {\n    padding: 15px;\n  }\n}\n\n:deep(.el-form-item) {\n  margin-bottom: 15px;\n}\n\n:deep(.el-input), :deep(.el-select), :deep(.el-date-editor) {\n  width: 100%;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAyJA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,eAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,YAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAM,IAAA;EACAC,UAAA;IACAC,SAAA,EAAAA,qBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACAC,SAAA;MACAC,YAAA;MACAC,cAAA;MACAC,aAAA;MACAC,UAAA;MACAC,eAAA,EAAAA,2BAAA;MACAC,kBAAA,EAAAA,8BAAA;MACAC,gBAAA,EAAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACAD,YAAA,WAAAA,aAAA;MACA,SAAAjB,QAAA,CAAAmB,MAAA;QACA,KAAAd,UAAA,CAAAC,QAAA,QAAAN,QAAA,CAAAM,QAAA;QACA,KAAAD,UAAA,CAAAE,MAAA,QAAAP,QAAA,CAAAO,MAAA;;QAEA;QACA,SAAAP,QAAA,CAAAoB,WAAA;UACA;YACA,IAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,MAAAvB,QAAA,CAAAoB,WAAA;YACA,KAAAZ,SAAA,GAAAa,OAAA,CAAAb,SAAA;UACA,SAAAgB,CAAA;YACAC,OAAA,CAAAC,IAAA,cAAAF,CAAA;UACA;QACA;MACA;IACA;IAEAG,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,SAAA;MACAD,KAAA,CAAAE,YAAA,CAAAC,OAAA,cAAAT,IAAA,CAAAU,SAAA,CAAAH,SAAA;IACA;IAEAI,cAAA,WAAAA,eAAAL,KAAA;MACAA,KAAA,CAAAM,cAAA;IACA;IAEAC,UAAA,WAAAA,WAAAP,KAAA;MACAA,KAAA,CAAAM,cAAA;MACA,IAAAE,aAAA,GAAAR,KAAA,CAAAE,YAAA,CAAAO,OAAA;MACA,IAAAD,aAAA;QACA,IAAAP,SAAA,GAAAP,IAAA,CAAAC,KAAA,CAAAa,aAAA;QACA,KAAAE,WAAA,CAAAT,SAAA;MACA;IACA;IAEAS,WAAA,WAAAA,YAAAT,SAAA;MACA,IAAAU,OAAA,OAAAC,cAAA,CAAArC,OAAA;QACAsC,EAAA,MAAAC,iBAAA;QACAzC,IAAA,EAAA4B,SAAA,CAAA5B,IAAA;QACA0C,KAAA,EAAAd,SAAA,CAAAc,KAAA;QACAC,IAAA,EAAAf,SAAA,CAAAe;MAAA,GACAf,SAAA,CAAAgB,YAAA,CACA;MACA,KAAArC,SAAA,CAAAsC,IAAA,CAAAP,OAAA;MACA,KAAA9B,YAAA,GAAA8B,OAAA;IACA;IAEAQ,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAvC,YAAA,GAAAuC,IAAA;IACA;IAEAC,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,qBAAA;MACA,KAAA3C,SAAA,CAAA4C,MAAA,CAAAF,KAAA;MACA,SAAAzC,YAAA,SAAAA,YAAA,CAAAgC,EAAA,OAAAU,qBAAA,QAAA3C,SAAA,CAAA0C,KAAA,eAAAC,qBAAA,uBAAAA,qBAAA,CAAAV,EAAA;QACA,KAAAhC,YAAA;MACA;IACA;IAEA4C,eAAA,WAAAA,gBAAAL,IAAA;MACA,IAAAM,UAAA,OAAAd,cAAA,CAAArC,OAAA,MAAAqC,cAAA,CAAArC,OAAA,MACAmB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAU,SAAA,CAAAgB,IAAA;QACAP,EAAA,MAAAC,iBAAA;MAAA,EACA;MACA,IAAAQ,KAAA,QAAA1C,SAAA,CAAA+C,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAf,EAAA,KAAAO,IAAA,CAAAP,EAAA;MAAA;MACA,KAAAjC,SAAA,CAAA4C,MAAA,CAAAF,KAAA,SAAAI,UAAA;IACA;IAEAG,sBAAA,WAAAA,uBAAAC,UAAA;MACA,SAAAjD,YAAA;QACAP,MAAA,CAAAyD,MAAA,MAAAlD,YAAA,EAAAiD,UAAA;MACA;IACA;IAEAE,aAAA,WAAAA,cAAA;MACA;IAAA,CACA;IAEAC,cAAA,WAAAA,eAAA;MACA,KAAApD,YAAA;IACA;IAEAqD,UAAA,WAAAA,WAAA;MACA,UAAAzD,UAAA,CAAAC,QAAA;QACA,KAAAyD,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,IAAAhE,QAAA,OAAAwC,cAAA,CAAArC,OAAA,MAAAqC,cAAA,CAAArC,OAAA,MACA,KAAAH,QAAA;QACAM,QAAA,OAAAD,UAAA,CAAAC,QAAA;QACAC,MAAA,OAAAF,UAAA,CAAAE,MAAA;QACAa,WAAA,EAAAE,IAAA,CAAAU,SAAA;UACAxB,SAAA,OAAAA,SAAA;UACAyD,MAAA,OAAA5D;QACA;MAAA,EACA;MAEA,KAAA6D,KAAA,SAAAlE,QAAA;IACA;IAEAmE,YAAA,WAAAA,aAAA;MACA,KAAAD,KAAA;IACA;IAEAE,aAAA,WAAAA,cAAA;MACA,KAAA1D,cAAA;IACA;IAEA2D,gBAAA,WAAAA,iBAAA;MACA,IAAAC,UAAA;QACA9D,SAAA,OAAAA,SAAA;QACAyD,MAAA,OAAA5D;MACA;MAEA,IAAAkE,IAAA,OAAAC,IAAA,EAAAlD,IAAA,CAAAU,SAAA,CAAAsC,UAAA;QAAArE,IAAA;MAAA;MACA,IAAAwE,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,IAAA;MACA,IAAAK,CAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,CAAA,CAAAG,IAAA,GAAAN,GAAA;MACAG,CAAA,CAAAI,QAAA,MAAAC,MAAA,MAAA5E,UAAA,CAAAC,QAAA;MACAsE,CAAA,CAAAM,KAAA;MACAR,GAAA,CAAAS,eAAA,CAAAV,GAAA;IACA;IAEAW,gBAAA,WAAAA,iBAAA;MACA,KAAAzE,aAAA;MACA,KAAAC,UAAA;IACA;IAEAyE,mBAAA,WAAAA,oBAAA;MACA;QACA,IAAAjF,IAAA,GAAAkB,IAAA,CAAAC,KAAA,MAAAX,UAAA;QACA,IAAAR,IAAA,CAAAI,SAAA;UACA,KAAAA,SAAA,GAAAJ,IAAA,CAAAI,SAAA;QACA;QACA,IAAAJ,IAAA,CAAA6D,MAAA;UACA,KAAA5D,UAAA,OAAAmC,cAAA,CAAArC,OAAA,MAAAqC,cAAA,CAAArC,OAAA,WAAAE,UAAA,GAAAD,IAAA,CAAA6D,MAAA;QACA;QACA,KAAAtD,aAAA;QACA,KAAAoD,QAAA,CAAAuB,OAAA;MACA,SAAA9D,CAAA;QACA,KAAAuC,QAAA,CAAAC,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}