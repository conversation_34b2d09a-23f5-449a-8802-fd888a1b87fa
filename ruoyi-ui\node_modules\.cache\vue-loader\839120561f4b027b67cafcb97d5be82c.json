{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormProperties.vue?vue&type=template&id=18484580&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormProperties.vue", "mtime": 1752386494977}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752199741460}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}