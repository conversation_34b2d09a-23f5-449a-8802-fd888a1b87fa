{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeForm\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeForm\\index.vue", "mtime": 1752410027356}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTm9kZUZvcm0nLAogIHByb3BzOiB7CiAgICB2YWx1ZTogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0sCiAgICB0aXRsZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgcmVhZG9ubHk6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmb3JtRmllbGRzOiBbXSwKICAgICAgc2hvd0ZpZWxkRGlhbG9nOiBmYWxzZSwKICAgICAgbmV3RmllbGQ6IHsKICAgICAgICBsYWJlbDogJycsCiAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgIHBsYWNlaG9sZGVyOiAnJywKICAgICAgICB2YWx1ZTogJycsCiAgICAgICAgb3B0aW9uczogW10KICAgICAgfQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZhbHVlOiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgdGhpcy5mb3JtRmllbGRzID0gbmV3VmFsIHx8IFtdOwogICAgICB9LAogICAgICBpbW1lZGlhdGU6IHRydWUsCiAgICAgIGRlZXA6IHRydWUKICAgIH0sCiAgICBmb3JtRmllbGRzOiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgdGhpcy4kZW1pdCgnaW5wdXQnLCBuZXdWYWwpOwogICAgICB9LAogICAgICBkZWVwOiB0cnVlCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5re75Yqg5a2X5q61ICovCiAgICBhZGRGaWVsZCgpIHsKICAgICAgdGhpcy5uZXdGaWVsZCA9IHsKICAgICAgICBsYWJlbDogJycsCiAgICAgICAgdHlwZTogJ3RleHQnLAogICAgICAgIHBsYWNlaG9sZGVyOiAnJywKICAgICAgICB2YWx1ZTogJycsCiAgICAgICAgb3B0aW9uczogW10KICAgICAgfTsKICAgICAgdGhpcy5zaG93RmllbGREaWFsb2cgPSB0cnVlOwogICAgfSwKCiAgICAvKiog56Gu6K6k5re75Yqg5a2X5q61ICovCiAgICBjb25maXJtQWRkRmllbGQoKSB7CiAgICAgIGlmICghdGhpcy5uZXdGaWVsZC5sYWJlbCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl5a2X5q615qCH562+Jyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICBjb25zdCBmaWVsZCA9IHsKICAgICAgICBpZDogRGF0ZS5ub3coKSwKICAgICAgICBsYWJlbDogdGhpcy5uZXdGaWVsZC5sYWJlbCwKICAgICAgICB0eXBlOiB0aGlzLm5ld0ZpZWxkLnR5cGUsCiAgICAgICAgcGxhY2Vob2xkZXI6IHRoaXMubmV3RmllbGQucGxhY2Vob2xkZXIsCiAgICAgICAgdmFsdWU6IHRoaXMuZ2V0RGVmYXVsdFZhbHVlKHRoaXMubmV3RmllbGQudHlwZSksCiAgICAgICAgb3B0aW9uczogWy4uLnRoaXMubmV3RmllbGQub3B0aW9uc10KICAgICAgfTsKCiAgICAgIHRoaXMuZm9ybUZpZWxkcy5wdXNoKGZpZWxkKTsKICAgICAgdGhpcy5zaG93RmllbGREaWFsb2cgPSBmYWxzZTsKICAgIH0sCgogICAgLyoqIOenu+mZpOWtl+autSAqLwogICAgcmVtb3ZlRmllbGQoaW5kZXgpIHsKICAgICAgdGhpcy5mb3JtRmllbGRzLnNwbGljZShpbmRleCwgMSk7CiAgICB9LAoKICAgIC8qKiDmt7vliqDpgInpobkgKi8KICAgIGFkZE9wdGlvbigpIHsKICAgICAgdGhpcy5uZXdGaWVsZC5vcHRpb25zLnB1c2goeyBsYWJlbDogJycsIHZhbHVlOiAnJyB9KTsKICAgIH0sCgogICAgLyoqIOenu+mZpOmAiemhuSAqLwogICAgcmVtb3ZlT3B0aW9uKGluZGV4KSB7CiAgICAgIHRoaXMubmV3RmllbGQub3B0aW9ucy5zcGxpY2UoaW5kZXgsIDEpOwogICAgfSwKCiAgICAvKiog6I635Y+W5a2X5q6157G75Z6L5paH5pysICovCiAgICBnZXRGaWVsZFR5cGVUZXh0KHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICB0ZXh0OiAn5paH5pysJywKICAgICAgICB0ZXh0YXJlYTogJ+WkmuihjOaWh+acrCcsCiAgICAgICAgbnVtYmVyOiAn5pWw5a2XJywKICAgICAgICBzZWxlY3Q6ICfkuIvmi4npgInmi6knLAogICAgICAgIGRhdGU6ICfml6XmnJ8nLAogICAgICAgIHN3aXRjaDogJ+W8gOWFsycsCiAgICAgICAgcmFkaW86ICfljZXpgIknLAogICAgICAgIGNoZWNrYm94OiAn5aSa6YCJJwogICAgICB9OwogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCB0eXBlOwogICAgfSwKCiAgICAvKiog6I635Y+W6buY6K6k5YC8ICovCiAgICBnZXREZWZhdWx0VmFsdWUodHlwZSkgewogICAgICBzd2l0Y2ggKHR5cGUpIHsKICAgICAgICBjYXNlICdudW1iZXInOgogICAgICAgICAgcmV0dXJuIDA7CiAgICAgICAgY2FzZSAnc3dpdGNoJzoKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICBjYXNlICdjaGVja2JveCc6CiAgICAgICAgICByZXR1cm4gW107CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIHJldHVybiAnJzsKICAgICAgfQogICAgfSwKCiAgICAvKiog6I635Y+W6KGo5Y2V5pWw5o2uICovCiAgICBnZXRGb3JtRGF0YSgpIHsKICAgICAgY29uc3QgZGF0YSA9IHt9OwogICAgICB0aGlzLmZvcm1GaWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7CiAgICAgICAgZGF0YVtmaWVsZC5sYWJlbF0gPSBmaWVsZC52YWx1ZTsKICAgICAgfSk7CiAgICAgIHJldHVybiBkYXRhOwogICAgfSwKCiAgICAvKiog6K6+572u6KGo5Y2V5pWw5o2uICovCiAgICBzZXRGb3JtRGF0YShkYXRhKSB7CiAgICAgIHRoaXMuZm9ybUZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsKICAgICAgICBpZiAoZGF0YS5oYXNPd25Qcm9wZXJ0eShmaWVsZC5sYWJlbCkpIHsKICAgICAgICAgIGZpZWxkLnZhbHVlID0gZGF0YVtmaWVsZC5sYWJlbF07CiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/NodeForm", "sourcesContent": ["<template>\n  <div class=\"node-form-container\">\n    <div class=\"form-header\">\n      <h4>\n        <i class=\"el-icon-edit-outline\"></i>\n        {{ title || '节点表单' }}\n      </h4>\n      <el-button \n        v-if=\"!readonly\" \n        type=\"primary\" \n        size=\"small\" \n        @click=\"addField\"\n      >\n        添加字段\n      </el-button>\n    </div>\n\n    <div class=\"form-content\">\n      <!-- 表单字段列表 -->\n      <div v-if=\"formFields.length > 0\" class=\"field-list\">\n        <div \n          v-for=\"(field, index) in formFields\" \n          :key=\"field.id || index\"\n          class=\"field-item\"\n        >\n          <div class=\"field-header\">\n            <span class=\"field-label\">{{ field.label }}</span>\n            <span class=\"field-type\">{{ getFieldTypeText(field.type) }}</span>\n            <el-button \n              v-if=\"!readonly\" \n              type=\"text\" \n              size=\"mini\" \n              @click=\"removeField(index)\"\n              class=\"remove-btn\"\n            >\n              删除\n            </el-button>\n          </div>\n          \n          <div class=\"field-content\">\n            <!-- 文本输入 -->\n            <el-input \n              v-if=\"field.type === 'text'\" \n              v-model=\"field.value\"\n              :placeholder=\"field.placeholder\"\n              :readonly=\"readonly\"\n            />\n            \n            <!-- 多行文本 -->\n            <el-input \n              v-else-if=\"field.type === 'textarea'\" \n              v-model=\"field.value\"\n              type=\"textarea\"\n              :rows=\"3\"\n              :placeholder=\"field.placeholder\"\n              :readonly=\"readonly\"\n            />\n            \n            <!-- 数字输入 -->\n            <el-input-number \n              v-else-if=\"field.type === 'number'\" \n              v-model=\"field.value\"\n              :placeholder=\"field.placeholder\"\n              :readonly=\"readonly\"\n              style=\"width: 100%\"\n            />\n            \n            <!-- 选择器 -->\n            <el-select \n              v-else-if=\"field.type === 'select'\" \n              v-model=\"field.value\"\n              :placeholder=\"field.placeholder\"\n              :disabled=\"readonly\"\n              style=\"width: 100%\"\n            >\n              <el-option \n                v-for=\"option in field.options\" \n                :key=\"option.value\"\n                :label=\"option.label\"\n                :value=\"option.value\"\n              />\n            </el-select>\n            \n            <!-- 日期选择 -->\n            <el-date-picker \n              v-else-if=\"field.type === 'date'\" \n              v-model=\"field.value\"\n              type=\"date\"\n              :placeholder=\"field.placeholder\"\n              :readonly=\"readonly\"\n              style=\"width: 100%\"\n            />\n            \n            <!-- 开关 -->\n            <el-switch \n              v-else-if=\"field.type === 'switch'\" \n              v-model=\"field.value\"\n              :disabled=\"readonly\"\n            />\n            \n            <!-- 单选框组 -->\n            <el-radio-group \n              v-else-if=\"field.type === 'radio'\" \n              v-model=\"field.value\"\n              :disabled=\"readonly\"\n            >\n              <el-radio \n                v-for=\"option in field.options\" \n                :key=\"option.value\"\n                :label=\"option.value\"\n              >\n                {{ option.label }}\n              </el-radio>\n            </el-radio-group>\n            \n            <!-- 复选框组 -->\n            <el-checkbox-group \n              v-else-if=\"field.type === 'checkbox'\" \n              v-model=\"field.value\"\n              :disabled=\"readonly\"\n            >\n              <el-checkbox \n                v-for=\"option in field.options\" \n                :key=\"option.value\"\n                :label=\"option.value\"\n              >\n                {{ option.label }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 空状态 -->\n      <div v-else class=\"empty-state\">\n        <i class=\"el-icon-document-add\"></i>\n        <p>{{ readonly ? '暂无表单数据' : '点击\"添加字段\"开始创建表单' }}</p>\n      </div>\n    </div>\n\n    <!-- 字段配置对话框 -->\n    <el-dialog \n      title=\"添加表单字段\" \n      :visible.sync=\"showFieldDialog\"\n      width=\"500px\"\n    >\n      <el-form :model=\"newField\" label-width=\"80px\">\n        <el-form-item label=\"字段标签\">\n          <el-input v-model=\"newField.label\" placeholder=\"请输入字段标签\" />\n        </el-form-item>\n        \n        <el-form-item label=\"字段类型\">\n          <el-select v-model=\"newField.type\" style=\"width: 100%\">\n            <el-option label=\"单行文本\" value=\"text\" />\n            <el-option label=\"多行文本\" value=\"textarea\" />\n            <el-option label=\"数字\" value=\"number\" />\n            <el-option label=\"下拉选择\" value=\"select\" />\n            <el-option label=\"日期\" value=\"date\" />\n            <el-option label=\"开关\" value=\"switch\" />\n            <el-option label=\"单选框\" value=\"radio\" />\n            <el-option label=\"复选框\" value=\"checkbox\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"占位符\">\n          <el-input v-model=\"newField.placeholder\" placeholder=\"请输入占位符文本\" />\n        </el-form-item>\n        \n        <el-form-item \n          v-if=\"['select', 'radio', 'checkbox'].includes(newField.type)\" \n          label=\"选项配置\"\n        >\n          <div class=\"options-config\">\n            <div \n              v-for=\"(option, index) in newField.options\" \n              :key=\"index\"\n              class=\"option-item\"\n            >\n              <el-input \n                v-model=\"option.label\" \n                placeholder=\"选项标签\" \n                style=\"width: 45%; margin-right: 10px;\"\n              />\n              <el-input \n                v-model=\"option.value\" \n                placeholder=\"选项值\" \n                style=\"width: 35%; margin-right: 10px;\"\n              />\n              <el-button \n                type=\"text\" \n                @click=\"removeOption(index)\"\n                style=\"color: #f56c6c;\"\n              >\n                删除\n              </el-button>\n            </div>\n            <el-button type=\"text\" @click=\"addOption\">+ 添加选项</el-button>\n          </div>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\">\n        <el-button @click=\"showFieldDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmAddField\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'NodeForm',\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    readonly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      formFields: [],\n      showFieldDialog: false,\n      newField: {\n        label: '',\n        type: 'text',\n        placeholder: '',\n        value: '',\n        options: []\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        this.formFields = newVal || [];\n      },\n      immediate: true,\n      deep: true\n    },\n    formFields: {\n      handler(newVal) {\n        this.$emit('input', newVal);\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 添加字段 */\n    addField() {\n      this.newField = {\n        label: '',\n        type: 'text',\n        placeholder: '',\n        value: '',\n        options: []\n      };\n      this.showFieldDialog = true;\n    },\n\n    /** 确认添加字段 */\n    confirmAddField() {\n      if (!this.newField.label) {\n        this.$message.warning('请输入字段标签');\n        return;\n      }\n\n      const field = {\n        id: Date.now(),\n        label: this.newField.label,\n        type: this.newField.type,\n        placeholder: this.newField.placeholder,\n        value: this.getDefaultValue(this.newField.type),\n        options: [...this.newField.options]\n      };\n\n      this.formFields.push(field);\n      this.showFieldDialog = false;\n    },\n\n    /** 移除字段 */\n    removeField(index) {\n      this.formFields.splice(index, 1);\n    },\n\n    /** 添加选项 */\n    addOption() {\n      this.newField.options.push({ label: '', value: '' });\n    },\n\n    /** 移除选项 */\n    removeOption(index) {\n      this.newField.options.splice(index, 1);\n    },\n\n    /** 获取字段类型文本 */\n    getFieldTypeText(type) {\n      const typeMap = {\n        text: '文本',\n        textarea: '多行文本',\n        number: '数字',\n        select: '下拉选择',\n        date: '日期',\n        switch: '开关',\n        radio: '单选',\n        checkbox: '多选'\n      };\n      return typeMap[type] || type;\n    },\n\n    /** 获取默认值 */\n    getDefaultValue(type) {\n      switch (type) {\n        case 'number':\n          return 0;\n        case 'switch':\n          return false;\n        case 'checkbox':\n          return [];\n        default:\n          return '';\n      }\n    },\n\n    /** 获取表单数据 */\n    getFormData() {\n      const data = {};\n      this.formFields.forEach(field => {\n        data[field.label] = field.value;\n      });\n      return data;\n    },\n\n    /** 设置表单数据 */\n    setFormData(data) {\n      this.formFields.forEach(field => {\n        if (data.hasOwnProperty(field.label)) {\n          field.value = data[field.label];\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.node-form-container {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-header {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  border-bottom: 1px solid #EBEEF5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h4 {\n    margin: 0;\n    color: #606266;\n    font-size: 14px;\n    font-weight: 600;\n\n    i {\n      margin-right: 8px;\n      color: #409EFF;\n    }\n  }\n}\n\n.form-content {\n  padding: 16px;\n  background-color: white;\n}\n\n.field-list {\n  .field-item {\n    margin-bottom: 16px;\n    border: 1px solid #E4E7ED;\n    border-radius: 4px;\n    overflow: hidden;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .field-header {\n    background-color: #FAFAFA;\n    padding: 8px 12px;\n    border-bottom: 1px solid #E4E7ED;\n    display: flex;\n    align-items: center;\n\n    .field-label {\n      font-weight: 600;\n      color: #303133;\n      flex: 1;\n    }\n\n    .field-type {\n      color: #909399;\n      font-size: 12px;\n      margin-right: 10px;\n    }\n\n    .remove-btn {\n      color: #F56C6C;\n    }\n  }\n\n  .field-content {\n    padding: 12px;\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n\n  i {\n    font-size: 48px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.options-config {\n  .option-item {\n    display: flex;\n    align-items: center;\n    margin-bottom: 8px;\n  }\n}\n</style>\n"]}]}