{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\system\\form\\designer.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\system\\form\\designer.vue", "mtime": 1752386648060}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "name", "components", "FormDesigner", "data", "formData", "formId", "formName", "formContent", "remark", "created", "$route", "query", "loadFormData", "methods", "handleSave", "console", "log", "$message", "success", "handleCancel", "$router", "go"], "sources": ["src/views/system/form/designer.vue"], "sourcesContent": ["<template>\n  <div class=\"form-designer-page\">\n    <form-designer \n      :form-data=\"formData\"\n      @save=\"handleSave\"\n      @cancel=\"handleCancel\"\n    />\n  </div>\n</template>\n\n<script>\nimport FormDesigner from '@/components/FormDesigner/index.vue'\n\nexport default {\n  name: 'FormDesignerPage',\n  components: {\n    FormDesigner\n  },\n  data() {\n    return {\n      formData: {\n        formId: null,\n        formName: '新建表单',\n        formContent: '',\n        remark: ''\n      }\n    }\n  },\n  created() {\n    // 如果有传入的表单ID，则加载表单数据\n    const formId = this.$route.query.formId\n    if (formId) {\n      this.loadFormData(formId)\n    }\n  },\n  methods: {\n    loadFormData(formId) {\n      // 这里可以调用API加载表单数据\n      // getForm(formId).then(response => {\n      //   this.formData = response.data\n      // })\n    },\n    \n    handleSave(formData) {\n      console.log('保存表单数据:', formData)\n      this.$message.success('表单保存成功！')\n      // 这里可以调用API保存表单\n      // if (formData.formId) {\n      //   updateForm(formData).then(() => {\n      //     this.$message.success('表单更新成功')\n      //     this.$router.go(-1)\n      //   })\n      // } else {\n      //   addForm(formData).then(() => {\n      //     this.$message.success('表单创建成功')\n      //     this.$router.go(-1)\n      //   })\n      // }\n    },\n    \n    handleCancel() {\n      this.$router.go(-1)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-designer-page {\n  height: 100vh;\n  overflow: hidden;\n}\n</style>\n"], "mappings": ";;;;;;;AAWA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,WAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,IAAAJ,MAAA,QAAAK,MAAA,CAAAC,KAAA,CAAAN,MAAA;IACA,IAAAA,MAAA;MACA,KAAAO,YAAA,CAAAP,MAAA;IACA;EACA;EACAQ,OAAA;IACAD,YAAA,WAAAA,aAAAP,MAAA;MACA;MACA;MACA;MACA;IAAA,CACA;IAEAS,UAAA,WAAAA,WAAAV,QAAA;MACAW,OAAA,CAAAC,GAAA,YAAAZ,QAAA;MACA,KAAAa,QAAA,CAAAC,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC,YAAA,WAAAA,aAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}