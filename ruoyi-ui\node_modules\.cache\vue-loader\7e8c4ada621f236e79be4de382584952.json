{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormPreview.vue?vue&type=style&index=0&id=25976f8b&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormPreview.vue", "mtime": 1752386549087}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmZvcm0tcHJldmlldyB7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICAKICAudXBsb2FkLWRyYWctYXJlYSB7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICBwYWRkaW5nOiAyMHB4OwogICAgYm9yZGVyOiAxcHggZGFzaGVkICNkOWQ5ZDk7CiAgICBib3JkZXItcmFkaXVzOiA2cHg7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAKICAgICY6aG92ZXIgewogICAgICBib3JkZXItY29sb3I6ICM0MDlFRkY7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["FormPreview.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgbA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "FormPreview.vue", "sourceRoot": "src/components/FormDesigner/components", "sourcesContent": ["<template>\n  <div class=\"form-preview\">\n    <el-form \n      ref=\"previewForm\"\n      :model=\"formModel\" \n      :rules=\"formRules\"\n      label-width=\"120px\"\n      size=\"medium\"\n    >\n      <el-row :gutter=\"20\">\n        <template v-for=\"item in formItems\">\n          <!-- 普通表单项 -->\n          <el-col :key=\"item.id\" :span=\"item.span || 24\" v-if=\"!isLayoutComponent(item.type)\">\n            <el-form-item \n              :label=\"item.label\"\n              :prop=\"item.id\"\n              :label-width=\"item.labelWidth\"\n              :required=\"item.required\"\n            >\n              <!-- 单行文本 -->\n              <el-input\n                v-if=\"item.type === 'input'\"\n                v-model=\"formModel[item.id]\"\n                :placeholder=\"item.placeholder\"\n                :clearable=\"item.clearable\"\n                :disabled=\"item.disabled\"\n                :readonly=\"item.readonly\"\n                :maxlength=\"item.maxlength\"\n                :show-word-limit=\"item.showWordLimit\"\n                :prefix-icon=\"item.prefixIcon\"\n                :suffix-icon=\"item.suffixIcon\"\n                :style=\"item.style\"\n              />\n\n              <!-- 多行文本 -->\n              <el-input\n                v-else-if=\"item.type === 'textarea'\"\n                v-model=\"formModel[item.id]\"\n                type=\"textarea\"\n                :placeholder=\"item.placeholder\"\n                :rows=\"item.rows\"\n                :autosize=\"item.autosize\"\n                :disabled=\"item.disabled\"\n                :readonly=\"item.readonly\"\n                :maxlength=\"item.maxlength\"\n                :show-word-limit=\"item.showWordLimit\"\n                :style=\"item.style\"\n              />\n\n              <!-- 数字输入 -->\n              <el-input-number\n                v-else-if=\"item.type === 'number'\"\n                v-model=\"formModel[item.id]\"\n                :placeholder=\"item.placeholder\"\n                :min=\"item.min\"\n                :max=\"item.max\"\n                :step=\"item.step\"\n                :precision=\"item.precision\"\n                :disabled=\"item.disabled\"\n                :controls=\"item.controls\"\n                :controls-position=\"item.controlsPosition\"\n                :style=\"item.style\"\n              />\n\n              <!-- 密码输入 -->\n              <el-input\n                v-else-if=\"item.type === 'password'\"\n                v-model=\"formModel[item.id]\"\n                type=\"password\"\n                :placeholder=\"item.placeholder\"\n                :show-password=\"item.showPassword\"\n                :disabled=\"item.disabled\"\n                :readonly=\"item.readonly\"\n                :maxlength=\"item.maxlength\"\n                :style=\"item.style\"\n              />\n\n              <!-- 下拉选择 -->\n              <el-select\n                v-else-if=\"item.type === 'select'\"\n                v-model=\"formModel[item.id]\"\n                :placeholder=\"item.placeholder\"\n                :multiple=\"item.multiple\"\n                :disabled=\"item.disabled\"\n                :clearable=\"item.clearable\"\n                :filterable=\"item.filterable\"\n                :allow-create=\"item.allowCreate\"\n                :style=\"item.style\"\n              >\n                <el-option\n                  v-for=\"option in item.options\"\n                  :key=\"option.value\"\n                  :label=\"option.label\"\n                  :value=\"option.value\"\n                />\n              </el-select>\n\n              <!-- 单选框 -->\n              <el-radio-group\n                v-else-if=\"item.type === 'radio'\"\n                v-model=\"formModel[item.id]\"\n                :disabled=\"item.disabled\"\n                :size=\"item.size\"\n                :text-color=\"item.textColor\"\n                :fill=\"item.fill\"\n              >\n                <el-radio\n                  v-for=\"option in item.options\"\n                  :key=\"option.value\"\n                  :label=\"option.value\"\n                >\n                  {{ option.label }}\n                </el-radio>\n              </el-radio-group>\n\n              <!-- 多选框 -->\n              <el-checkbox-group\n                v-else-if=\"item.type === 'checkbox'\"\n                v-model=\"formModel[item.id]\"\n                :disabled=\"item.disabled\"\n                :size=\"item.size\"\n                :text-color=\"item.textColor\"\n                :fill=\"item.fill\"\n                :min=\"item.min\"\n                :max=\"item.max\"\n              >\n                <el-checkbox\n                  v-for=\"option in item.options\"\n                  :key=\"option.value\"\n                  :label=\"option.value\"\n                >\n                  {{ option.label }}\n                </el-checkbox>\n              </el-checkbox-group>\n\n              <!-- 开关 -->\n              <el-switch\n                v-else-if=\"item.type === 'switch'\"\n                v-model=\"formModel[item.id]\"\n                :disabled=\"item.disabled\"\n                :width=\"item.width\"\n                :active-text=\"item.activeText\"\n                :inactive-text=\"item.inactiveText\"\n                :active-value=\"item.activeValue\"\n                :inactive-value=\"item.inactiveValue\"\n                :active-color=\"item.activeColor\"\n                :inactive-color=\"item.inactiveColor\"\n              />\n\n              <!-- 滑块 -->\n              <el-slider\n                v-else-if=\"item.type === 'slider'\"\n                v-model=\"formModel[item.id]\"\n                :min=\"item.min\"\n                :max=\"item.max\"\n                :step=\"item.step\"\n                :disabled=\"item.disabled\"\n                :show-input=\"item.showInput\"\n                :show-input-controls=\"item.showInputControls\"\n                :show-stops=\"item.showStops\"\n                :show-tooltip=\"item.showTooltip\"\n                :range=\"item.range\"\n                :vertical=\"item.vertical\"\n                :height=\"item.height\"\n              />\n\n              <!-- 日期选择 -->\n              <el-date-picker\n                v-else-if=\"item.type === 'date'\"\n                v-model=\"formModel[item.id]\"\n                :type=\"item.dateType || 'date'\"\n                :placeholder=\"item.placeholder\"\n                :disabled=\"item.disabled\"\n                :clearable=\"item.clearable\"\n                :readonly=\"item.readonly\"\n                :editable=\"item.editable\"\n                :format=\"item.format\"\n                :value-format=\"item.valueFormat\"\n                :start-placeholder=\"item.startPlaceholder\"\n                :end-placeholder=\"item.endPlaceholder\"\n                :style=\"item.style\"\n              />\n\n              <!-- 时间选择 -->\n              <el-time-picker\n                v-else-if=\"item.type === 'time'\"\n                v-model=\"formModel[item.id]\"\n                :placeholder=\"item.placeholder\"\n                :disabled=\"item.disabled\"\n                :clearable=\"item.clearable\"\n                :readonly=\"item.readonly\"\n                :editable=\"item.editable\"\n                :format=\"item.format\"\n                :value-format=\"item.valueFormat\"\n                :style=\"item.style\"\n              />\n\n              <!-- 评分 -->\n              <el-rate\n                v-else-if=\"item.type === 'rate'\"\n                v-model=\"formModel[item.id]\"\n                :max=\"item.max\"\n                :disabled=\"item.disabled\"\n                :allow-half=\"item.allowHalf\"\n                :low-threshold=\"item.lowThreshold\"\n                :high-threshold=\"item.highThreshold\"\n                :colors=\"item.colors\"\n                :void-color=\"item.voidColor\"\n                :disabled-void-color=\"item.disabledVoidColor\"\n                :icon-classes=\"item.iconClasses\"\n                :void-icon-class=\"item.voidIconClass\"\n                :disabled-void-icon-class=\"item.disabledVoidIconClass\"\n                :show-text=\"item.showText\"\n                :show-score=\"item.showScore\"\n                :text-color=\"item.textColor\"\n                :texts=\"item.texts\"\n                :score-template=\"item.scoreTemplate\"\n              />\n\n              <!-- 级联选择 -->\n              <el-cascader\n                v-else-if=\"item.type === 'cascader'\"\n                v-model=\"formModel[item.id]\"\n                :placeholder=\"item.placeholder\"\n                :disabled=\"item.disabled\"\n                :clearable=\"item.clearable\"\n                :show-all-levels=\"item.showAllLevels\"\n                :collapse-tags=\"item.collapseTags\"\n                :separator=\"item.separator\"\n                :filterable=\"item.filterable\"\n                :options=\"item.options\"\n                :props=\"item.props\"\n                :style=\"item.style\"\n              />\n\n              <!-- 颜色选择 -->\n              <el-color-picker\n                v-else-if=\"item.type === 'color'\"\n                v-model=\"formModel[item.id]\"\n                :disabled=\"item.disabled\"\n                :size=\"item.size\"\n                :show-alpha=\"item.showAlpha\"\n                :color-format=\"item.colorFormat\"\n                :popper-class=\"item.popperClass\"\n                :predefine=\"item.predefine\"\n              />\n\n              <!-- 文件上传 -->\n              <el-upload\n                v-else-if=\"item.type === 'upload'\"\n                v-model=\"formModel[item.id]\"\n                :action=\"item.action\"\n                :multiple=\"item.multiple\"\n                :disabled=\"item.disabled\"\n                :accept=\"item.accept\"\n                :list-type=\"item.listType\"\n                :auto-upload=\"item.autoUpload\"\n                :show-file-list=\"item.showFileList\"\n                :drag=\"item.drag\"\n                :limit=\"item.limit\"\n                :on-success=\"(response, file, fileList) => handleUploadSuccess(item.id, response, file, fileList)\"\n                :on-remove=\"(file, fileList) => handleUploadRemove(item.id, file, fileList)\"\n              >\n                <el-button v-if=\"!item.drag\" size=\"small\" type=\"primary\">{{ item.buttonText }}</el-button>\n                <div v-else class=\"upload-drag-area\">\n                  <i class=\"el-icon-upload\"></i>\n                  <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n                </div>\n                <div v-if=\"item.tip\" slot=\"tip\" class=\"el-upload__tip\">{{ item.tip }}</div>\n              </el-upload>\n            </el-form-item>\n          </el-col>\n\n          <!-- 布局组件 -->\n          <el-col :key=\"item.id\" :span=\"item.span || 24\" v-else>\n            <!-- 分割线 -->\n            <el-divider\n              v-if=\"item.type === 'divider'\"\n              :direction=\"item.direction\"\n              :content-position=\"item.contentPosition\"\n            >\n              {{ item.text }}\n            </el-divider>\n\n            <!-- 文本 -->\n            <div\n              v-else-if=\"item.type === 'text'\"\n              :style=\"{\n                textAlign: item.textAlign,\n                fontSize: item.fontSize,\n                color: item.color,\n                fontWeight: item.fontWeight\n              }\"\n            >\n              {{ item.text }}\n            </div>\n\n            <!-- HTML -->\n            <div\n              v-else-if=\"item.type === 'html'\"\n              v-html=\"item.html\"\n            ></div>\n\n            <!-- 按钮 -->\n            <el-button\n              v-else-if=\"item.type === 'button'\"\n              :type=\"item.buttonType || 'primary'\"\n              :size=\"item.size\"\n              :plain=\"item.plain\"\n              :round=\"item.round\"\n              :circle=\"item.circle\"\n              :disabled=\"item.disabled\"\n              :icon=\"item.icon\"\n              :loading=\"item.loading\"\n              :style=\"item.style\"\n              @click=\"handleButtonClick(item)\"\n            >\n              {{ item.text }}\n            </el-button>\n\n            <!-- 警告提示 -->\n            <el-alert\n              v-else-if=\"item.type === 'alert'\"\n              :title=\"item.title\"\n              :type=\"item.alertType || 'info'\"\n              :description=\"item.description\"\n              :closable=\"item.closable\"\n              :center=\"item.center\"\n              :close-text=\"item.closeText\"\n              :show-icon=\"item.showIcon\"\n              :effect=\"item.effect\"\n            />\n          </el-col>\n        </template>\n      </el-row>\n\n      <!-- 表单操作按钮 -->\n      <el-form-item style=\"margin-top: 30px;\">\n        <el-button type=\"primary\" @click=\"handleSubmit\">提交表单</el-button>\n        <el-button @click=\"handleReset\">重置表单</el-button>\n        <el-button type=\"info\" @click=\"handleViewData\">查看数据</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 数据查看对话框 -->\n    <el-dialog title=\"表单数据\" :visible.sync=\"dataVisible\" width=\"60%\">\n      <pre>{{ JSON.stringify(formModel, null, 2) }}</pre>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { generateFormModel, generateRules, isLayoutComponent } from '../utils/index.js'\n\nexport default {\n  name: 'FormPreview',\n  props: {\n    formItems: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      formModel: {},\n      formRules: {},\n      dataVisible: false\n    }\n  },\n  watch: {\n    formItems: {\n      handler() {\n        this.initFormData()\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    isLayoutComponent,\n    \n    initFormData() {\n      this.formModel = generateFormModel(this.formItems)\n      this.formRules = this.generateFormRules()\n    },\n    \n    generateFormRules() {\n      const rules = {}\n      this.formItems.forEach(item => {\n        if (!isLayoutComponent(item.type)) {\n          rules[item.id] = generateRules(item)\n        }\n      })\n      return rules\n    },\n    \n    handleSubmit() {\n      this.$refs.previewForm.validate((valid) => {\n        if (valid) {\n          this.$message.success('表单验证通过！')\n          console.log('表单数据:', this.formModel)\n        } else {\n          this.$message.error('表单验证失败，请检查输入！')\n        }\n      })\n    },\n    \n    handleReset() {\n      this.$refs.previewForm.resetFields()\n      this.initFormData()\n    },\n    \n    handleViewData() {\n      this.dataVisible = true\n    },\n    \n    handleButtonClick(item) {\n      this.$message.info(`点击了按钮: ${item.text}`)\n    },\n    \n    handleUploadSuccess(fieldId, response, file, fileList) {\n      this.formModel[fieldId] = fileList\n    },\n    \n    handleUploadRemove(fieldId, file, fileList) {\n      this.formModel[fieldId] = fileList\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-preview {\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  \n  .upload-drag-area {\n    text-align: center;\n    padding: 20px;\n    border: 1px dashed #d9d9d9;\n    border-radius: 6px;\n    cursor: pointer;\n    \n    &:hover {\n      border-color: #409EFF;\n    }\n  }\n}\n</style>\n"]}]}