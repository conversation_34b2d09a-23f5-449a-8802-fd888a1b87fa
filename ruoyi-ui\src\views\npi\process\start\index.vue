<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="processList">
      <el-table-column label="流程编号" align="center" prop="procDefId" width="100" />
      <el-table-column label="流程名称" align="center" prop="procDefName" />
      <el-table-column label="流程标识" align="center" prop="procDefKey" />
      <el-table-column label="流程版本" align="center" prop="procDefVersion" width="100">
        <template slot-scope="scope">
          <el-tag size="medium">v{{ scope.row.procDefVersion }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="流程分类" align="center" prop="category" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-video-play"
            @click="handleStart(scope.row)"
            v-hasPermi="['flowable:process:start']"
          >发起</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 流程发起对话框 -->
    <el-dialog :title="'发起流程：' + form.procDefName" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="流程名称">
          <el-input v-model="form.procDefName" disabled />
        </el-form-item>
        <el-form-item label="流程标识">
          <el-input v-model="form.procDefKey" disabled />
        </el-form-item>
        <el-form-item label="业务标题" prop="businessTitle">
          <el-input v-model="form.businessTitle" placeholder="请输入业务标题" />
        </el-form-item>
        <el-form-item label="业务描述">
          <el-input v-model="form.businessDesc" type="textarea" placeholder="请输入业务描述" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="form.priority" placeholder="请选择优先级">
            <el-option label="低" value="1"></el-option>
            <el-option label="中" value="2"></el-option>
            <el-option label="高" value="3"></el-option>
            <el-option label="紧急" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="流程变量">
          <el-input v-model="form.variables" type="textarea" placeholder="请输入流程变量(JSON格式)" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">发 起</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 流程图查看对话框 -->
    <el-dialog title="流程图" :visible.sync="viewOpen" width="80%" append-to-body>
      <div style="text-align: center;">
        <img :src="processImageUrl" style="max-width: 100%; height: auto;" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNPIProcessDefinitions, startNPIProcess } from "@/api/npi/process";

export default {
  name: "NPIProcessStart",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 流程定义表格数据
      processList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 流程图URL
      processImageUrl: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        businessTitle: [
          { required: true, message: "业务标题不能为空", trigger: "blur" }
        ],
        priority: [
          { required: true, message: "优先级不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询NPI流程定义列表 */
    getList() {
      this.loading = true;
      listNPIProcessDefinitions(this.queryParams).then(response => {
        this.processList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        procDefId: null,
        procDefName: null,
        procDefKey: null,
        businessTitle: null,
        businessDesc: null,
        priority: "2",
        variables: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 发起按钮操作 */
    handleStart(row) {
      this.reset();
      this.form.procDefId = row.procDefId;
      this.form.procDefName = row.procDefName;
      this.form.procDefKey = row.procDefKey;
      this.open = true;
      this.title = "发起流程";
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.processImageUrl = process.env.VUE_APP_BASE_API + '/flowable/definition/readResource/' + row.procDefId + '/image';
      this.viewOpen = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 构建流程变量
          let variables = {};
          if (this.form.businessTitle) {
            variables.businessTitle = this.form.businessTitle;
          }
          if (this.form.businessDesc) {
            variables.businessDesc = this.form.businessDesc;
          }
          if (this.form.priority) {
            variables.priority = this.form.priority;
          }
          
          // 如果用户输入了自定义变量，尝试解析
          if (this.form.variables) {
            try {
              const customVars = JSON.parse(this.form.variables);
              variables = { ...variables, ...customVars };
            } catch (e) {
              this.$modal.msgError("流程变量格式错误，请输入有效的JSON格式");
              return;
            }
          }

          startNPIProcess(this.form.procDefKey, JSON.stringify(variables)).then(response => {
            this.$modal.msgSuccess("流程发起成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('npi/flow/start/export', {
        ...this.queryParams
      }, `npi_process_definitions_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
