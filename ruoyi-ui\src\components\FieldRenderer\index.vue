<template>
  <div class="field-renderer">
    <!-- 文本输入 -->
    <el-input
      v-if="field.type === 'input'"
      :value="value"
      :placeholder="getFieldOption('placeholder')"
      :disabled="disabled"
      readonly
    />

    <!-- 多行文本 -->
    <el-input
      v-else-if="field.type === 'textarea'"
      :value="value"
      type="textarea"
      :rows="getFieldOption('rows') || 3"
      :placeholder="getFieldOption('placeholder')"
      :disabled="disabled"
      readonly
    />

    <!-- 数字输入 -->
    <el-input-number
      v-else-if="field.type === 'number'"
      :value="value"
      :placeholder="getFieldOption('placeholder')"
      :disabled="disabled"
      style="width: 100%"
      :controls="false"
    />

    <!-- 选择器 -->
    <el-select
      v-else-if="field.type === 'select'"
      :value="value"
      :placeholder="getFieldOption('placeholder')"
      :disabled="disabled"
      style="width: 100%"
    >
      <el-option
        v-for="option in getFieldOption('optionItems') || []"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>

    <!-- 日期选择 -->
    <el-date-picker
      v-else-if="field.type === 'date'"
      :value="value"
      type="date"
      :placeholder="getFieldOption('placeholder')"
      :disabled="disabled"
      style="width: 100%"
    />

    <!-- 开关 -->
    <el-switch
      v-else-if="field.type === 'switch'"
      :value="value"
      :disabled="disabled"
    />

    <!-- 单选框组 -->
    <el-radio-group
      v-else-if="field.type === 'radio'"
      :value="value"
      :disabled="disabled"
    >
      <el-radio
        v-for="option in getFieldOption('optionItems') || []"
        :key="option.value"
        :label="option.value"
      >
        {{ option.label }}
      </el-radio>
    </el-radio-group>

    <!-- 复选框组 -->
    <el-checkbox-group
      v-else-if="field.type === 'checkbox'"
      :value="value || []"
      :disabled="disabled"
    >
      <el-checkbox
        v-for="option in getFieldOption('optionItems') || []"
        :key="option.value"
        :label="option.value"
      >
        {{ option.label }}
      </el-checkbox>
    </el-checkbox-group>
    
    <!-- 默认文本显示 -->
    <span v-else class="default-text">{{ formatValue(value) }}</span>
  </div>
</template>

<script>
export default {
  name: 'FieldRenderer',
  props: {
    value: {
      type: [String, Number, Boolean, Array, Object],
      default: null
    },
    field: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    formatValue(value) {
      if (value === null || value === undefined) {
        return '';
      }
      if (Array.isArray(value)) {
        return value.join(', ');
      }
      if (typeof value === 'object') {
        return JSON.stringify(value);
      }
      if (typeof value === 'boolean') {
        return value ? '是' : '否';
      }
      return String(value);
    },

    getFieldOption(optionName) {
      return this.field.options && this.field.options[optionName] ? this.field.options[optionName] : null;
    }
  }
}
</script>

<style lang="scss" scoped>
.field-renderer {
  width: 100%;
  
  .default-text {
    color: #606266;
    font-size: 14px;
    line-height: 32px;
  }
  
  // 禁用状态下的样式调整
  :deep(.el-input.is-disabled .el-input__inner) {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #606266;
  }
  
  :deep(.el-textarea.is-disabled .el-textarea__inner) {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #606266;
  }
  
  :deep(.el-select.is-disabled .el-input__inner) {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #606266;
  }
  
  :deep(.el-date-editor.is-disabled .el-input__inner) {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #606266;
  }
  
  :deep(.el-switch.is-disabled) {
    opacity: 0.6;
  }
  
  :deep(.el-radio.is-disabled) {
    color: #c0c4cc;
  }
  
  :deep(.el-checkbox.is-disabled) {
    color: #c0c4cc;
  }
}
</style>
