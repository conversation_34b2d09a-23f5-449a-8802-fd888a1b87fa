{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormProperties.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormProperties.vue", "mtime": 1752386494977}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzTGF5b3V0Q29tcG9uZW50LCBuZWVkc1ZhbGlkYXRpb24gfSBmcm9tICcuLi91dGlscy9pbmRleC5qcycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRm9ybVByb3BlcnRpZXMnLAogIHByb3BzOiB7CiAgICBpdGVtOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhY3RpdmVUYWI6ICdiYXNpYycsCiAgICAgIHByb3BlcnRpZXM6IHt9CiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgbmVlZHNWYWxpZGF0aW9uKCkgewogICAgICByZXR1cm4gbmVlZHNWYWxpZGF0aW9uKHRoaXMuaXRlbS50eXBlKQogICAgfSwKICAgIAogICAgaGFzUGxhY2Vob2xkZXIoKSB7CiAgICAgIHJldHVybiBbJ2lucHV0JywgJ3RleHRhcmVhJywgJ251bWJlcicsICdwYXNzd29yZCcsICdzZWxlY3QnLCAnZGF0ZScsICd0aW1lJywgJ2Nhc2NhZGVyJ10uaW5jbHVkZXModGhpcy5pdGVtLnR5cGUpCiAgICB9LAogICAgCiAgICBoYXNEZWZhdWx0VmFsdWUoKSB7CiAgICAgIHJldHVybiAhaXNMYXlvdXRDb21wb25lbnQodGhpcy5pdGVtLnR5cGUpCiAgICB9LAogICAgCiAgICBoYXNEaXNhYmxlZCgpIHsKICAgICAgcmV0dXJuICFbJ2RpdmlkZXInLCAndGV4dCcsICdodG1sJywgJ2FsZXJ0J10uaW5jbHVkZXModGhpcy5pdGVtLnR5cGUpCiAgICB9LAogICAgCiAgICBoYXNSZWFkb25seSgpIHsKICAgICAgcmV0dXJuIFsnaW5wdXQnLCAndGV4dGFyZWEnLCAncGFzc3dvcmQnLCAnZGF0ZScsICd0aW1lJ10uaW5jbHVkZXModGhpcy5pdGVtLnR5cGUpCiAgICB9LAogICAgCiAgICBoYXNDbGVhcmFibGUoKSB7CiAgICAgIHJldHVybiBbJ2lucHV0JywgJ3NlbGVjdCcsICdkYXRlJywgJ3RpbWUnLCAnY2FzY2FkZXInXS5pbmNsdWRlcyh0aGlzLml0ZW0udHlwZSkKICAgIH0sCiAgICAKICAgIGhhc0FkdmFuY2VkUHJvcHMoKSB7CiAgICAgIHJldHVybiAhWydkaXZpZGVyJywgJ3RleHQnLCAnaHRtbCcsICdhbGVydCddLmluY2x1ZGVzKHRoaXMuaXRlbS50eXBlKQogICAgfSwKICAgIAogICAgaGFzT3B0aW9ucygpIHsKICAgICAgcmV0dXJuIFsnc2VsZWN0JywgJ3JhZGlvJywgJ2NoZWNrYm94J10uaW5jbHVkZXModGhpcy5pdGVtLnR5cGUpCiAgICB9LAogICAgCiAgICBoYXNTdHlsZSgpIHsKICAgICAgcmV0dXJuIHRoaXMucHJvcGVydGllcy5zdHlsZSAmJiB0eXBlb2YgdGhpcy5wcm9wZXJ0aWVzLnN0eWxlID09PSAnb2JqZWN0JwogICAgfSwKICAgIAogICAgc3R5bGVXaWR0aDogewogICAgICBnZXQoKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuaGFzU3R5bGUgPyB0aGlzLnByb3BlcnRpZXMuc3R5bGUud2lkdGggOiAnJwogICAgICB9LAogICAgICBzZXQodmFsdWUpIHsKICAgICAgICB0aGlzLnVwZGF0ZVN0eWxlV2lkdGgodmFsdWUpCiAgICAgIH0KICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBpdGVtOiB7CiAgICAgIGhhbmRsZXIobmV3SXRlbSkgewogICAgICAgIHRoaXMucHJvcGVydGllcyA9IHsgLi4ubmV3SXRlbSB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwKICAgICAgZGVlcDogdHJ1ZQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgdXBkYXRlUHJvcGVydHkoa2V5LCB2YWx1ZSkgewogICAgICB0aGlzLnByb3BlcnRpZXNba2V5XSA9IHZhbHVlCiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZScsIHsgW2tleV06IHZhbHVlIH0pCiAgICB9LAogICAgCiAgICB1cGRhdGVPcHRpb25zKCkgewogICAgICB0aGlzLiRlbWl0KCd1cGRhdGUnLCB7IG9wdGlvbnM6IHRoaXMucHJvcGVydGllcy5vcHRpb25zIH0pCiAgICB9LAogICAgCiAgICB1cGRhdGVSdWxlcygpIHsKICAgICAgdGhpcy4kZW1pdCgndXBkYXRlJywgeyBydWxlczogdGhpcy5wcm9wZXJ0aWVzLnJ1bGVzIH0pCiAgICB9LAogICAgCiAgICB1cGRhdGVTdHlsZVdpZHRoKHZhbHVlKSB7CiAgICAgIGlmICghdGhpcy5wcm9wZXJ0aWVzLnN0eWxlKSB7CiAgICAgICAgdGhpcy5wcm9wZXJ0aWVzLnN0eWxlID0ge30KICAgICAgfQogICAgICB0aGlzLnByb3BlcnRpZXMuc3R5bGUud2lkdGggPSB2YWx1ZQogICAgICB0aGlzLiRlbWl0KCd1cGRhdGUnLCB7IHN0eWxlOiB0aGlzLnByb3BlcnRpZXMuc3R5bGUgfSkKICAgIH0sCiAgICAKICAgIGFkZE9wdGlvbigpIHsKICAgICAgaWYgKCF0aGlzLnByb3BlcnRpZXMub3B0aW9ucykgewogICAgICAgIHRoaXMucHJvcGVydGllcy5vcHRpb25zID0gW10KICAgICAgfQogICAgICB0aGlzLnByb3BlcnRpZXMub3B0aW9ucy5wdXNoKHsKICAgICAgICBsYWJlbDogYOmAiemhuSR7dGhpcy5wcm9wZXJ0aWVzLm9wdGlvbnMubGVuZ3RoICsgMX1gLAogICAgICAgIHZhbHVlOiBgJHt0aGlzLnByb3BlcnRpZXMub3B0aW9ucy5sZW5ndGggKyAxfWAKICAgICAgfSkKICAgICAgdGhpcy51cGRhdGVPcHRpb25zKCkKICAgIH0sCiAgICAKICAgIHJlbW92ZU9wdGlvbihpbmRleCkgewogICAgICB0aGlzLnByb3BlcnRpZXMub3B0aW9ucy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgIHRoaXMudXBkYXRlT3B0aW9ucygpCiAgICB9LAogICAgCiAgICBhZGRSdWxlKCkgewogICAgICBpZiAoIXRoaXMucHJvcGVydGllcy5ydWxlcykgewogICAgICAgIHRoaXMucHJvcGVydGllcy5ydWxlcyA9IFtdCiAgICAgIH0KICAgICAgdGhpcy5wcm9wZXJ0aWVzLnJ1bGVzLnB1c2goewogICAgICAgIHR5cGU6ICdyZXF1aXJlZCcsCiAgICAgICAgbWVzc2FnZTogJ+atpOWtl+auteS4uuW/heWhq+mhuScsCiAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgIH0pCiAgICAgIHRoaXMudXBkYXRlUnVsZXMoKQogICAgfSwKICAgIAogICAgcmVtb3ZlUnVsZShpbmRleCkgewogICAgICB0aGlzLnByb3BlcnRpZXMucnVsZXMuc3BsaWNlKGluZGV4LCAxKQogICAgICB0aGlzLnVwZGF0ZVJ1bGVzKCkKICAgIH0sCiAgICAKICAgIGdldERlZmF1bHRWYWx1ZUNvbXBvbmVudCgpIHsKICAgICAgY29uc3QgY29tcG9uZW50TWFwID0gewogICAgICAgICdpbnB1dCc6ICdlbC1pbnB1dCcsCiAgICAgICAgJ3RleHRhcmVhJzogJ2VsLWlucHV0JywKICAgICAgICAnbnVtYmVyJzogJ2VsLWlucHV0LW51bWJlcicsCiAgICAgICAgJ3Bhc3N3b3JkJzogJ2VsLWlucHV0JywKICAgICAgICAnc3dpdGNoJzogJ2VsLXN3aXRjaCcsCiAgICAgICAgJ3NsaWRlcic6ICdlbC1zbGlkZXInLAogICAgICAgICdyYXRlJzogJ2VsLXJhdGUnCiAgICAgIH0KICAgICAgcmV0dXJuIGNvbXBvbmVudE1hcFt0aGlzLml0ZW0udHlwZV0gfHwgJ2VsLWlucHV0JwogICAgfSwKICAgIAogICAgZ2V0RGVmYXVsdFZhbHVlUHJvcHMoKSB7CiAgICAgIGNvbnN0IHByb3BzTWFwID0gewogICAgICAgICd0ZXh0YXJlYSc6IHsgdHlwZTogJ3RleHRhcmVhJyB9LAogICAgICAgICdwYXNzd29yZCc6IHsgdHlwZTogJ3Bhc3N3b3JkJywgc2hvd1Bhc3N3b3JkOiB0cnVlIH0sCiAgICAgICAgJ251bWJlcic6IHsgY29udHJvbHNQb3NpdGlvbjogJ3JpZ2h0JyB9CiAgICAgIH0KICAgICAgcmV0dXJuIHByb3BzTWFwW3RoaXMuaXRlbS50eXBlXSB8fCB7fQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["FormProperties.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmRA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FormProperties.vue", "sourceRoot": "src/components/FormDesigner/components", "sourcesContent": ["<template>\n  <div class=\"form-properties\">\n    <el-tabs v-model=\"activeTab\" type=\"border-card\">\n      <!-- 基础属性 -->\n      <el-tab-pane label=\"基础属性\" name=\"basic\">\n        <el-form :model=\"properties\" label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"字段标识\">\n            <el-input v-model=\"properties.id\" disabled />\n          </el-form-item>\n          \n          <el-form-item label=\"标签文字\">\n            <el-input v-model=\"properties.label\" @input=\"updateProperty('label', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"占位提示\" v-if=\"hasPlaceholder\">\n            <el-input v-model=\"properties.placeholder\" @input=\"updateProperty('placeholder', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"默认值\" v-if=\"hasDefaultValue\">\n            <component\n              :is=\"getDefaultValueComponent()\"\n              v-model=\"properties.defaultValue\"\n              @input=\"updateProperty('defaultValue', $event)\"\n              v-bind=\"getDefaultValueProps()\"\n            />\n          </el-form-item>\n\n          <el-form-item label=\"是否必填\" v-if=\"needsValidation\">\n            <el-switch v-model=\"properties.required\" @change=\"updateProperty('required', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"是否禁用\" v-if=\"hasDisabled\">\n            <el-switch v-model=\"properties.disabled\" @change=\"updateProperty('disabled', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"是否只读\" v-if=\"hasReadonly\">\n            <el-switch v-model=\"properties.readonly\" @change=\"updateProperty('readonly', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"是否可清空\" v-if=\"hasClearable\">\n            <el-switch v-model=\"properties.clearable\" @change=\"updateProperty('clearable', $event)\" />\n          </el-form-item>\n        </el-form>\n      </el-tab-pane>\n\n      <!-- 高级属性 -->\n      <el-tab-pane label=\"高级属性\" name=\"advanced\" v-if=\"hasAdvancedProps\">\n        <el-form :model=\"properties\" label-width=\"80px\" size=\"small\">\n          <!-- 输入框特有属性 -->\n          <template v-if=\"item.type === 'input' || item.type === 'textarea'\">\n            <el-form-item label=\"最大长度\">\n              <el-input-number v-model=\"properties.maxlength\" :min=\"0\" @change=\"updateProperty('maxlength', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示字数\">\n              <el-switch v-model=\"properties.showWordLimit\" @change=\"updateProperty('showWordLimit', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 数字输入框特有属性 -->\n          <template v-if=\"item.type === 'number'\">\n            <el-form-item label=\"最小值\">\n              <el-input-number v-model=\"properties.min\" @change=\"updateProperty('min', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"最大值\">\n              <el-input-number v-model=\"properties.max\" @change=\"updateProperty('max', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"步长\">\n              <el-input-number v-model=\"properties.step\" :min=\"0\" @change=\"updateProperty('step', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"精度\">\n              <el-input-number v-model=\"properties.precision\" :min=\"0\" @change=\"updateProperty('precision', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 选择器特有属性 -->\n          <template v-if=\"item.type === 'select'\">\n            <el-form-item label=\"多选\">\n              <el-switch v-model=\"properties.multiple\" @change=\"updateProperty('multiple', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"可搜索\">\n              <el-switch v-model=\"properties.filterable\" @change=\"updateProperty('filterable', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"允许创建\">\n              <el-switch v-model=\"properties.allowCreate\" @change=\"updateProperty('allowCreate', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 开关特有属性 -->\n          <template v-if=\"item.type === 'switch'\">\n            <el-form-item label=\"开启文字\">\n              <el-input v-model=\"properties.activeText\" @input=\"updateProperty('activeText', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"关闭文字\">\n              <el-input v-model=\"properties.inactiveText\" @input=\"updateProperty('inactiveText', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"开启值\">\n              <el-input v-model=\"properties.activeValue\" @input=\"updateProperty('activeValue', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"关闭值\">\n              <el-input v-model=\"properties.inactiveValue\" @input=\"updateProperty('inactiveValue', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 滑块特有属性 -->\n          <template v-if=\"item.type === 'slider'\">\n            <el-form-item label=\"显示输入框\">\n              <el-switch v-model=\"properties.showInput\" @change=\"updateProperty('showInput', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示间断点\">\n              <el-switch v-model=\"properties.showStops\" @change=\"updateProperty('showStops', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"范围选择\">\n              <el-switch v-model=\"properties.range\" @change=\"updateProperty('range', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 评分特有属性 -->\n          <template v-if=\"item.type === 'rate'\">\n            <el-form-item label=\"最大分值\">\n              <el-input-number v-model=\"properties.max\" :min=\"1\" @change=\"updateProperty('max', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"允许半选\">\n              <el-switch v-model=\"properties.allowHalf\" @change=\"updateProperty('allowHalf', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示文字\">\n              <el-switch v-model=\"properties.showText\" @change=\"updateProperty('showText', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示分数\">\n              <el-switch v-model=\"properties.showScore\" @change=\"updateProperty('showScore', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 上传特有属性 -->\n          <template v-if=\"item.type === 'upload'\">\n            <el-form-item label=\"上传地址\">\n              <el-input v-model=\"properties.action\" @input=\"updateProperty('action', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"多选\">\n              <el-switch v-model=\"properties.multiple\" @change=\"updateProperty('multiple', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"拖拽上传\">\n              <el-switch v-model=\"properties.drag\" @change=\"updateProperty('drag', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"文件类型\">\n              <el-input v-model=\"properties.accept\" @input=\"updateProperty('accept', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"文件个数限制\">\n              <el-input-number v-model=\"properties.limit\" :min=\"0\" @change=\"updateProperty('limit', $event)\" />\n            </el-form-item>\n          </template>\n        </el-form>\n      </el-tab-pane>\n\n      <!-- 选项配置 -->\n      <el-tab-pane label=\"选项配置\" name=\"options\" v-if=\"hasOptions\">\n        <div class=\"options-config\">\n          <div class=\"options-header\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addOption\">添加选项</el-button>\n          </div>\n          <div class=\"options-list\">\n            <div \n              v-for=\"(option, index) in properties.options\" \n              :key=\"index\"\n              class=\"option-item\"\n            >\n              <el-input \n                v-model=\"option.label\" \n                placeholder=\"选项标签\"\n                size=\"small\"\n                @input=\"updateOptions\"\n              />\n              <el-input \n                v-model=\"option.value\" \n                placeholder=\"选项值\"\n                size=\"small\"\n                @input=\"updateOptions\"\n              />\n              <el-button \n                type=\"danger\" \n                size=\"small\" \n                icon=\"el-icon-delete\"\n                @click=\"removeOption(index)\"\n              />\n            </div>\n          </div>\n        </div>\n      </el-tab-pane>\n\n      <!-- 样式配置 -->\n      <el-tab-pane label=\"样式配置\" name=\"style\">\n        <el-form :model=\"properties\" label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"栅格占位\">\n            <el-slider \n              v-model=\"properties.span\" \n              :min=\"1\" \n              :max=\"24\" \n              show-input\n              @change=\"updateProperty('span', $event)\"\n            />\n          </el-form-item>\n\n          <el-form-item label=\"标签宽度\">\n            <el-input v-model=\"properties.labelWidth\" @input=\"updateProperty('labelWidth', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"组件宽度\" v-if=\"hasStyle\">\n            <el-input v-model=\"styleWidth\" @input=\"updateStyleWidth\" placeholder=\"如: 100%, 200px\" />\n          </el-form-item>\n\n          <!-- 文本样式 -->\n          <template v-if=\"item.type === 'text'\">\n            <el-form-item label=\"文字对齐\">\n              <el-select v-model=\"properties.textAlign\" @change=\"updateProperty('textAlign', $event)\">\n                <el-option label=\"左对齐\" value=\"left\" />\n                <el-option label=\"居中\" value=\"center\" />\n                <el-option label=\"右对齐\" value=\"right\" />\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"字体大小\">\n              <el-input v-model=\"properties.fontSize\" @input=\"updateProperty('fontSize', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"字体颜色\">\n              <el-color-picker v-model=\"properties.color\" @change=\"updateProperty('color', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"字体粗细\">\n              <el-select v-model=\"properties.fontWeight\" @change=\"updateProperty('fontWeight', $event)\">\n                <el-option label=\"正常\" value=\"normal\" />\n                <el-option label=\"粗体\" value=\"bold\" />\n              </el-select>\n            </el-form-item>\n          </template>\n        </el-form>\n      </el-tab-pane>\n\n      <!-- 验证规则 -->\n      <el-tab-pane label=\"验证规则\" name=\"validation\" v-if=\"needsValidation\">\n        <div class=\"validation-config\">\n          <div class=\"validation-header\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addRule\">添加规则</el-button>\n          </div>\n          <div class=\"validation-list\">\n            <div \n              v-for=\"(rule, index) in properties.rules\" \n              :key=\"index\"\n              class=\"rule-item\"\n            >\n              <el-form :model=\"rule\" label-width=\"60px\" size=\"small\">\n                <el-form-item label=\"类型\">\n                  <el-select v-model=\"rule.type\" @change=\"updateRules\">\n                    <el-option label=\"必填\" value=\"required\" />\n                    <el-option label=\"最小长度\" value=\"min\" />\n                    <el-option label=\"最大长度\" value=\"max\" />\n                    <el-option label=\"正则\" value=\"pattern\" />\n                    <el-option label=\"自定义\" value=\"validator\" />\n                  </el-select>\n                </el-form-item>\n                <el-form-item label=\"值\" v-if=\"rule.type !== 'required'\">\n                  <el-input v-model=\"rule.value\" @input=\"updateRules\" />\n                </el-form-item>\n                <el-form-item label=\"提示\">\n                  <el-input v-model=\"rule.message\" @input=\"updateRules\" />\n                </el-form-item>\n                <el-form-item>\n                  <el-button type=\"danger\" size=\"small\" @click=\"removeRule(index)\">删除</el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n          </div>\n        </div>\n      </el-tab-pane>\n    </el-tabs>\n  </div>\n</template>\n\n<script>\nimport { isLayoutComponent, needsValidation } from '../utils/index.js'\n\nexport default {\n  name: 'FormProperties',\n  props: {\n    item: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      activeTab: 'basic',\n      properties: {}\n    }\n  },\n  computed: {\n    needsValidation() {\n      return needsValidation(this.item.type)\n    },\n    \n    hasPlaceholder() {\n      return ['input', 'textarea', 'number', 'password', 'select', 'date', 'time', 'cascader'].includes(this.item.type)\n    },\n    \n    hasDefaultValue() {\n      return !isLayoutComponent(this.item.type)\n    },\n    \n    hasDisabled() {\n      return !['divider', 'text', 'html', 'alert'].includes(this.item.type)\n    },\n    \n    hasReadonly() {\n      return ['input', 'textarea', 'password', 'date', 'time'].includes(this.item.type)\n    },\n    \n    hasClearable() {\n      return ['input', 'select', 'date', 'time', 'cascader'].includes(this.item.type)\n    },\n    \n    hasAdvancedProps() {\n      return !['divider', 'text', 'html', 'alert'].includes(this.item.type)\n    },\n    \n    hasOptions() {\n      return ['select', 'radio', 'checkbox'].includes(this.item.type)\n    },\n    \n    hasStyle() {\n      return this.properties.style && typeof this.properties.style === 'object'\n    },\n    \n    styleWidth: {\n      get() {\n        return this.hasStyle ? this.properties.style.width : ''\n      },\n      set(value) {\n        this.updateStyleWidth(value)\n      }\n    }\n  },\n  watch: {\n    item: {\n      handler(newItem) {\n        this.properties = { ...newItem }\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    updateProperty(key, value) {\n      this.properties[key] = value\n      this.$emit('update', { [key]: value })\n    },\n    \n    updateOptions() {\n      this.$emit('update', { options: this.properties.options })\n    },\n    \n    updateRules() {\n      this.$emit('update', { rules: this.properties.rules })\n    },\n    \n    updateStyleWidth(value) {\n      if (!this.properties.style) {\n        this.properties.style = {}\n      }\n      this.properties.style.width = value\n      this.$emit('update', { style: this.properties.style })\n    },\n    \n    addOption() {\n      if (!this.properties.options) {\n        this.properties.options = []\n      }\n      this.properties.options.push({\n        label: `选项${this.properties.options.length + 1}`,\n        value: `${this.properties.options.length + 1}`\n      })\n      this.updateOptions()\n    },\n    \n    removeOption(index) {\n      this.properties.options.splice(index, 1)\n      this.updateOptions()\n    },\n    \n    addRule() {\n      if (!this.properties.rules) {\n        this.properties.rules = []\n      }\n      this.properties.rules.push({\n        type: 'required',\n        message: '此字段为必填项',\n        trigger: 'blur'\n      })\n      this.updateRules()\n    },\n    \n    removeRule(index) {\n      this.properties.rules.splice(index, 1)\n      this.updateRules()\n    },\n    \n    getDefaultValueComponent() {\n      const componentMap = {\n        'input': 'el-input',\n        'textarea': 'el-input',\n        'number': 'el-input-number',\n        'password': 'el-input',\n        'switch': 'el-switch',\n        'slider': 'el-slider',\n        'rate': 'el-rate'\n      }\n      return componentMap[this.item.type] || 'el-input'\n    },\n    \n    getDefaultValueProps() {\n      const propsMap = {\n        'textarea': { type: 'textarea' },\n        'password': { type: 'password', showPassword: true },\n        'number': { controlsPosition: 'right' }\n      }\n      return propsMap[this.item.type] || {}\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-properties {\n  height: 100%;\n  \n  .options-config, .validation-config {\n    .options-header, .validation-header {\n      margin-bottom: 10px;\n    }\n    \n    .option-item, .rule-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 10px;\n      padding: 10px;\n      border: 1px solid #e4e7ed;\n      border-radius: 4px;\n      \n      .el-input {\n        margin-right: 10px;\n      }\n    }\n    \n    .rule-item {\n      flex-direction: column;\n      align-items: stretch;\n    }\n  }\n}\n</style>\n"]}]}