{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\finished\\detail\\index.vue?vue&type=style&index=0&id=b461f678&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\finished\\detail\\index.vue", "mtime": 1752407394746}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi50ZXN0LWZvcm0gew0KICBtYXJnaW46IDE1cHggYXV0bzsNCiAgd2lkdGg6IDgwMHB4Ow0KICBwYWRkaW5nOiAxNXB4Ow0KfQ0KDQouY2xlYXJmaXg6YmVmb3JlLA0KLmNsZWFyZml4OmFmdGVyIHsNCiAgZGlzcGxheTogdGFibGU7DQogIGNvbnRlbnQ6ICIiOw0KfQ0KLmNsZWFyZml4OmFmdGVyIHsNCiAgY2xlYXI6IGJvdGgNCn0NCg0KLmJveC1jYXJkIHsNCiAgd2lkdGg6IDEwMCU7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5lbC10YWcgKyAuZWwtdGFnIHsNCiAgbWFyZ2luLWxlZnQ6IDEwcHg7DQp9DQoNCi5teS1sYWJlbCB7DQogIGJhY2tncm91bmQ6ICNFMUYzRDg7DQp9DQoNCi5jdXJyZW50LWZvcm0tY2FyZCB7DQogIGJvcmRlcjogMnB4IHNvbGlkICM0MDlFRkY7DQp9DQoNCi5jdXJyZW50LWZvcm0taGVhZGVyIHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5jdXJyZW50LWZvcm0taGVhZGVyIGkgew0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/task/finished/detail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\" >\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">已办任务</span>\r\n        <el-button style=\"float: right;\" size=\"mini\" type=\"danger\" @click=\"goBack\">关闭</el-button>\r\n      </div>\r\n      <el-tabs  tab-position=\"top\" v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <!--表单信息-->\r\n        <el-tab-pane label=\"表单信息\" name=\"1\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <v-form-render ref=\"vFormRef\" :key=\"formKey\"/>\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <!--流程流转记录-->\r\n        <el-tab-pane label=\"流转记录\" name=\"2\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <flow-history :flow-record-list=\"flowRecordList\" />\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"流程图\" name=\"3\">\r\n          <Bpmn-viewer :flowData=\"flowData\" :procInsId=\"taskForm.procInsId\"/>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {flowRecord} from \"@/api/flowable/finished\";\r\nimport {getProcessVariables, flowXmlAndNode} from \"@/api/flowable/definition\";\r\nimport BpmnViewer from '@/components/Process/viewer';\r\nimport FlowHistory from '@/components/FlowHistory';\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Record\",\r\n  components: {\r\n    BpmnViewer,\r\n    FlowHistory,\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      // 模型xml数据\r\n      flowData: {},\r\n      activeName: '1',\r\n      // 查询参数\r\n      queryParams: {\r\n        deptId: undefined\r\n      },\r\n      // 遮罩层\r\n      loading: true,\r\n      flowRecordList: [], // 流程流转数据\r\n      taskForm:{\r\n        multiple: false,\r\n        comment:\"\", // 意见内容\r\n        procInsId: \"\", // 流程实例编号\r\n        instanceId: \"\", // 流程实例编号\r\n        deployId: \"\",  // 流程定义编号\r\n        taskId: \"\" ,// 流程任务编号\r\n        procDefId: \"\",  // 流程编号\r\n        vars: \"\",\r\n      },\r\n      formKey: Date.now() // VForm组件的唯一key\r\n    };\r\n  },\r\n  created() {\r\n    this.taskForm.deployId = this.$route.query && this.$route.query.deployId;\r\n    this.taskForm.taskId  = this.$route.query && this.$route.query.taskId;\r\n    this.taskForm.procInsId = this.$route.query && this.$route.query.procInsId;\r\n    // 流程任务重获取变量表单\r\n    if (this.taskForm.taskId){\r\n      this.processVariables( this.taskForm.taskId)\r\n    }\r\n    this.getFlowRecordList( this.taskForm.procInsId, this.taskForm.deployId);\r\n  },\r\n  methods: {\r\n    handleClick(tab) {\r\n      if (tab.name === '3') {\r\n        flowXmlAndNode({procInsId: this.taskForm.procInsId, deployId: this.taskForm.deployId}).then(res => {\r\n          this.flowData = res.data;\r\n        })\r\n      }\r\n    },\r\n    /** 流程流转记录 */\r\n    getFlowRecordList(procInsId, deployId) {\r\n      const that = this\r\n      const params = {procInsId: procInsId, deployId: deployId}\r\n      flowRecord(params).then(res => {\r\n        that.flowRecordList = res.data.flowList;\r\n      }).catch(() => {\r\n        this.goBack();\r\n      })\r\n    },\r\n    /** 获取流程变量内容 */\r\n    processVariables(taskId) {\r\n      if (taskId) {\r\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\r\n        getProcessVariables(taskId).then(res => {\r\n          // 更新formKey以强制重新渲染VForm组件，避免key冲突\r\n          this.formKey = Date.now();\r\n\r\n          this.$nextTick(() => {\r\n            // 回显表单\r\n            this.$refs.vFormRef.setFormJson(res.data.formJson);\r\n            this.$nextTick(() => {\r\n              // 加载表单填写的数据\r\n              this.$refs.vFormRef.setFormData(res.data);\r\n              this.$nextTick(() => {\r\n                // 表单禁用\r\n                this.$refs.vFormRef.disableForm();\r\n              })\r\n            })\r\n          })\r\n        });\r\n      }\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      const obj = { path: \"/task/finished\", query: { t: Date.now()} };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.test-form {\r\n  margin: 15px auto;\r\n  width: 800px;\r\n  padding: 15px;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n.clearfix:after {\r\n  clear: both\r\n}\r\n\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.my-label {\r\n  background: #E1F3D8;\r\n}\r\n\r\n.current-form-card {\r\n  border: 2px solid #409EFF;\r\n}\r\n\r\n.current-form-header {\r\n  color: #409EFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.current-form-header i {\r\n  margin-right: 8px;\r\n}\r\n</style>\r\n"]}]}