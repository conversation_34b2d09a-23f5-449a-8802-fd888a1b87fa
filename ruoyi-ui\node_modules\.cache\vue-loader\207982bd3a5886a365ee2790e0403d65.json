{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue", "mtime": 1752405854599}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2Zsb3dSZWNvcmR9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL2ZpbmlzaGVkIjsNCmltcG9ydCBGbG93VXNlciBmcm9tICdAL2NvbXBvbmVudHMvZmxvdy9Vc2VyJw0KaW1wb3J0IEZsb3dSb2xlIGZyb20gJ0AvY29tcG9uZW50cy9mbG93L1JvbGUnDQppbXBvcnQge2Zsb3dYbWxBbmROb2RlfSBmcm9tICJAL2FwaS9mbG93YWJsZS9kZWZpbml0aW9uIjsNCmltcG9ydCB7DQogIGNvbXBsZXRlLA0KICByZWplY3RUYXNrLA0KICByZXR1cm5MaXN0LA0KICByZXR1cm5UYXNrLA0KICBnZXROZXh0Rmxvd05vZGUsDQogIGRlbGVnYXRlLA0KICBmbG93VGFza0Zvcm0NCn0gZnJvbSAiQC9hcGkvZmxvd2FibGUvdG9kbyI7DQppbXBvcnQgQnBtblZpZXdlciBmcm9tICdAL2NvbXBvbmVudHMvUHJvY2Vzcy92aWV3ZXInOw0KaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlJlY29yZCIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBCcG1uVmlld2VyLA0KICAgIEZsb3dVc2VyLA0KICAgIEZsb3dSb2xlLA0KICB9LA0KICBwcm9wczoge30sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGV2ZW50TmFtZTogImNsaWNrIiwNCiAgICAgIC8vIOa1geeoi+aVsOaNrg0KICAgICAgZmxvd0RhdGE6IHt9LA0KICAgICAgYWN0aXZlTmFtZTogJzEnLA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgZmxvd1JlY29yZExpc3Q6IFtdLCAvLyDmtYHnqIvmtYHovazmlbDmja4NCiAgICAgIHJ1bGVzOiB7fSwgLy8g6KGo5Y2V5qCh6aqMDQogICAgICB0YXNrRm9ybTogew0KICAgICAgICByZXR1cm5UYXNrU2hvdzogZmFsc2UsIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQ0KICAgICAgICBkZWxlZ2F0ZVRhc2tTaG93OiBmYWxzZSwgLy8g5piv5ZCm5bGV56S65Zue6YCA6KGo5Y2VDQogICAgICAgIGRlZmF1bHRUYXNrU2hvdzogdHJ1ZSwgLy8g6buY6K6k5aSE55CGDQogICAgICAgIGNvbW1lbnQ6ICIiLCAvLyDmhI/op4HlhoXlrrkNCiAgICAgICAgcHJvY0luc0lkOiAiIiwgLy8g5rWB56iL5a6e5L6L57yW5Y+3DQogICAgICAgIGluc3RhbmNlSWQ6ICIiLCAvLyDmtYHnqIvlrp7kvovnvJblj7cNCiAgICAgICAgZGVwbG95SWQ6ICIiLCAgLy8g5rWB56iL5a6a5LmJ57yW5Y+3DQogICAgICAgIHRhc2tJZDogIiIsLy8g5rWB56iL5Lu75Yqh57yW5Y+3DQogICAgICAgIHByb2NEZWZJZDogIiIsICAvLyDmtYHnqIvnvJblj7cNCiAgICAgICAgdGFyZ2V0S2V5OiAiIiwNCiAgICAgICAgdmFyaWFibGVzOiB7fSwNCiAgICAgIH0sDQogICAgICByZXR1cm5UYXNrTGlzdDogW10sICAvLyDlm57pgIDliJfooajmlbDmja4NCiAgICAgIGNvbXBsZXRlVGl0bGU6IG51bGwsDQogICAgICBjb21wbGV0ZU9wZW46IGZhbHNlLA0KICAgICAgcmV0dXJuVGl0bGU6IG51bGwsDQogICAgICByZXR1cm5PcGVuOiBmYWxzZSwNCiAgICAgIHJlamVjdE9wZW46IGZhbHNlLA0KICAgICAgcmVqZWN0VGl0bGU6IG51bGwsDQogICAgICBjaGVja1NlbmRVc2VyOiBmYWxzZSwgLy8g5piv5ZCm5bGV56S65Lq65ZGY6YCJ5oup5qih5Z2XDQogICAgICBjaGVja1NlbmRSb2xlOiBmYWxzZSwvLyDmmK/lkKblsZXnpLrop5LoibLpgInmi6nmqKHlnZcNCiAgICAgIGNoZWNrVHlwZTogJ3NpbmdsZScsIC8vIOmAieaLqeexu+Weiw0KICAgICAgdGFza05hbWU6IG51bGwsIC8vIOS7u+WKoeiKgueCuQ0KICAgICAgc3RhcnRVc2VyOiBudWxsLCAvLyDlj5Hotbfkurrkv6Hmga8sDQogICAgICBtdWx0aUluc3RhbmNlVmFyczogJycsIC8vIOS8muetvuiKgueCuQ0KICAgICAgZm9ybUpzb246e30sDQogICAgICBhY3RpdmVIaXN0b3J5TmFtZXM6IFtdLCAvLyDlsZXlvIDnmoTljoblj7LoioLngrkNCiAgICAgIGNvbXBsZXRlZEZsb3dSZWNvcmRzOiBbXSwgLy8g5bey5a6M5oiQ55qE5rWB56iL6K6w5b2VDQogICAgICBmb3JtS2V5OiBEYXRlLm5vdygpIC8vIFZGb3Jt57uE5Lu255qE5ZSv5LiAa2V5DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBpZiAodGhpcy4kcm91dGUucXVlcnkpIHsNCiAgICAgIHRoaXMudGFza05hbWUgPSB0aGlzLiRyb3V0ZS5xdWVyeS50YXNrTmFtZTsNCiAgICAgIHRoaXMuc3RhcnRVc2VyID0gdGhpcy4kcm91dGUucXVlcnkuc3RhcnRVc2VyOw0KICAgICAgdGhpcy50YXNrRm9ybS5kZXBsb3lJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmRlcGxveUlkOw0KICAgICAgdGhpcy50YXNrRm9ybS50YXNrSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS50YXNrSWQ7DQogICAgICB0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsNCiAgICAgIHRoaXMudGFza0Zvcm0uZXhlY3V0aW9uSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5leGVjdXRpb25JZDsNCiAgICAgIHRoaXMudGFza0Zvcm0uaW5zdGFuY2VJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsNCiAgICAgIC8vIOa1geeoi+S7u+WKoeiOt+WPluWPmOmHj+S/oeaBrw0KICAgICAgaWYgKHRoaXMudGFza0Zvcm0udGFza0lkKSB7DQogICAgICAgIHRoaXMuZ2V0Rmxvd1Rhc2tGb3JtKHRoaXMudGFza0Zvcm0udGFza0lkKTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0Rmxvd1JlY29yZExpc3QodGhpcy50YXNrRm9ybS5wcm9jSW5zSWQsIHRoaXMudGFza0Zvcm0uZGVwbG95SWQpOw0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNsaWNrKHRhYiwgZXZlbnQpIHsNCiAgICAgIGlmICh0YWIubmFtZSA9PT0gJzMnKSB7DQogICAgICAgIGZsb3dYbWxBbmROb2RlKHtwcm9jSW5zSWQ6IHRoaXMudGFza0Zvcm0ucHJvY0luc0lkLCBkZXBsb3lJZDogdGhpcy50YXNrRm9ybS5kZXBsb3lJZH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICB0aGlzLmZsb3dEYXRhID0gcmVzLmRhdGE7DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBzZXRJY29uKHZhbCkgew0KICAgICAgaWYgKHZhbCkgew0KICAgICAgICByZXR1cm4gImVsLWljb24tY2hlY2siOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICJlbC1pY29uLXRpbWUiOw0KICAgICAgfQ0KICAgIH0sDQogICAgc2V0Q29sb3IodmFsKSB7DQogICAgICBpZiAodmFsKSB7DQogICAgICAgIHJldHVybiAiIzJiYzQxOCI7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gIiNiM2JkYmIiOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g55So5oi35L+h5oGv6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlVXNlclNlbGVjdChzZWxlY3Rpb24pIHsNCiAgICAgIGlmIChzZWxlY3Rpb24pIHsNCiAgICAgICAgaWYgKHNlbGVjdGlvbiBpbnN0YW5jZW9mIEFycmF5KSB7DQogICAgICAgICAgY29uc3Qgc2VsZWN0VmFsID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0udXNlcklkLnRvU3RyaW5nKCkpOw0KICAgICAgICAgIGlmICh0aGlzLm11bHRpSW5zdGFuY2VWYXJzKSB7DQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy50YXNrRm9ybS52YXJpYWJsZXMsIHRoaXMubXVsdGlJbnN0YW5jZVZhcnMsICBzZWxlY3RWYWwpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRzZXQodGhpcy50YXNrRm9ybS52YXJpYWJsZXMsICJhcHByb3ZhbCIsIHNlbGVjdFZhbC5qb2luKCcsJykpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy50YXNrRm9ybS52YXJpYWJsZXMsICJhcHByb3ZhbCIsIHNlbGVjdGlvbi51c2VySWQudG9TdHJpbmcoKSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOinkuiJsuS/oeaBr+mAieS4reaVsOaNrg0KICAgIGhhbmRsZVJvbGVTZWxlY3Qoc2VsZWN0aW9uLCByb2xlTmFtZSkgew0KICAgICAgaWYgKHNlbGVjdGlvbikgew0KICAgICAgICBpZiAoc2VsZWN0aW9uIGluc3RhbmNlb2YgQXJyYXkpIHsNCiAgICAgICAgICBjb25zdCBzZWxlY3RWYWwgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5yb2xlSWQudG9TdHJpbmcoKSk7DQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMudGFza0Zvcm0udmFyaWFibGVzLCAiYXBwcm92YWwiLCBzZWxlY3RWYWwuam9pbignLCcpKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy50YXNrRm9ybS52YXJpYWJsZXMsICJhcHByb3ZhbCIsIHNlbGVjdGlvbik7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmtYHnqIvmtYHovazorrDlvZUgKi8NCiAgICBnZXRGbG93UmVjb3JkTGlzdChwcm9jSW5zSWQsIGRlcGxveUlkKSB7DQogICAgICBjb25zdCB0aGF0ID0gdGhpcw0KICAgICAgY29uc3QgcGFyYW1zID0ge3Byb2NJbnNJZDogcHJvY0luc0lkLCBkZXBsb3lJZDogZGVwbG95SWR9DQogICAgICBmbG93UmVjb3JkKHBhcmFtcykudGhlbihyZXMgPT4gew0KICAgICAgICB0aGF0LmZsb3dSZWNvcmRMaXN0ID0gcmVzLmRhdGEuZmxvd0xpc3Q7DQogICAgICAgIC8vIOWkhOeQhuWOhuWPsuiKgueCueaVsOaNrg0KICAgICAgICB0aGF0LnByb2Nlc3NGbG93UmVjb3JkcygpOw0KICAgICAgfSkuY2F0Y2gocmVzID0+IHsNCiAgICAgICAgdGhpcy5nb0JhY2soKTsNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5rWB56iL6IqC54K56KGo5Y2VICovDQogICAgZ2V0Rmxvd1Rhc2tGb3JtKHRhc2tJZCkgew0KICAgICAgaWYgKHRhc2tJZCkgew0KICAgICAgICAvLyDmj5DkuqTmtYHnqIvnlLPor7fml7bloavlhpnnmoTooajljZXlrZjlhaXkuobmtYHnqIvlj5jph4/kuK3lkI7nu63ku7vliqHlpITnkIbml7bpnIDopoHlsZXnpLoNCiAgICAgICAgZmxvd1Rhc2tGb3JtKHt0YXNrSWQ6IHRhc2tJZH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAvLyDmm7TmlrBmb3JtS2V55Lul5by65Yi26YeN5paw5riy5p+TVkZvcm3nu4Tku7bvvIzpgb/lhY1rZXnlhrLnqoENCiAgICAgICAgICB0aGlzLmZvcm1LZXkgPSBEYXRlLm5vdygpOw0KDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgLy8g5Zue5pi+6KGo5Y2VDQogICAgICAgICAgICB0aGlzLiRyZWZzLnZGb3JtUmVmLnNldEZvcm1Kc29uKHJlcy5kYXRhLmZvcm1Kc29uKTsNCiAgICAgICAgICAgIHRoaXMuZm9ybUpzb24gPSByZXMuZGF0YS5mb3JtSnNvbjsNCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgLy8g5Yqg6L296KGo5Y2V5aGr5YaZ55qE5pWw5o2uDQogICAgICAgICAgICAgIHRoaXMuJHJlZnMudkZvcm1SZWYuc2V0Rm9ybURhdGEocmVzLmRhdGEpOw0KICAgICAgICAgICAgICAvLyB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgIC8vICAgLy8g6KGo5Y2V56aB55SoDQogICAgICAgICAgICAgIC8vICAgdGhpcy4kcmVmcy52Rm9ybVJlZi5kaXNhYmxlRm9ybSgpOw0KICAgICAgICAgICAgICAvLyB9KQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWnlOa0vuS7u+WKoSAqLw0KICAgIGhhbmRsZURlbGVnYXRlKCkgew0KICAgICAgdGhpcy50YXNrRm9ybS5kZWxlZ2F0ZVRhc2tTaG93ID0gdHJ1ZTsNCiAgICAgIHRoaXMudGFza0Zvcm0uZGVmYXVsdFRhc2tTaG93ID0gZmFsc2U7DQogICAgfSwNCiAgICBoYW5kbGVBc3NpZ24oKSB7DQoNCiAgICB9LA0KICAgIC8qKiDov5Tlm57pobXpnaIgKi8NCiAgICBnb0JhY2soKSB7DQogICAgICAvLyDlhbPpl63lvZPliY3moIfnrb7pobXlubbov5Tlm57kuIrkuKrpobXpnaINCiAgICAgIGNvbnN0IG9iaiA9IHsgcGF0aDogIi90YXNrL3RvZG8iLCBxdWVyeTogeyB0OiBEYXRlLm5vdygpfSB9Ow0KICAgICAgdGhpcy4kdGFiLmNsb3NlT3BlblBhZ2Uob2JqKTsNCiAgICB9LA0KICAgIC8qKiDpqbPlm57ku7vliqEgKi8NCiAgICBoYW5kbGVSZWplY3QoKSB7DQogICAgICB0aGlzLnJlamVjdE9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5yZWplY3RUaXRsZSA9ICLpqbPlm57mtYHnqIsiOw0KICAgIH0sDQogICAgLyoqIOmps+WbnuS7u+WKoSAqLw0KICAgIHRhc2tSZWplY3QoKSB7DQogICAgICB0aGlzLiRyZWZzWyJ0YXNrRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgcmVqZWN0VGFzayh0aGlzLnRhc2tGb3JtKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHJlcy5tc2cpOw0KICAgICAgICAgICAgdGhpcy5nb0JhY2soKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Y+v6YCA5Zue5Lu75Yqh5YiX6KGoICovDQogICAgaGFuZGxlUmV0dXJuKCkgew0KICAgICAgdGhpcy5yZXR1cm5PcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMucmV0dXJuVGl0bGUgPSAi6YCA5Zue5rWB56iLIjsNCiAgICAgIHJldHVybkxpc3QodGhpcy50YXNrRm9ybSkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLnJldHVyblRhc2tMaXN0ID0gcmVzLmRhdGE7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOmAgOWbnuS7u+WKoSAqLw0KICAgIHRhc2tSZXR1cm4oKSB7DQogICAgICB0aGlzLiRyZWZzWyJ0YXNrRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgcmV0dXJuVGFzayh0aGlzLnRhc2tGb3JtKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHJlcy5tc2cpOw0KICAgICAgICAgICAgdGhpcy5nb0JhY2soKQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlj5bmtojlm57pgIDku7vliqHmjInpkq4gKi8NCiAgICBjYW5jZWxUYXNrKCkgew0KICAgICAgdGhpcy50YXNrRm9ybS5yZXR1cm5UYXNrU2hvdyA9IGZhbHNlOw0KICAgICAgdGhpcy50YXNrRm9ybS5kZWZhdWx0VGFza1Nob3cgPSB0cnVlOw0KICAgICAgdGhpcy5yZXR1cm5UYXNrTGlzdCA9IFtdOw0KICAgIH0sDQogICAgLyoqIOWnlOa0vuS7u+WKoSAqLw0KICAgIHN1Ym1pdERlbGV0ZVRhc2soKSB7DQogICAgICB0aGlzLiRyZWZzWyJ0YXNrRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgZGVsZWdhdGUodGhpcy50YXNrRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7DQogICAgICAgICAgICB0aGlzLmdvQmFjaygpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlj5bmtojlm57pgIDku7vliqHmjInpkq4gKi8NCiAgICBjYW5jZWxEZWxlZ2F0ZVRhc2soKSB7DQogICAgICB0aGlzLnRhc2tGb3JtLmRlbGVnYXRlVGFza1Nob3cgPSBmYWxzZTsNCiAgICAgIHRoaXMudGFza0Zvcm0uZGVmYXVsdFRhc2tTaG93ID0gdHJ1ZTsNCiAgICAgIHRoaXMucmV0dXJuVGFza0xpc3QgPSBbXTsNCiAgICB9LA0KICAgIC8qKiDliqDovb3lrqHmibnku7vliqHlvLnmoYYgKi8NCiAgICBoYW5kbGVDb21wbGV0ZSgpIHsNCiAgICAgIHRoaXMuY29tcGxldGVPcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMuY29tcGxldGVUaXRsZSA9ICLmtYHnqIvlrqHmibkiOw0KICAgICAgdGhpcy5zdWJtaXRGb3JtKCk7DQogICAgfSwNCiAgICAvKiog55So5oi35a6h5om55Lu75YqhICovDQogICAgdGFza0NvbXBsZXRlKCkgew0KICAgICAgaWYgKCF0aGlzLnRhc2tGb3JtLnZhcmlhYmxlcyAmJiB0aGlzLmNoZWNrU2VuZFVzZXIpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqea1geeoi+aOpeaUtuS6uuWRmCEiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLnRhc2tGb3JtLnZhcmlhYmxlcyAmJiB0aGlzLmNoZWNrU2VuZFJvbGUpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqea1geeoi+aOpeaUtuinkuiJsue7hCEiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLnRhc2tGb3JtLmNvbW1lbnQpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+i+k+WFpeWuoeaJueaEj+ingSEiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMudGFza0Zvcm0pIHsNCiAgICAgICAgY29tcGxldGUodGhpcy50YXNrRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2VzcyhyZXNwb25zZS5tc2cpOw0KICAgICAgICAgIHRoaXMuZ29CYWNrKCk7DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29tcGxldGUodGhpcy50YXNrRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2VzcyhyZXNwb25zZS5tc2cpOw0KICAgICAgICAgIHRoaXMuZ29CYWNrKCk7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOeUs+ivt+a1geeoi+ihqOWNleaVsOaNruaPkOS6pCAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICAvLyDmoLnmja7lvZPliY3ku7vliqHmiJbogIXmtYHnqIvorr7orqHphY3nva7nmoTkuIvkuIDmraXoioLngrkgdG9kbyDmmoLml7bmnKrmtonlj4rliLDogIPomZHnvZHlhbPjgIHooajovr7lvI/lkozlpJroioLngrnmg4XlhrUNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHt0YXNrSWQ6IHRoaXMudGFza0Zvcm0udGFza0lkfQ0KICAgICAgZ2V0TmV4dEZsb3dOb2RlKHBhcmFtcykudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLnZGb3JtUmVmLmdldEZvcm1EYXRhKCkudGhlbihmb3JtRGF0YSA9PiB7DQogICAgICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLnRhc2tGb3JtLnZhcmlhYmxlcywgZm9ybURhdGEpOw0KICAgICAgICAgIHRoaXMudGFza0Zvcm0udmFyaWFibGVzLmZvcm1Kc29uID0gdGhpcy5mb3JtSnNvbjsNCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLnRhc2tGb3JtLCAi5rWB56iL5a6h5om55o+Q5Lqk6KGo5Y2V5pWw5o2uMSIpDQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAvLyB0aGlzLiRtb2RhbC5tc2dFcnJvcihlcnJvcikNCiAgICAgICAgfSkNCiAgICAgICAgY29uc3QgZGF0YSA9IHJlcy5kYXRhOw0KICAgICAgICBpZiAoZGF0YSkgew0KICAgICAgICAgIGlmIChkYXRhLmRhdGFUeXBlID09PSAnZHluYW1pYycpIHsNCiAgICAgICAgICAgIGlmIChkYXRhLnR5cGUgPT09ICdhc3NpZ25lZScpIHsgLy8g5oyH5a6a5Lq65ZGYDQogICAgICAgICAgICAgIHRoaXMuY2hlY2tTZW5kVXNlciA9IHRydWU7DQogICAgICAgICAgICAgIHRoaXMuY2hlY2tUeXBlID0gInNpbmdsZSI7DQogICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gJ2NhbmRpZGF0ZVVzZXJzJykgeyAgLy8g5YCZ6YCJ5Lq65ZGYKOWkmuS4qikNCiAgICAgICAgICAgICAgdGhpcy5jaGVja1NlbmRVc2VyID0gdHJ1ZTsNCiAgICAgICAgICAgICAgdGhpcy5jaGVja1R5cGUgPSAibXVsdGlwbGUiOw0KICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICdjYW5kaWRhdGVHcm91cHMnKSB7IC8vIOaMh+Wumue7hCjmiYDlsZ7op5LoibLmjqXmlLbku7vliqEpDQogICAgICAgICAgICAgIHRoaXMuY2hlY2tTZW5kUm9sZSA9IHRydWU7DQogICAgICAgICAgICB9IGVsc2UgeyAvLyDkvJrnrb4NCiAgICAgICAgICAgICAgLy8g5rWB56iL6K6+6K6h5oyH5a6a55qEIGVsZW1lbnRWYXJpYWJsZSDkvZzkuLrkvJrnrb7kurrlkZjliJfooagNCiAgICAgICAgICAgICAgdGhpcy5tdWx0aUluc3RhbmNlVmFycyA9IGRhdGEudmFyczsNCiAgICAgICAgICAgICAgdGhpcy5jaGVja1NlbmRVc2VyID0gdHJ1ZTsNCiAgICAgICAgICAgICAgdGhpcy5jaGVja1R5cGUgPSAibXVsdGlwbGUiOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWKqOaAgee7keWumuaTjeS9nOaMiemSrueahOeCueWHu+S6i+S7tg0KICAgIGhhbmRsZUJ1dHRvbkNsaWNrKG1ldGhvZCkgew0KICAgICAgdGhpc1ttZXRob2RdKCk7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bljoblj7LoioLngrnmoIfpopggKi8NCiAgICBnZXRIaXN0b3J5VGl0bGUocmVjb3JkKSB7DQogICAgICByZXR1cm4gYCR7cmVjb3JkLnRhc2tOYW1lfSAtICR7cmVjb3JkLmFzc2lnbmVlTmFtZSB8fCAn5pyq5YiG6YWNJ30gLSAke3JlY29yZC5maW5pc2hUaW1lIHx8ICflpITnkIbkuK0nfWA7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bljoblj7LoioLngrnlm77moIcgKi8NCiAgICBnZXRIaXN0b3J5SWNvbihyZWNvcmQpIHsNCiAgICAgIGlmIChyZWNvcmQuZmluaXNoVGltZSkgew0KICAgICAgICByZXR1cm4gJ2VsLWljb24tY2hlY2snOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICdlbC1pY29uLXRpbWUnOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5Y6G5Y+y6IqC54K56aKc6ImyICovDQogICAgZ2V0SGlzdG9yeUNvbG9yKHJlY29yZCkgew0KICAgICAgaWYgKHJlY29yZC5maW5pc2hUaW1lKSB7DQogICAgICAgIHJldHVybiAnIzY3QzIzQSc7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJyNFNkEyM0MnOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W54q25oCB5qCH562+57G75Z6LICovDQogICAgZ2V0U3RhdHVzVGFnVHlwZShyZWNvcmQpIHsNCiAgICAgIGlmIChyZWNvcmQuZmluaXNoVGltZSkgew0KICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICd3YXJuaW5nJzsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlueKtuaAgeaWh+acrCAqLw0KICAgIGdldFN0YXR1c1RleHQocmVjb3JkKSB7DQogICAgICBpZiAocmVjb3JkLmZpbmlzaFRpbWUpIHsNCiAgICAgICAgcmV0dXJuICflt7LlrozmiJAnOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICflpITnkIbkuK0nOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG5rWB56iL6K6w5b2V5pWw5o2uICovDQogICAgcHJvY2Vzc0Zsb3dSZWNvcmRzKCkgew0KICAgICAgLy8g6buY6K6k5bGV5byA5pyA6L+R55qE5LiA5Liq6IqC54K577yI5aaC5p6c5pyJ5pWw5o2u55qE6K+d77yJDQogICAgICBpZiAodGhpcy5mbG93UmVjb3JkTGlzdCAmJiB0aGlzLmZsb3dSZWNvcmRMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgLy8g6buY6K6k5bGV5byA56ys5LiA5Liq6IqC54K5DQogICAgICAgIHRoaXMuYWN0aXZlSGlzdG9yeU5hbWVzID0gWydoaXN0b3J5LTAnXTsNCiAgICAgIH0NCiAgICB9DQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/task/todo/detail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">待办任务</span>\r\n        <el-tag style=\"margin-left:10px\">发起人:{{ startUser }}</el-tag>\r\n        <el-tag>任务节点:{{ taskName }}</el-tag>\r\n        <el-button style=\"float: right;\" size=\"mini\" type=\"danger\" @click=\"goBack\">关闭</el-button>\r\n      </div>\r\n      <el-tabs tab-position=\"top\" v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <!--表单信息-->\r\n        <el-tab-pane label=\"表单信息\" name=\"1\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <div v-if=\"flowRecordList && flowRecordList.length > 0\" style=\"margin-bottom: 20px;\">\r\n              <h4 style=\"margin-bottom: 15px; color: #606266;\">\r\n                <i class=\"el-icon-time\"></i> 流程历史记录\r\n              </h4>\r\n              <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\r\n                <el-collapse-item\r\n                  v-for=\"(record, index) in flowRecordList\"\r\n                  :key=\"`history-${index}-${record.taskId || record.id || index}`\"\r\n                  :name=\"`history-${index}`\"\r\n                >\r\n                  <template slot=\"title\">\r\n                    <div class=\"history-title\">\r\n                      <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\r\n                      <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\r\n                      <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\r\n                      <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\r\n                      <el-tag\r\n                        :type=\"getStatusTagType(record)\"\r\n                        size=\"mini\"\r\n                        style=\"margin-left: 10px;\"\r\n                      >\r\n                        {{ getStatusText(record) }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </template>\r\n\r\n                  <div class=\"history-content\">\r\n                    <el-descriptions :column=\"2\" size=\"small\" border>\r\n                      <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\r\n                        <span>{{ record.assigneeName }}</span>\r\n                        <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\r\n                        {{ record.candidate }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\r\n                        {{ record.createTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\r\n                        {{ record.finishTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\r\n                        {{ record.duration }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\r\n                        <div class=\"comment-content\">\r\n                          {{ record.comment.comment }}\r\n                        </div>\r\n                      </el-descriptions-item>\r\n                    </el-descriptions>\r\n                  </div>\r\n                </el-collapse-item>\r\n              </el-collapse>\r\n            </div>\r\n\r\n            <!-- 当前表单 -->\r\n            <el-card class=\"current-form-card\" shadow=\"hover\">\r\n              <div slot=\"header\" class=\"current-form-header\">\r\n                <i class=\"el-icon-edit-outline\"></i>\r\n                <span>当前待处理表单</span>\r\n              </div>\r\n              <v-form-render ref=\"vFormRef\" :key=\"formKey\"/>\r\n            </el-card>\r\n\r\n            <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;margin-top: 20px;\">\r\n              <el-button type=\"primary\" @click=\"handleComplete\">审 批</el-button>\r\n            </div>\r\n          </el-col>\r\n        </el-tab-pane>\r\n\r\n        <!--流程流转记录-->\r\n        <el-tab-pane label=\"流转记录\" name=\"2\">\r\n          <!--flowRecordList-->\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <div class=\"block\">\r\n              <el-timeline>\r\n                <el-timeline-item\r\n                  v-for=\"(item,index ) in flowRecordList\"\r\n                  :key=\"index\"\r\n                  :icon=\"setIcon(item.finishTime)\"\r\n                  :color=\"setColor(item.finishTime)\"\r\n                >\r\n                  <p style=\"font-weight: 700\">{{ item.taskName }}</p>\r\n                  <el-card :body-style=\"{ padding: '10px' }\">\r\n                    <el-descriptions class=\"margin-top\" :column=\"1\" size=\"small\" border>\r\n                      <el-descriptions-item v-if=\"item.assigneeName\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-user\"></i>办理人</template>\r\n                        {{ item.assigneeName }}\r\n                        <el-tag type=\"info\" size=\"mini\">{{ item.deptName }}</el-tag>\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.candidate\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-user\"></i>候选办理</template>\r\n                        {{ item.candidate }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-date\"></i>接收时间</template>\r\n                        {{ item.createTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.finishTime\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-date\"></i>处理时间</template>\r\n                        {{ item.finishTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.duration\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-time\"></i>耗时</template>\r\n                        {{ item.duration }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.comment\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-tickets\"></i>处理意见</template>\r\n                        {{ item.comment.comment }}\r\n                      </el-descriptions-item>\r\n                    </el-descriptions>\r\n                  </el-card>\r\n                </el-timeline-item>\r\n              </el-timeline>\r\n            </div>\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <!--流程图-->\r\n        <el-tab-pane label=\"流程图\" name=\"3\">\r\n          <bpmn-viewer :flowData=\"flowData\" :procInsId=\"taskForm.procInsId\"/>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n      <!--审批任务-->\r\n      <el-dialog :title=\"completeTitle\" :visible.sync=\"completeOpen\" width=\"60%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\">\r\n          <el-form-item prop=\"targetKey\">\r\n            <flow-user v-if=\"checkSendUser\" :checkType=\"checkType\" @handleUserSelect=\"handleUserSelect\"></flow-user>\r\n            <flow-role v-if=\"checkSendRole\" @handleRoleSelect=\"handleRoleSelect\"></flow-role>\r\n          </el-form-item>\r\n          <el-form-item label=\"处理意见\" label-width=\"80px\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入处理意见', trigger: 'blur' }]\">\r\n            <el-input type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入处理意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"completeOpen = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\r\n        </span>\r\n      </el-dialog>\r\n      <!--退回流程-->\r\n      <el-dialog :title=\"returnTitle\" :visible.sync=\"returnOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"退回节点\" prop=\"targetKey\">\r\n            <el-radio-group v-model=\"taskForm.targetKey\">\r\n              <el-radio-button\r\n                v-for=\"item in returnTaskList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.id\"\r\n              >{{ item.name }}\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"退回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"returnOpen = false\">取 消</el-button>\r\n              <el-button type=\"primary\" @click=\"taskReturn\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n      <!--驳回流程-->\r\n      <el-dialog :title=\"rejectTitle\" :visible.sync=\"rejectOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"驳回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"rejectOpen = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"taskReject\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {flowRecord} from \"@/api/flowable/finished\";\r\nimport FlowUser from '@/components/flow/User'\r\nimport FlowRole from '@/components/flow/Role'\r\nimport {flowXmlAndNode} from \"@/api/flowable/definition\";\r\nimport {\r\n  complete,\r\n  rejectTask,\r\n  returnList,\r\n  returnTask,\r\n  getNextFlowNode,\r\n  delegate,\r\n  flowTaskForm\r\n} from \"@/api/flowable/todo\";\r\nimport BpmnViewer from '@/components/Process/viewer';\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Record\",\r\n  components: {\r\n    BpmnViewer,\r\n    FlowUser,\r\n    FlowRole,\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      eventName: \"click\",\r\n      // 流程数据\r\n      flowData: {},\r\n      activeName: '1',\r\n      // 遮罩层\r\n      loading: true,\r\n      flowRecordList: [], // 流程流转数据\r\n      rules: {}, // 表单校验\r\n      taskForm: {\r\n        returnTaskShow: false, // 是否展示回退表单\r\n        delegateTaskShow: false, // 是否展示回退表单\r\n        defaultTaskShow: true, // 默认处理\r\n        comment: \"\", // 意见内容\r\n        procInsId: \"\", // 流程实例编号\r\n        instanceId: \"\", // 流程实例编号\r\n        deployId: \"\",  // 流程定义编号\r\n        taskId: \"\",// 流程任务编号\r\n        procDefId: \"\",  // 流程编号\r\n        targetKey: \"\",\r\n        variables: {},\r\n      },\r\n      returnTaskList: [],  // 回退列表数据\r\n      completeTitle: null,\r\n      completeOpen: false,\r\n      returnTitle: null,\r\n      returnOpen: false,\r\n      rejectOpen: false,\r\n      rejectTitle: null,\r\n      checkSendUser: false, // 是否展示人员选择模块\r\n      checkSendRole: false,// 是否展示角色选择模块\r\n      checkType: 'single', // 选择类型\r\n      taskName: null, // 任务节点\r\n      startUser: null, // 发起人信息,\r\n      multiInstanceVars: '', // 会签节点\r\n      formJson:{},\r\n      activeHistoryNames: [], // 展开的历史节点\r\n      completedFlowRecords: [], // 已完成的流程记录\r\n      formKey: Date.now() // VForm组件的唯一key\r\n    };\r\n  },\r\n  created() {\r\n    if (this.$route.query) {\r\n      this.taskName = this.$route.query.taskName;\r\n      this.startUser = this.$route.query.startUser;\r\n      this.taskForm.deployId = this.$route.query.deployId;\r\n      this.taskForm.taskId = this.$route.query.taskId;\r\n      this.taskForm.procInsId = this.$route.query.procInsId;\r\n      this.taskForm.executionId = this.$route.query.executionId;\r\n      this.taskForm.instanceId = this.$route.query.procInsId;\r\n      // 流程任务获取变量信息\r\n      if (this.taskForm.taskId) {\r\n        this.getFlowTaskForm(this.taskForm.taskId);\r\n      }\r\n      this.getFlowRecordList(this.taskForm.procInsId, this.taskForm.deployId);\r\n    }\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      if (tab.name === '3') {\r\n        flowXmlAndNode({procInsId: this.taskForm.procInsId, deployId: this.taskForm.deployId}).then(res => {\r\n          this.flowData = res.data;\r\n        })\r\n      }\r\n    },\r\n    setIcon(val) {\r\n      if (val) {\r\n        return \"el-icon-check\";\r\n      } else {\r\n        return \"el-icon-time\";\r\n      }\r\n    },\r\n    setColor(val) {\r\n      if (val) {\r\n        return \"#2bc418\";\r\n      } else {\r\n        return \"#b3bdbb\";\r\n      }\r\n    },\r\n    // 用户信息选中数据\r\n    handleUserSelect(selection) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.userId.toString());\r\n          if (this.multiInstanceVars) {\r\n            this.$set(this.taskForm.variables, this.multiInstanceVars,  selectVal);\r\n          } else {\r\n            this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n          }\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection.userId.toString());\r\n        }\r\n      }\r\n    },\r\n    // 角色信息选中数据\r\n    handleRoleSelect(selection, roleName) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.roleId.toString());\r\n          this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection);\r\n        }\r\n      }\r\n    },\r\n    /** 流程流转记录 */\r\n    getFlowRecordList(procInsId, deployId) {\r\n      const that = this\r\n      const params = {procInsId: procInsId, deployId: deployId}\r\n      flowRecord(params).then(res => {\r\n        that.flowRecordList = res.data.flowList;\r\n        // 处理历史节点数据\r\n        that.processFlowRecords();\r\n      }).catch(res => {\r\n        this.goBack();\r\n      })\r\n    },\r\n    /** 流程节点表单 */\r\n    getFlowTaskForm(taskId) {\r\n      if (taskId) {\r\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\r\n        flowTaskForm({taskId: taskId}).then(res => {\r\n          // 更新formKey以强制重新渲染VForm组件，避免key冲突\r\n          this.formKey = Date.now();\r\n\r\n          this.$nextTick(() => {\r\n            // 回显表单\r\n            this.$refs.vFormRef.setFormJson(res.data.formJson);\r\n            this.formJson = res.data.formJson;\r\n            this.$nextTick(() => {\r\n              // 加载表单填写的数据\r\n              this.$refs.vFormRef.setFormData(res.data);\r\n              // this.$nextTick(() => {\r\n              //   // 表单禁用\r\n              //   this.$refs.vFormRef.disableForm();\r\n              // })\r\n            })\r\n          })\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 委派任务 */\r\n    handleDelegate() {\r\n      this.taskForm.delegateTaskShow = true;\r\n      this.taskForm.defaultTaskShow = false;\r\n    },\r\n    handleAssign() {\r\n\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      const obj = { path: \"/task/todo\", query: { t: Date.now()} };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 驳回任务 */\r\n    handleReject() {\r\n      this.rejectOpen = true;\r\n      this.rejectTitle = \"驳回流程\";\r\n    },\r\n    /** 驳回任务 */\r\n    taskReject() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          rejectTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 可退回任务列表 */\r\n    handleReturn() {\r\n      this.returnOpen = true;\r\n      this.returnTitle = \"退回流程\";\r\n      returnList(this.taskForm).then(res => {\r\n        this.returnTaskList = res.data;\r\n      })\r\n    },\r\n    /** 提交退回任务 */\r\n    taskReturn() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          returnTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack()\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelTask() {\r\n      this.taskForm.returnTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 委派任务 */\r\n    submitDeleteTask() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          delegate(this.taskForm).then(response => {\r\n            this.$modal.msgSuccess(response.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelDelegateTask() {\r\n      this.taskForm.delegateTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 加载审批任务弹框 */\r\n    handleComplete() {\r\n      this.completeOpen = true;\r\n      this.completeTitle = \"流程审批\";\r\n      this.submitForm();\r\n    },\r\n    /** 用户审批任务 */\r\n    taskComplete() {\r\n      if (!this.taskForm.variables && this.checkSendUser) {\r\n        this.$modal.msgError(\"请选择流程接收人员!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.variables && this.checkSendRole) {\r\n        this.$modal.msgError(\"请选择流程接收角色组!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.comment) {\r\n        this.$modal.msgError(\"请输入审批意见!\");\r\n        return;\r\n      }\r\n      if (this.taskForm) {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      } else {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      }\r\n    },\r\n    /** 申请流程表单数据提交 */\r\n    submitForm() {\r\n      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况\r\n      const params = {taskId: this.taskForm.taskId}\r\n      getNextFlowNode(params).then(res => {\r\n        this.$refs.vFormRef.getFormData().then(formData => {\r\n          Object.assign(this.taskForm.variables, formData);\r\n          this.taskForm.variables.formJson = this.formJson;\r\n          console.log(this.taskForm, \"流程审批提交表单数据1\")\r\n        }).catch(error => {\r\n          // this.$modal.msgError(error)\r\n        })\r\n        const data = res.data;\r\n        if (data) {\r\n          if (data.dataType === 'dynamic') {\r\n            if (data.type === 'assignee') { // 指定人员\r\n              this.checkSendUser = true;\r\n              this.checkType = \"single\";\r\n            } else if (data.type === 'candidateUsers') {  // 候选人员(多个)\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            } else if (data.type === 'candidateGroups') { // 指定组(所属角色接收任务)\r\n              this.checkSendRole = true;\r\n            } else { // 会签\r\n              // 流程设计指定的 elementVariable 作为会签人员列表\r\n              this.multiInstanceVars = data.vars;\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 动态绑定操作按钮的点击事件\r\n    handleButtonClick(method) {\r\n      this[method]();\r\n    },\r\n\r\n    /** 获取历史节点标题 */\r\n    getHistoryTitle(record) {\r\n      return `${record.taskName} - ${record.assigneeName || '未分配'} - ${record.finishTime || '处理中'}`;\r\n    },\r\n\r\n    /** 获取历史节点图标 */\r\n    getHistoryIcon(record) {\r\n      if (record.finishTime) {\r\n        return 'el-icon-check';\r\n      } else {\r\n        return 'el-icon-time';\r\n      }\r\n    },\r\n\r\n    /** 获取历史节点颜色 */\r\n    getHistoryColor(record) {\r\n      if (record.finishTime) {\r\n        return '#67C23A';\r\n      } else {\r\n        return '#E6A23C';\r\n      }\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(record) {\r\n      if (record.finishTime) {\r\n        return 'success';\r\n      } else {\r\n        return 'warning';\r\n      }\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(record) {\r\n      if (record.finishTime) {\r\n        return '已完成';\r\n      } else {\r\n        return '处理中';\r\n      }\r\n    },\r\n\r\n    /** 处理流程记录数据 */\r\n    processFlowRecords() {\r\n      // 默认展开最近的一个节点（如果有数据的话）\r\n      if (this.flowRecordList && this.flowRecordList.length > 0) {\r\n        // 默认展开第一个节点\r\n        this.activeHistoryNames = ['history-0'];\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.test-form {\r\n  margin: 15px auto;\r\n  width: 800px;\r\n  padding: 15px;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both\r\n}\r\n\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.my-label {\r\n  background: #E1F3D8;\r\n}\r\n\r\n/* 历史节点样式 */\r\n.history-collapse {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-collapse .el-collapse-item__header {\r\n  background-color: #F5F7FA;\r\n  border-bottom: 1px solid #EBEEF5;\r\n  padding: 0 20px;\r\n  height: 48px;\r\n  line-height: 48px;\r\n}\r\n\r\n.history-collapse .el-collapse-item__content {\r\n  padding: 20px;\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.history-title {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n}\r\n\r\n.history-title .node-name {\r\n  font-weight: 600;\r\n  margin-left: 8px;\r\n  margin-right: 15px;\r\n  color: #303133;\r\n}\r\n\r\n.history-title .assignee-name {\r\n  color: #606266;\r\n  margin-right: 15px;\r\n}\r\n\r\n.history-title .finish-time {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-left: auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.history-content {\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n}\r\n\r\n.comment-content {\r\n  background-color: #F8F9FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  font-style: italic;\r\n  color: #606266;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 16px;\r\n}\r\n\r\n.current-form-card {\r\n  border: 2px solid #409EFF;\r\n}\r\n\r\n.current-form-header {\r\n  color: #409EFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.current-form-header i {\r\n  margin-right: 8px;\r\n}\r\n</style>\r\n"]}]}