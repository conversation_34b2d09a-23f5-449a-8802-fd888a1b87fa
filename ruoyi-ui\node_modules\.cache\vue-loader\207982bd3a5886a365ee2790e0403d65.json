{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue", "mtime": 1752407558498}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2Zsb3dSZWNvcmR9IGZyb20gIkAvYXBpL2Zsb3dhYmxlL2ZpbmlzaGVkIjsNCmltcG9ydCBGbG93VXNlciBmcm9tICdAL2NvbXBvbmVudHMvZmxvdy9Vc2VyJw0KaW1wb3J0IEZsb3dSb2xlIGZyb20gJ0AvY29tcG9uZW50cy9mbG93L1JvbGUnDQppbXBvcnQge2Zsb3dYbWxBbmROb2RlfSBmcm9tICJAL2FwaS9mbG93YWJsZS9kZWZpbml0aW9uIjsNCmltcG9ydCB7DQogIGNvbXBsZXRlLA0KICByZWplY3RUYXNrLA0KICByZXR1cm5MaXN0LA0KICByZXR1cm5UYXNrLA0KICBnZXROZXh0Rmxvd05vZGUsDQogIGRlbGVnYXRlLA0KICBmbG93VGFza0Zvcm0NCn0gZnJvbSAiQC9hcGkvZmxvd2FibGUvdG9kbyI7DQppbXBvcnQgQnBtblZpZXdlciBmcm9tICdAL2NvbXBvbmVudHMvUHJvY2Vzcy92aWV3ZXInOw0KaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlJlY29yZCIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBCcG1uVmlld2VyLA0KICAgIEZsb3dVc2VyLA0KICAgIEZsb3dSb2xlLA0KICB9LA0KICBwcm9wczoge30sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGV2ZW50TmFtZTogImNsaWNrIiwNCiAgICAgIC8vIOa1geeoi+aVsOaNrg0KICAgICAgZmxvd0RhdGE6IHt9LA0KICAgICAgYWN0aXZlTmFtZTogJzEnLA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgZmxvd1JlY29yZExpc3Q6IFtdLCAvLyDmtYHnqIvmtYHovazmlbDmja4NCiAgICAgIHJ1bGVzOiB7fSwgLy8g6KGo5Y2V5qCh6aqMDQogICAgICB0YXNrRm9ybTogew0KICAgICAgICByZXR1cm5UYXNrU2hvdzogZmFsc2UsIC8vIOaYr+WQpuWxleekuuWbnumAgOihqOWNlQ0KICAgICAgICBkZWxlZ2F0ZVRhc2tTaG93OiBmYWxzZSwgLy8g5piv5ZCm5bGV56S65Zue6YCA6KGo5Y2VDQogICAgICAgIGRlZmF1bHRUYXNrU2hvdzogdHJ1ZSwgLy8g6buY6K6k5aSE55CGDQogICAgICAgIGNvbW1lbnQ6ICIiLCAvLyDmhI/op4HlhoXlrrkNCiAgICAgICAgcHJvY0luc0lkOiAiIiwgLy8g5rWB56iL5a6e5L6L57yW5Y+3DQogICAgICAgIGluc3RhbmNlSWQ6ICIiLCAvLyDmtYHnqIvlrp7kvovnvJblj7cNCiAgICAgICAgZGVwbG95SWQ6ICIiLCAgLy8g5rWB56iL5a6a5LmJ57yW5Y+3DQogICAgICAgIHRhc2tJZDogIiIsLy8g5rWB56iL5Lu75Yqh57yW5Y+3DQogICAgICAgIHByb2NEZWZJZDogIiIsICAvLyDmtYHnqIvnvJblj7cNCiAgICAgICAgdGFyZ2V0S2V5OiAiIiwNCiAgICAgICAgdmFyaWFibGVzOiB7fSwNCiAgICAgIH0sDQogICAgICByZXR1cm5UYXNrTGlzdDogW10sICAvLyDlm57pgIDliJfooajmlbDmja4NCiAgICAgIGNvbXBsZXRlVGl0bGU6IG51bGwsDQogICAgICBjb21wbGV0ZU9wZW46IGZhbHNlLA0KICAgICAgcmV0dXJuVGl0bGU6IG51bGwsDQogICAgICByZXR1cm5PcGVuOiBmYWxzZSwNCiAgICAgIHJlamVjdE9wZW46IGZhbHNlLA0KICAgICAgcmVqZWN0VGl0bGU6IG51bGwsDQogICAgICBjaGVja1NlbmRVc2VyOiBmYWxzZSwgLy8g5piv5ZCm5bGV56S65Lq65ZGY6YCJ5oup5qih5Z2XDQogICAgICBjaGVja1NlbmRSb2xlOiBmYWxzZSwvLyDmmK/lkKblsZXnpLrop5LoibLpgInmi6nmqKHlnZcNCiAgICAgIGNoZWNrVHlwZTogJ3NpbmdsZScsIC8vIOmAieaLqeexu+Weiw0KICAgICAgdGFza05hbWU6IG51bGwsIC8vIOS7u+WKoeiKgueCuQ0KICAgICAgc3RhcnRVc2VyOiBudWxsLCAvLyDlj5Hotbfkurrkv6Hmga8sDQogICAgICBtdWx0aUluc3RhbmNlVmFyczogJycsIC8vIOS8muetvuiKgueCuQ0KICAgICAgZm9ybUpzb246e30sDQogICAgICBhY3RpdmVIaXN0b3J5TmFtZXM6IFtdLCAvLyDlsZXlvIDnmoTljoblj7LoioLngrkNCiAgICAgIGNvbXBsZXRlZEZsb3dSZWNvcmRzOiBbXSwgLy8g5bey5a6M5oiQ55qE5rWB56iL6K6w5b2VDQogICAgICBmb3JtS2V5OiBEYXRlLm5vdygpIC8vIFZGb3Jt57uE5Lu255qE5ZSv5LiAa2V5DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBpZiAodGhpcy4kcm91dGUucXVlcnkpIHsNCiAgICAgIHRoaXMudGFza05hbWUgPSB0aGlzLiRyb3V0ZS5xdWVyeS50YXNrTmFtZTsNCiAgICAgIHRoaXMuc3RhcnRVc2VyID0gdGhpcy4kcm91dGUucXVlcnkuc3RhcnRVc2VyOw0KICAgICAgdGhpcy50YXNrRm9ybS5kZXBsb3lJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmRlcGxveUlkOw0KICAgICAgdGhpcy50YXNrRm9ybS50YXNrSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS50YXNrSWQ7DQogICAgICB0aGlzLnRhc2tGb3JtLnByb2NJbnNJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsNCiAgICAgIHRoaXMudGFza0Zvcm0uZXhlY3V0aW9uSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5leGVjdXRpb25JZDsNCiAgICAgIHRoaXMudGFza0Zvcm0uaW5zdGFuY2VJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnByb2NJbnNJZDsNCiAgICAgIC8vIOa1geeoi+S7u+WKoeiOt+WPluWPmOmHj+S/oeaBrw0KICAgICAgaWYgKHRoaXMudGFza0Zvcm0udGFza0lkKSB7DQogICAgICAgIHRoaXMuZ2V0Rmxvd1Rhc2tGb3JtKHRoaXMudGFza0Zvcm0udGFza0lkKTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0Rmxvd1JlY29yZExpc3QodGhpcy50YXNrRm9ybS5wcm9jSW5zSWQsIHRoaXMudGFza0Zvcm0uZGVwbG95SWQpOw0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNsaWNrKHRhYikgew0KICAgICAgaWYgKHRhYi5uYW1lID09PSAnMycpIHsNCiAgICAgICAgZmxvd1htbEFuZE5vZGUoe3Byb2NJbnNJZDogdGhpcy50YXNrRm9ybS5wcm9jSW5zSWQsIGRlcGxveUlkOiB0aGlzLnRhc2tGb3JtLmRlcGxveUlkfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIHRoaXMuZmxvd0RhdGEgPSByZXMuZGF0YTsNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOeUqOaIt+S/oeaBr+mAieS4reaVsOaNrg0KICAgIGhhbmRsZVVzZXJTZWxlY3Qoc2VsZWN0aW9uKSB7DQogICAgICBpZiAoc2VsZWN0aW9uKSB7DQogICAgICAgIGlmIChzZWxlY3Rpb24gaW5zdGFuY2VvZiBBcnJheSkgew0KICAgICAgICAgIGNvbnN0IHNlbGVjdFZhbCA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnVzZXJJZC50b1N0cmluZygpKTsNCiAgICAgICAgICBpZiAodGhpcy5tdWx0aUluc3RhbmNlVmFycykgew0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMudGFza0Zvcm0udmFyaWFibGVzLCB0aGlzLm11bHRpSW5zdGFuY2VWYXJzLCAgc2VsZWN0VmFsKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMudGFza0Zvcm0udmFyaWFibGVzLCAiYXBwcm92YWwiLCBzZWxlY3RWYWwuam9pbignLCcpKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMudGFza0Zvcm0udmFyaWFibGVzLCAiYXBwcm92YWwiLCBzZWxlY3Rpb24udXNlcklkLnRvU3RyaW5nKCkpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDop5LoibLkv6Hmga/pgInkuK3mlbDmja4NCiAgICBoYW5kbGVSb2xlU2VsZWN0KHNlbGVjdGlvbikgew0KICAgICAgaWYgKHNlbGVjdGlvbikgew0KICAgICAgICBpZiAoc2VsZWN0aW9uIGluc3RhbmNlb2YgQXJyYXkpIHsNCiAgICAgICAgICBjb25zdCBzZWxlY3RWYWwgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5yb2xlSWQudG9TdHJpbmcoKSk7DQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMudGFza0Zvcm0udmFyaWFibGVzLCAiYXBwcm92YWwiLCBzZWxlY3RWYWwuam9pbignLCcpKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy50YXNrRm9ybS52YXJpYWJsZXMsICJhcHByb3ZhbCIsIHNlbGVjdGlvbik7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmtYHnqIvmtYHovazorrDlvZUgKi8NCiAgICBnZXRGbG93UmVjb3JkTGlzdChwcm9jSW5zSWQsIGRlcGxveUlkKSB7DQogICAgICBjb25zdCB0aGF0ID0gdGhpcw0KICAgICAgY29uc3QgcGFyYW1zID0ge3Byb2NJbnNJZDogcHJvY0luc0lkLCBkZXBsb3lJZDogZGVwbG95SWR9DQogICAgICBmbG93UmVjb3JkKHBhcmFtcykudGhlbihyZXMgPT4gew0KICAgICAgICB0aGF0LmZsb3dSZWNvcmRMaXN0ID0gcmVzLmRhdGEuZmxvd0xpc3Q7DQogICAgICAgIC8vIOWkhOeQhuWOhuWPsuiKgueCueaVsOaNrg0KICAgICAgICB0aGF0LnByb2Nlc3NGbG93UmVjb3JkcygpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmdvQmFjaygpOw0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDmtYHnqIvoioLngrnooajljZUgKi8NCiAgICBnZXRGbG93VGFza0Zvcm0odGFza0lkKSB7DQogICAgICBpZiAodGFza0lkKSB7DQogICAgICAgIC8vIOaPkOS6pOa1geeoi+eUs+ivt+aXtuWhq+WGmeeahOihqOWNleWtmOWFpeS6hua1geeoi+WPmOmHj+S4reWQjue7reS7u+WKoeWkhOeQhuaXtumcgOimgeWxleekug0KICAgICAgICBmbG93VGFza0Zvcm0oe3Rhc2tJZDogdGFza0lkfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIC8vIOabtOaWsGZvcm1LZXnku6XlvLrliLbph43mlrDmuLLmn5NWRm9ybee7hOS7tu+8jOmBv+WFjWtleeWGsueqgQ0KICAgICAgICAgIHRoaXMuZm9ybUtleSA9IERhdGUubm93KCk7DQoNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAvLyDlm57mmL7ooajljZUNCiAgICAgICAgICAgIHRoaXMuJHJlZnMudkZvcm1SZWYuc2V0Rm9ybUpzb24ocmVzLmRhdGEuZm9ybUpzb24pOw0KICAgICAgICAgICAgdGhpcy5mb3JtSnNvbiA9IHJlcy5kYXRhLmZvcm1Kc29uOw0KICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICAvLyDliqDovb3ooajljZXloavlhpnnmoTmlbDmja4NCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy52Rm9ybVJlZi5zZXRGb3JtRGF0YShyZXMuZGF0YSk7DQogICAgICAgICAgICAgIC8vIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgLy8gICAvLyDooajljZXnpoHnlKgNCiAgICAgICAgICAgICAgLy8gICB0aGlzLiRyZWZzLnZGb3JtUmVmLmRpc2FibGVGb3JtKCk7DQogICAgICAgICAgICAgIC8vIH0pDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5aeU5rS+5Lu75YqhICovDQogICAgaGFuZGxlRGVsZWdhdGUoKSB7DQogICAgICB0aGlzLnRhc2tGb3JtLmRlbGVnYXRlVGFza1Nob3cgPSB0cnVlOw0KICAgICAgdGhpcy50YXNrRm9ybS5kZWZhdWx0VGFza1Nob3cgPSBmYWxzZTsNCiAgICB9LA0KICAgIGhhbmRsZUFzc2lnbigpIHsNCg0KICAgIH0sDQogICAgLyoqIOi/lOWbnumhtemdoiAqLw0KICAgIGdvQmFjaygpIHsNCiAgICAgIC8vIOWFs+mXreW9k+WJjeagh+etvumhteW5tui/lOWbnuS4iuS4qumhtemdog0KICAgICAgY29uc3Qgb2JqID0geyBwYXRoOiAiL3Rhc2svdG9kbyIsIHF1ZXJ5OiB7IHQ6IERhdGUubm93KCl9IH07DQogICAgICB0aGlzLiR0YWIuY2xvc2VPcGVuUGFnZShvYmopOw0KICAgIH0sDQogICAgLyoqIOmps+WbnuS7u+WKoSAqLw0KICAgIGhhbmRsZVJlamVjdCgpIHsNCiAgICAgIHRoaXMucmVqZWN0T3BlbiA9IHRydWU7DQogICAgICB0aGlzLnJlamVjdFRpdGxlID0gIumps+Wbnua1geeoiyI7DQogICAgfSwNCiAgICAvKiog6amz5Zue5Lu75YqhICovDQogICAgdGFza1JlamVjdCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbInRhc2tGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICByZWplY3RUYXNrKHRoaXMudGFza0Zvcm0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7DQogICAgICAgICAgICB0aGlzLmdvQmFjaygpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlj6/pgIDlm57ku7vliqHliJfooaggKi8NCiAgICBoYW5kbGVSZXR1cm4oKSB7DQogICAgICB0aGlzLnJldHVybk9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5yZXR1cm5UaXRsZSA9ICLpgIDlm57mtYHnqIsiOw0KICAgICAgcmV0dXJuTGlzdCh0aGlzLnRhc2tGb3JtKS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMucmV0dXJuVGFza0xpc3QgPSByZXMuZGF0YTsNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5o+Q5Lqk6YCA5Zue5Lu75YqhICovDQogICAgdGFza1JldHVybigpIHsNCiAgICAgIHRoaXMuJHJlZnNbInRhc2tGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICByZXR1cm5UYXNrKHRoaXMudGFza0Zvcm0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7DQogICAgICAgICAgICB0aGlzLmdvQmFjaygpDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWPlua2iOWbnumAgOS7u+WKoeaMiemSriAqLw0KICAgIGNhbmNlbFRhc2soKSB7DQogICAgICB0aGlzLnRhc2tGb3JtLnJldHVyblRhc2tTaG93ID0gZmFsc2U7DQogICAgICB0aGlzLnRhc2tGb3JtLmRlZmF1bHRUYXNrU2hvdyA9IHRydWU7DQogICAgICB0aGlzLnJldHVyblRhc2tMaXN0ID0gW107DQogICAgfSwNCiAgICAvKiog5aeU5rS+5Lu75YqhICovDQogICAgc3VibWl0RGVsZXRlVGFzaygpIHsNCiAgICAgIHRoaXMuJHJlZnNbInRhc2tGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBkZWxlZ2F0ZSh0aGlzLnRhc2tGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsNCiAgICAgICAgICAgIHRoaXMuZ29CYWNrKCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWPlua2iOWbnumAgOS7u+WKoeaMiemSriAqLw0KICAgIGNhbmNlbERlbGVnYXRlVGFzaygpIHsNCiAgICAgIHRoaXMudGFza0Zvcm0uZGVsZWdhdGVUYXNrU2hvdyA9IGZhbHNlOw0KICAgICAgdGhpcy50YXNrRm9ybS5kZWZhdWx0VGFza1Nob3cgPSB0cnVlOw0KICAgICAgdGhpcy5yZXR1cm5UYXNrTGlzdCA9IFtdOw0KICAgIH0sDQogICAgLyoqIOWKoOi9veWuoeaJueS7u+WKoeW8ueahhiAqLw0KICAgIGhhbmRsZUNvbXBsZXRlKCkgew0KICAgICAgdGhpcy5jb21wbGV0ZU9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5jb21wbGV0ZVRpdGxlID0gIua1geeoi+WuoeaJuSI7DQogICAgICB0aGlzLnN1Ym1pdEZvcm0oKTsNCiAgICB9LA0KICAgIC8qKiDnlKjmiLflrqHmibnku7vliqEgKi8NCiAgICB0YXNrQ29tcGxldGUoKSB7DQogICAgICBpZiAoIXRoaXMudGFza0Zvcm0udmFyaWFibGVzICYmIHRoaXMuY2hlY2tTZW5kVXNlcikgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup5rWB56iL5o6l5pS25Lq65ZGYISIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBpZiAoIXRoaXMudGFza0Zvcm0udmFyaWFibGVzICYmIHRoaXMuY2hlY2tTZW5kUm9sZSkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup5rWB56iL5o6l5pS26KeS6Imy57uEISIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBpZiAoIXRoaXMudGFza0Zvcm0uY29tbWVudCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36L6T5YWl5a6h5om55oSP6KeBISIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBpZiAodGhpcy50YXNrRm9ybSkgew0KICAgICAgICBjb21wbGV0ZSh0aGlzLnRhc2tGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7DQogICAgICAgICAgdGhpcy5nb0JhY2soKTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb21wbGV0ZSh0aGlzLnRhc2tGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7DQogICAgICAgICAgdGhpcy5nb0JhY2soKTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog55Sz6K+35rWB56iL6KGo5Y2V5pWw5o2u5o+Q5LqkICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIC8vIOagueaNruW9k+WJjeS7u+WKoeaIluiAhea1geeoi+iuvuiuoemFjee9rueahOS4i+S4gOatpeiKgueCuSB0b2RvIOaaguaXtuacqua2ieWPiuWIsOiAg+iZkee9keWFs+OAgeihqOi+vuW8j+WSjOWkmuiKgueCueaDheWGtQ0KICAgICAgY29uc3QgcGFyYW1zID0ge3Rhc2tJZDogdGhpcy50YXNrRm9ybS50YXNrSWR9DQogICAgICBnZXROZXh0Rmxvd05vZGUocGFyYW1zKS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMudkZvcm1SZWYuZ2V0Rm9ybURhdGEoKS50aGVuKGZvcm1EYXRhID0+IHsNCiAgICAgICAgICBPYmplY3QuYXNzaWduKHRoaXMudGFza0Zvcm0udmFyaWFibGVzLCBmb3JtRGF0YSk7DQogICAgICAgICAgdGhpcy50YXNrRm9ybS52YXJpYWJsZXMuZm9ybUpzb24gPSB0aGlzLmZvcm1Kc29uOw0KICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMudGFza0Zvcm0sICLmtYHnqIvlrqHmibnmj5DkuqTooajljZXmlbDmja4xIikNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIC8vIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGVycm9yKQ0KICAgICAgICB9KQ0KICAgICAgICBjb25zdCBkYXRhID0gcmVzLmRhdGE7DQogICAgICAgIGlmIChkYXRhKSB7DQogICAgICAgICAgaWYgKGRhdGEuZGF0YVR5cGUgPT09ICdkeW5hbWljJykgew0KICAgICAgICAgICAgaWYgKGRhdGEudHlwZSA9PT0gJ2Fzc2lnbmVlJykgeyAvLyDmjIflrprkurrlkZgNCiAgICAgICAgICAgICAgdGhpcy5jaGVja1NlbmRVc2VyID0gdHJ1ZTsNCiAgICAgICAgICAgICAgdGhpcy5jaGVja1R5cGUgPSAic2luZ2xlIjsNCiAgICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSAnY2FuZGlkYXRlVXNlcnMnKSB7ICAvLyDlgJnpgInkurrlkZgo5aSa5LiqKQ0KICAgICAgICAgICAgICB0aGlzLmNoZWNrU2VuZFVzZXIgPSB0cnVlOw0KICAgICAgICAgICAgICB0aGlzLmNoZWNrVHlwZSA9ICJtdWx0aXBsZSI7DQogICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gJ2NhbmRpZGF0ZUdyb3VwcycpIHsgLy8g5oyH5a6a57uEKOaJgOWxnuinkuiJsuaOpeaUtuS7u+WKoSkNCiAgICAgICAgICAgICAgdGhpcy5jaGVja1NlbmRSb2xlID0gdHJ1ZTsNCiAgICAgICAgICAgIH0gZWxzZSB7IC8vIOS8muetvg0KICAgICAgICAgICAgICAvLyDmtYHnqIvorr7orqHmjIflrprnmoQgZWxlbWVudFZhcmlhYmxlIOS9nOS4uuS8muetvuS6uuWRmOWIl+ihqA0KICAgICAgICAgICAgICB0aGlzLm11bHRpSW5zdGFuY2VWYXJzID0gZGF0YS52YXJzOw0KICAgICAgICAgICAgICB0aGlzLmNoZWNrU2VuZFVzZXIgPSB0cnVlOw0KICAgICAgICAgICAgICB0aGlzLmNoZWNrVHlwZSA9ICJtdWx0aXBsZSI7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Yqo5oCB57uR5a6a5pON5L2c5oyJ6ZKu55qE54K55Ye75LqL5Lu2DQogICAgaGFuZGxlQnV0dG9uQ2xpY2sobWV0aG9kKSB7DQogICAgICB0aGlzW21ldGhvZF0oKTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluWOhuWPsuiKgueCueagh+mimCAqLw0KICAgIGdldEhpc3RvcnlUaXRsZShyZWNvcmQpIHsNCiAgICAgIHJldHVybiBgJHtyZWNvcmQudGFza05hbWV9IC0gJHtyZWNvcmQuYXNzaWduZWVOYW1lIHx8ICfmnKrliIbphY0nfSAtICR7cmVjb3JkLmZpbmlzaFRpbWUgfHwgJ+WkhOeQhuS4rSd9YDsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluWOhuWPsuiKgueCueWbvuaghyAqLw0KICAgIGdldEhpc3RvcnlJY29uKHJlY29yZCkgew0KICAgICAgaWYgKHJlY29yZC5maW5pc2hUaW1lKSB7DQogICAgICAgIHJldHVybiAnZWwtaWNvbi1jaGVjayc7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ2VsLWljb24tdGltZSc7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bljoblj7LoioLngrnpopzoibIgKi8NCiAgICBnZXRIaXN0b3J5Q29sb3IocmVjb3JkKSB7DQogICAgICBpZiAocmVjb3JkLmZpbmlzaFRpbWUpIHsNCiAgICAgICAgcmV0dXJuICcjNjdDMjNBJzsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAnI0U2QTIzQyc7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bnirbmgIHmoIfnrb7nsbvlnosgKi8NCiAgICBnZXRTdGF0dXNUYWdUeXBlKHJlY29yZCkgew0KICAgICAgaWYgKHJlY29yZC5maW5pc2hUaW1lKSB7DQogICAgICAgIHJldHVybiAnc3VjY2Vzcyc7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ3dhcm5pbmcnOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W54q25oCB5paH5pysICovDQogICAgZ2V0U3RhdHVzVGV4dChyZWNvcmQpIHsNCiAgICAgIGlmIChyZWNvcmQuZmluaXNoVGltZSkgew0KICAgICAgICByZXR1cm4gJ+W3suWujOaIkCc7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ+WkhOeQhuS4rSc7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlpITnkIbmtYHnqIvorrDlvZXmlbDmja4gKi8NCiAgICBwcm9jZXNzRmxvd1JlY29yZHMoKSB7DQogICAgICAvLyDpu5jorqTlsZXlvIDmnIDov5HnmoTkuIDkuKroioLngrnvvIjlpoLmnpzmnInmlbDmja7nmoTor53vvIkNCiAgICAgIGlmICh0aGlzLmZsb3dSZWNvcmRMaXN0ICYmIHRoaXMuZmxvd1JlY29yZExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAvLyDpu5jorqTlsZXlvIDnrKzkuIDkuKroioLngrkNCiAgICAgICAgdGhpcy5hY3RpdmVIaXN0b3J5TmFtZXMgPSBbJ2hpc3RvcnktMCddOw0KICAgICAgfQ0KICAgIH0NCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/task/todo/detail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">待办任务</span>\r\n        <el-tag style=\"margin-left:10px\">发起人:{{ startUser }}</el-tag>\r\n        <el-tag>任务节点:{{ taskName }}</el-tag>\r\n        <el-button style=\"float: right;\" size=\"mini\" type=\"danger\" @click=\"goBack\">关闭</el-button>\r\n      </div>\r\n      <el-tabs tab-position=\"top\" v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <!--表单信息-->\r\n        <el-tab-pane label=\"表单信息\" name=\"1\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <div v-if=\"flowRecordList && flowRecordList.length > 0\" style=\"margin-bottom: 20px;\">\r\n              <h4 style=\"margin-bottom: 15px; color: #606266;\">\r\n                <i class=\"el-icon-time\"></i> 流程历史记录\r\n              </h4>\r\n              <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\r\n                <el-collapse-item\r\n                  v-for=\"(record, index) in flowRecordList\"\r\n                  :key=\"`history-${index}-${record.taskId || record.id || index}`\"\r\n                  :name=\"`history-${index}`\"\r\n                >\r\n                  <template slot=\"title\">\r\n                    <div class=\"history-title\">\r\n                      <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\r\n                      <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\r\n                      <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\r\n                      <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\r\n                      <el-tag\r\n                        :type=\"getStatusTagType(record)\"\r\n                        size=\"mini\"\r\n                        style=\"margin-left: 10px;\"\r\n                      >\r\n                        {{ getStatusText(record) }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </template>\r\n\r\n                  <div class=\"history-content\">\r\n                    <el-descriptions :column=\"2\" size=\"small\" border>\r\n                      <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\r\n                        <span>{{ record.assigneeName }}</span>\r\n                        <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\r\n                        {{ record.candidate }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\r\n                        {{ record.createTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\r\n                        {{ record.finishTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\r\n                        {{ record.duration }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\r\n                        <div class=\"comment-content\">\r\n                          {{ record.comment.comment }}\r\n                        </div>\r\n                      </el-descriptions-item>\r\n                    </el-descriptions>\r\n                  </div>\r\n                </el-collapse-item>\r\n              </el-collapse>\r\n            </div>\r\n\r\n            <!-- 当前表单 -->\r\n            <el-card class=\"current-form-card\" shadow=\"hover\">\r\n              <div slot=\"header\" class=\"current-form-header\">\r\n                <i class=\"el-icon-edit-outline\"></i>\r\n                <span>当前待处理表单</span>\r\n              </div>\r\n              <v-form-render ref=\"vFormRef\" :key=\"formKey\"/>\r\n            </el-card>\r\n\r\n            <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;margin-top: 20px;\">\r\n              <el-button type=\"primary\" @click=\"handleComplete\">审 批</el-button>\r\n            </div>\r\n          </el-col>\r\n        </el-tab-pane>\r\n\r\n        <!--流程流转记录-->\r\n        <el-tab-pane label=\"流转记录\" name=\"2\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <flow-history :flow-record-list=\"flowRecordList\" />\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <!--流程图-->\r\n        <el-tab-pane label=\"流程图\" name=\"3\">\r\n          <bpmn-viewer :flowData=\"flowData\" :procInsId=\"taskForm.procInsId\"/>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n      <!--审批任务-->\r\n      <el-dialog :title=\"completeTitle\" :visible.sync=\"completeOpen\" width=\"60%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\">\r\n          <el-form-item prop=\"targetKey\">\r\n            <flow-user v-if=\"checkSendUser\" :checkType=\"checkType\" @handleUserSelect=\"handleUserSelect\"></flow-user>\r\n            <flow-role v-if=\"checkSendRole\" @handleRoleSelect=\"handleRoleSelect\"></flow-role>\r\n          </el-form-item>\r\n          <el-form-item label=\"处理意见\" label-width=\"80px\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入处理意见', trigger: 'blur' }]\">\r\n            <el-input type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入处理意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"completeOpen = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\r\n        </span>\r\n      </el-dialog>\r\n      <!--退回流程-->\r\n      <el-dialog :title=\"returnTitle\" :visible.sync=\"returnOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"退回节点\" prop=\"targetKey\">\r\n            <el-radio-group v-model=\"taskForm.targetKey\">\r\n              <el-radio-button\r\n                v-for=\"item in returnTaskList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.id\"\r\n              >{{ item.name }}\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"退回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"returnOpen = false\">取 消</el-button>\r\n              <el-button type=\"primary\" @click=\"taskReturn\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n      <!--驳回流程-->\r\n      <el-dialog :title=\"rejectTitle\" :visible.sync=\"rejectOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"驳回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"rejectOpen = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"taskReject\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {flowRecord} from \"@/api/flowable/finished\";\r\nimport FlowUser from '@/components/flow/User'\r\nimport FlowRole from '@/components/flow/Role'\r\nimport {flowXmlAndNode} from \"@/api/flowable/definition\";\r\nimport {\r\n  complete,\r\n  rejectTask,\r\n  returnList,\r\n  returnTask,\r\n  getNextFlowNode,\r\n  delegate,\r\n  flowTaskForm\r\n} from \"@/api/flowable/todo\";\r\nimport BpmnViewer from '@/components/Process/viewer';\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Record\",\r\n  components: {\r\n    BpmnViewer,\r\n    FlowUser,\r\n    FlowRole,\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      eventName: \"click\",\r\n      // 流程数据\r\n      flowData: {},\r\n      activeName: '1',\r\n      // 遮罩层\r\n      loading: true,\r\n      flowRecordList: [], // 流程流转数据\r\n      rules: {}, // 表单校验\r\n      taskForm: {\r\n        returnTaskShow: false, // 是否展示回退表单\r\n        delegateTaskShow: false, // 是否展示回退表单\r\n        defaultTaskShow: true, // 默认处理\r\n        comment: \"\", // 意见内容\r\n        procInsId: \"\", // 流程实例编号\r\n        instanceId: \"\", // 流程实例编号\r\n        deployId: \"\",  // 流程定义编号\r\n        taskId: \"\",// 流程任务编号\r\n        procDefId: \"\",  // 流程编号\r\n        targetKey: \"\",\r\n        variables: {},\r\n      },\r\n      returnTaskList: [],  // 回退列表数据\r\n      completeTitle: null,\r\n      completeOpen: false,\r\n      returnTitle: null,\r\n      returnOpen: false,\r\n      rejectOpen: false,\r\n      rejectTitle: null,\r\n      checkSendUser: false, // 是否展示人员选择模块\r\n      checkSendRole: false,// 是否展示角色选择模块\r\n      checkType: 'single', // 选择类型\r\n      taskName: null, // 任务节点\r\n      startUser: null, // 发起人信息,\r\n      multiInstanceVars: '', // 会签节点\r\n      formJson:{},\r\n      activeHistoryNames: [], // 展开的历史节点\r\n      completedFlowRecords: [], // 已完成的流程记录\r\n      formKey: Date.now() // VForm组件的唯一key\r\n    };\r\n  },\r\n  created() {\r\n    if (this.$route.query) {\r\n      this.taskName = this.$route.query.taskName;\r\n      this.startUser = this.$route.query.startUser;\r\n      this.taskForm.deployId = this.$route.query.deployId;\r\n      this.taskForm.taskId = this.$route.query.taskId;\r\n      this.taskForm.procInsId = this.$route.query.procInsId;\r\n      this.taskForm.executionId = this.$route.query.executionId;\r\n      this.taskForm.instanceId = this.$route.query.procInsId;\r\n      // 流程任务获取变量信息\r\n      if (this.taskForm.taskId) {\r\n        this.getFlowTaskForm(this.taskForm.taskId);\r\n      }\r\n      this.getFlowRecordList(this.taskForm.procInsId, this.taskForm.deployId);\r\n    }\r\n  },\r\n  methods: {\r\n    handleClick(tab) {\r\n      if (tab.name === '3') {\r\n        flowXmlAndNode({procInsId: this.taskForm.procInsId, deployId: this.taskForm.deployId}).then(res => {\r\n          this.flowData = res.data;\r\n        })\r\n      }\r\n    },\r\n    // 用户信息选中数据\r\n    handleUserSelect(selection) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.userId.toString());\r\n          if (this.multiInstanceVars) {\r\n            this.$set(this.taskForm.variables, this.multiInstanceVars,  selectVal);\r\n          } else {\r\n            this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n          }\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection.userId.toString());\r\n        }\r\n      }\r\n    },\r\n    // 角色信息选中数据\r\n    handleRoleSelect(selection) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.roleId.toString());\r\n          this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection);\r\n        }\r\n      }\r\n    },\r\n    /** 流程流转记录 */\r\n    getFlowRecordList(procInsId, deployId) {\r\n      const that = this\r\n      const params = {procInsId: procInsId, deployId: deployId}\r\n      flowRecord(params).then(res => {\r\n        that.flowRecordList = res.data.flowList;\r\n        // 处理历史节点数据\r\n        that.processFlowRecords();\r\n      }).catch(() => {\r\n        this.goBack();\r\n      })\r\n    },\r\n    /** 流程节点表单 */\r\n    getFlowTaskForm(taskId) {\r\n      if (taskId) {\r\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\r\n        flowTaskForm({taskId: taskId}).then(res => {\r\n          // 更新formKey以强制重新渲染VForm组件，避免key冲突\r\n          this.formKey = Date.now();\r\n\r\n          this.$nextTick(() => {\r\n            // 回显表单\r\n            this.$refs.vFormRef.setFormJson(res.data.formJson);\r\n            this.formJson = res.data.formJson;\r\n            this.$nextTick(() => {\r\n              // 加载表单填写的数据\r\n              this.$refs.vFormRef.setFormData(res.data);\r\n              // this.$nextTick(() => {\r\n              //   // 表单禁用\r\n              //   this.$refs.vFormRef.disableForm();\r\n              // })\r\n            })\r\n          })\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 委派任务 */\r\n    handleDelegate() {\r\n      this.taskForm.delegateTaskShow = true;\r\n      this.taskForm.defaultTaskShow = false;\r\n    },\r\n    handleAssign() {\r\n\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      const obj = { path: \"/task/todo\", query: { t: Date.now()} };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 驳回任务 */\r\n    handleReject() {\r\n      this.rejectOpen = true;\r\n      this.rejectTitle = \"驳回流程\";\r\n    },\r\n    /** 驳回任务 */\r\n    taskReject() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          rejectTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 可退回任务列表 */\r\n    handleReturn() {\r\n      this.returnOpen = true;\r\n      this.returnTitle = \"退回流程\";\r\n      returnList(this.taskForm).then(res => {\r\n        this.returnTaskList = res.data;\r\n      })\r\n    },\r\n    /** 提交退回任务 */\r\n    taskReturn() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          returnTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack()\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelTask() {\r\n      this.taskForm.returnTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 委派任务 */\r\n    submitDeleteTask() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          delegate(this.taskForm).then(response => {\r\n            this.$modal.msgSuccess(response.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelDelegateTask() {\r\n      this.taskForm.delegateTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 加载审批任务弹框 */\r\n    handleComplete() {\r\n      this.completeOpen = true;\r\n      this.completeTitle = \"流程审批\";\r\n      this.submitForm();\r\n    },\r\n    /** 用户审批任务 */\r\n    taskComplete() {\r\n      if (!this.taskForm.variables && this.checkSendUser) {\r\n        this.$modal.msgError(\"请选择流程接收人员!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.variables && this.checkSendRole) {\r\n        this.$modal.msgError(\"请选择流程接收角色组!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.comment) {\r\n        this.$modal.msgError(\"请输入审批意见!\");\r\n        return;\r\n      }\r\n      if (this.taskForm) {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      } else {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      }\r\n    },\r\n    /** 申请流程表单数据提交 */\r\n    submitForm() {\r\n      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况\r\n      const params = {taskId: this.taskForm.taskId}\r\n      getNextFlowNode(params).then(res => {\r\n        this.$refs.vFormRef.getFormData().then(formData => {\r\n          Object.assign(this.taskForm.variables, formData);\r\n          this.taskForm.variables.formJson = this.formJson;\r\n          console.log(this.taskForm, \"流程审批提交表单数据1\")\r\n        }).catch(() => {\r\n          // this.$modal.msgError(error)\r\n        })\r\n        const data = res.data;\r\n        if (data) {\r\n          if (data.dataType === 'dynamic') {\r\n            if (data.type === 'assignee') { // 指定人员\r\n              this.checkSendUser = true;\r\n              this.checkType = \"single\";\r\n            } else if (data.type === 'candidateUsers') {  // 候选人员(多个)\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            } else if (data.type === 'candidateGroups') { // 指定组(所属角色接收任务)\r\n              this.checkSendRole = true;\r\n            } else { // 会签\r\n              // 流程设计指定的 elementVariable 作为会签人员列表\r\n              this.multiInstanceVars = data.vars;\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 动态绑定操作按钮的点击事件\r\n    handleButtonClick(method) {\r\n      this[method]();\r\n    },\r\n\r\n    /** 获取历史节点标题 */\r\n    getHistoryTitle(record) {\r\n      return `${record.taskName} - ${record.assigneeName || '未分配'} - ${record.finishTime || '处理中'}`;\r\n    },\r\n\r\n    /** 获取历史节点图标 */\r\n    getHistoryIcon(record) {\r\n      if (record.finishTime) {\r\n        return 'el-icon-check';\r\n      } else {\r\n        return 'el-icon-time';\r\n      }\r\n    },\r\n\r\n    /** 获取历史节点颜色 */\r\n    getHistoryColor(record) {\r\n      if (record.finishTime) {\r\n        return '#67C23A';\r\n      } else {\r\n        return '#E6A23C';\r\n      }\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(record) {\r\n      if (record.finishTime) {\r\n        return 'success';\r\n      } else {\r\n        return 'warning';\r\n      }\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(record) {\r\n      if (record.finishTime) {\r\n        return '已完成';\r\n      } else {\r\n        return '处理中';\r\n      }\r\n    },\r\n\r\n    /** 处理流程记录数据 */\r\n    processFlowRecords() {\r\n      // 默认展开最近的一个节点（如果有数据的话）\r\n      if (this.flowRecordList && this.flowRecordList.length > 0) {\r\n        // 默认展开第一个节点\r\n        this.activeHistoryNames = ['history-0'];\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.test-form {\r\n  margin: 15px auto;\r\n  width: 800px;\r\n  padding: 15px;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both\r\n}\r\n\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.my-label {\r\n  background: #E1F3D8;\r\n}\r\n\r\n/* 历史节点样式 */\r\n.history-collapse {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-collapse .el-collapse-item__header {\r\n  background-color: #F5F7FA;\r\n  border-bottom: 1px solid #EBEEF5;\r\n  padding: 0 20px;\r\n  height: 48px;\r\n  line-height: 48px;\r\n}\r\n\r\n.history-collapse .el-collapse-item__content {\r\n  padding: 20px;\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.history-title {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n}\r\n\r\n.history-title .node-name {\r\n  font-weight: 600;\r\n  margin-left: 8px;\r\n  margin-right: 15px;\r\n  color: #303133;\r\n}\r\n\r\n.history-title .assignee-name {\r\n  color: #606266;\r\n  margin-right: 15px;\r\n}\r\n\r\n.history-title .finish-time {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-left: auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.history-content {\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n}\r\n\r\n.comment-content {\r\n  background-color: #F8F9FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  font-style: italic;\r\n  color: #606266;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 16px;\r\n}\r\n\r\n.current-form-card {\r\n  border: 2px solid #409EFF;\r\n}\r\n\r\n.current-form-header {\r\n  color: #409EFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.current-form-header i {\r\n  margin-right: 8px;\r\n}\r\n</style>\r\n"]}]}