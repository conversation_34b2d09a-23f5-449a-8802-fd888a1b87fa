{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=style&index=0&id=e836feee&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752412939715}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZmxvdy1oaXN0b3J5LWNvbnRhaW5lciB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmhpc3RvcnktdGl0bGUgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgY29sb3I6ICM2MDYyNjY7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7Cn0KCi8qIOWOhuWPsuiKgueCueagt+W8jyAqLwouaGlzdG9yeS1jb2xsYXBzZSB7CiAgYm9yZGVyOiAxcHggc29saWQgI0VCRUVGNTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLmhpc3RvcnktY29sbGFwc2UgLmVsLWNvbGxhcHNlLWl0ZW1fX2hlYWRlciB7CiAgYmFja2dyb3VuZC1jb2xvcjogI0Y1RjdGQTsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI0VCRUVGNTsKICBwYWRkaW5nOiAwIDIwcHg7CiAgaGVpZ2h0OiA0OHB4OwogIGxpbmUtaGVpZ2h0OiA0OHB4Owp9CgouaGlzdG9yeS1jb2xsYXBzZSAuZWwtY29sbGFwc2UtaXRlbV9fY29udGVudCB7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjRkFGQUZBOwp9CgouaGlzdG9yeS10aXRsZS1jb250ZW50IHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgd2lkdGg6IDEwMCU7CiAgZm9udC1zaXplOiAxNHB4Owp9CgouaGlzdG9yeS10aXRsZS1jb250ZW50IC5ub2RlLW5hbWUgewogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgbWFyZ2luLWxlZnQ6IDhweDsKICBtYXJnaW4tcmlnaHQ6IDE1cHg7CiAgY29sb3I6ICMzMDMxMzM7Cn0KCi5oaXN0b3J5LXRpdGxlLWNvbnRlbnQgLmFzc2lnbmVlLW5hbWUgewogIGNvbG9yOiAjNjA2MjY2OwogIG1hcmdpbi1yaWdodDogMTVweDsKfQoKLmhpc3RvcnktdGl0bGUtY29udGVudCAuZmluaXNoLXRpbWUgewogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtc2l6ZTogMTJweDsKICBtYXJnaW4tbGVmdDogYXV0bzsKICBtYXJnaW4tcmlnaHQ6IDEwcHg7Cn0KCi5zdGF0dXMtdGFnIHsKICBtYXJnaW4tbGVmdDogMTBweDsKfQoKLmhpc3RvcnktY29udGVudCB7CiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIHBhZGRpbmc6IDE2cHg7Cn0KCi5jb21tZW50LWNvbnRlbnQgewogIGJhY2tncm91bmQtY29sb3I6ICNGOEY5RkE7CiAgcGFkZGluZzogMTJweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjNDA5RUZGOwogIGZvbnQtc3R5bGU6IGl0YWxpYzsKICBjb2xvcjogIzYwNjI2NjsKfQoKLyog6IqC54K56KGo5Y2V5qC35byPICovCi5ub2RlLWZvcm0tc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBib3JkZXI6IDFweCBzb2xpZCAjRTRFN0VEOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgouZm9ybS1zZWN0aW9uLXRpdGxlIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjVGN0ZBOwogIHBhZGRpbmc6IDEycHggMTZweDsKICBtYXJnaW46IDA7CiAgZm9udC1zaXplOiAxNHB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICM2MDYyNjY7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNFNEU3RUQ7Cn0KCi5mb3JtLXNlY3Rpb24tdGl0bGUgaSB7CiAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgY29sb3I6ICM0MDlFRkY7Cn0KCi8qIOihqOWNleaYvuekuuWuueWZqOagt+W8jyAqLwouZm9ybS1kaXNwbGF5LWNvbnRhaW5lciB7CiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7Cn0KCi8qIOiKgueCueihqOWNleinhuWbvuagt+W8jyAqLwoubm9kZS1mb3JtLXZpZXcgewogIHBhZGRpbmc6IDE2cHg7Cn0KCi5mb3JtLWZpZWxkcyB7CiAgLmZvcm0tZmllbGQtaXRlbSB7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0OwoKICAgICY6bGFzdC1jaGlsZCB7CiAgICAgIG1hcmdpbi1ib3R0b206IDA7CiAgICB9CiAgfQoKICAuZmllbGQtbGFiZWwgewogICAgd2lkdGg6IDEyMHB4OwogICAgbWluLXdpZHRoOiAxMjBweDsKICAgIHBhZGRpbmc6IDhweCAxMnB4IDhweCAwOwogICAgY29sb3I6ICM2MDYyNjY7CiAgICBmb250LXdlaWdodDogNTAwOwogICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgICBsaW5lLWhlaWdodDogMzJweDsKICB9CgogIC5maWVsZC1jb250ZW50IHsKICAgIGZsZXg6IDE7CiAgICBwYWRkaW5nOiA0cHggMDsKICB9Cn0KCi5uby1maWVsZHMgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiA0MHB4IDIwcHg7CiAgY29sb3I6ICM5MDkzOTk7CiAgZm9udC1zdHlsZTogaXRhbGljOwp9CgovKiDoioLngrnmlbDmja7op4blm77moLflvI8gKi8KLm5vZGUtZGF0YS12aWV3IHsKICBwYWRkaW5nOiAxNnB4OwogIGJhY2tncm91bmQtY29sb3I6ICNGQUZBRkE7Cn0KCi8qIOmAmueUqOaVsOaNruWuueWZqOagt+W8jyAqLwouZm9ybS1kYXRhLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMTZweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjRkFGQUZBOwp9CgouZm9ybS1kYXRhLWRlc2NyaXB0aW9ucyB7CiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7Cn0KCi8qIOihqOWNleWtl+auteWAvOagt+W8jyAqLwouZm9ybS1maWVsZC12YWx1ZSB7CiAgd29yZC1icmVhazogYnJlYWstd29yZDsKICBsaW5lLWhlaWdodDogMS41Owp9CgouZmllbGQtYm9vbGVhbi10cnVlIHsKICBjb2xvcjogIzY3QzIzQTsKICBmb250LXdlaWdodDogNjAwOwp9CgouZmllbGQtYm9vbGVhbi1mYWxzZSB7CiAgY29sb3I6ICNGNTZDNkM7CiAgZm9udC13ZWlnaHQ6IDYwMDsKfQoKLmZpZWxkLW51bWJlciB7CiAgY29sb3I6ICNFNkEyM0M7CiAgZm9udC13ZWlnaHQ6IDUwMDsKfQoKLmZpZWxkLWFycmF5IHsKICBjb2xvcjogIzkwOTM5OTsKICBmb250LXN0eWxlOiBpdGFsaWM7Cn0KCi5maWVsZC10ZXh0IHsKICBjb2xvcjogIzYwNjI2NjsKfQoKLyog5Y6f5aeL5pWw5o2u5pi+56S65qC35byPICovCi5yYXctZGF0YS1jb250YWluZXIgewogIGJhY2tncm91bmQtY29sb3I6ICNmOGY4Zjg7CiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgcGFkZGluZzogMTJweDsKICBtYXgtaGVpZ2h0OiA0MDBweDsKICBvdmVyZmxvdy15OiBhdXRvOwp9CgoucmF3LWRhdGEtY29udGFpbmVyIHByZSB7CiAgbWFyZ2luOiAwOwogIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7CiAgZm9udC1zaXplOiAxMnB4OwogIGxpbmUtaGVpZ2h0OiAxLjQ7CiAgY29sb3I6ICMzMzM7CiAgd2hpdGUtc3BhY2U6IHByZS13cmFwOwogIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKfQoKLm5vLWRhdGEgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjb2xvcjogIzkwOTM5OTsKICBwYWRkaW5nOiAyMHB4OwogIGZvbnQtc3R5bGU6IGl0YWxpYzsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsaA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FlowHistory", "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <!-- 节点表单数据显示 -->\n          <div v-if=\"record.taskId && nodeFormData[record.taskId]\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> {{ record.taskName }} - 数据记录\n            </h5>\n            <div class=\"form-data-container\">\n              <el-descriptions :column=\"2\" size=\"small\" border class=\"form-data-descriptions\">\n                <el-descriptions-item\n                  v-for=\"(value, key) in getFormDataDisplay(record.taskId)\"\n                  :key=\"key\"\n                  :label=\"key\"\n                  :span=\"getFieldSpan(value)\"\n                >\n                  <div class=\"form-field-value\" :class=\"getFieldClass(value)\">\n                    {{ formatFieldValue(value) }}\n                  </div>\n                </el-descriptions-item>\n              </el-descriptions>\n              <div v-if=\"Object.keys(getFormDataDisplay(record.taskId)).length === 0\" class=\"no-data\">\n                暂无表单数据\n              </div>\n            </div>\n          </div>\n\n          <!-- 办理信息 -->\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nimport { getProcessVariables } from '@/api/flowable/definition'\nimport FieldRenderer from '@/components/FieldRenderer'\n\nexport default {\n  name: 'FlowHistory',\n  components: {\n    FieldRenderer\n  },\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: [],\n      nodeFormData: {}, // 存储每个节点的表单数据\n      loadingNodes: new Set() // 正在加载的节点\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0) {\n          if (this.defaultExpanded) {\n            // 默认展开第一个节点\n            this.activeHistoryNames = ['history-0'];\n          }\n          // 加载所有节点的表单数据\n          this.loadAllNodeForms();\n        }\n      },\n      immediate: true\n    },\n    activeHistoryNames: {\n      handler(newVal) {\n        // 当展开节点时，加载对应的表单数据\n        newVal.forEach(name => {\n          const index = parseInt(name.replace('history-', ''));\n          const record = this.flowRecordList[index];\n          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n            this.loadNodeForm(record.taskId);\n          }\n        });\n      }\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    },\n\n    /** 加载所有节点的表单数据 */\n    loadAllNodeForms() {\n      this.flowRecordList.forEach(record => {\n        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n          this.loadNodeForm(record.taskId);\n        }\n      });\n    },\n\n    /** 加载单个节点的表单数据 */\n    loadNodeForm(taskId) {\n      if (!taskId || this.loadingNodes.has(taskId)) {\n        return;\n      }\n\n      this.loadingNodes.add(taskId);\n\n      getProcessVariables(taskId).then(res => {\n        if (res.data) {\n          // 设置表单数据\n          this.$set(this.nodeFormData, taskId, res.data);\n        }\n      }).catch(error => {\n        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);\n      }).finally(() => {\n        this.loadingNodes.delete(taskId);\n      });\n    },\n\n    /** 获取表单数据用于显示 */\n    getFormDataDisplay(taskId) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return {};\n\n      console.log('节点数据:', taskId, nodeData); // 调试信息\n\n      // 系统字段列表\n      const systemFields = ['formJson', 'taskId', 'procInsId', 'deployId', 'procDefId', 'instanceId'];\n\n      const filteredData = {};\n      Object.keys(nodeData).forEach(key => {\n        const value = nodeData[key];\n\n        // 跳过系统字段\n        if (systemFields.includes(key)) {\n          return;\n        }\n\n        // 包含所有非空值，包括数字0、false等有意义的值\n        if (value !== null && value !== undefined && value !== '') {\n          const label = this.getFieldLabel(key, nodeData.formJson);\n          filteredData[label] = value;\n        }\n      });\n\n      console.log('过滤后的数据:', filteredData); // 调试信息\n      return filteredData;\n    },\n\n    /** 获取字段标签 */\n    getFieldLabel(fieldKey, formJson) {\n      if (!formJson) {\n        return fieldKey;\n      }\n\n      // 尝试多种可能的表单结构\n      let widgets = [];\n\n      if (formJson.widgetList) {\n        widgets = formJson.widgetList;\n      } else if (formJson.formConfig && formJson.formConfig.widgetList) {\n        widgets = formJson.formConfig.widgetList;\n      } else if (Array.isArray(formJson)) {\n        widgets = formJson;\n      }\n\n      console.log('查找字段标签:', fieldKey, '在widgets:', widgets); // 调试信息\n\n      // 在表单组件中查找字段标签\n      const widget = widgets.find(w => {\n        if (w.options && w.options.name === fieldKey) return true;\n        if (w.__config__ && w.__config__.formId === fieldKey) return true;\n        if (w.__vModel__ === fieldKey) return true;\n        return false;\n      });\n\n      if (widget) {\n        // 尝试多种可能的标签字段\n        const label = widget.options?.label ||\n                     widget.__config__?.label ||\n                     widget.label ||\n                     fieldKey;\n        console.log('找到标签:', fieldKey, '->', label); // 调试信息\n        return label;\n      }\n\n      return fieldKey;\n    },\n\n    /** 格式化字段值 */\n    formatFieldValue(value) {\n      if (Array.isArray(value)) {\n        return value.join(', ');\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      if (typeof value === 'boolean') {\n        return value ? '是' : '否';\n      }\n      return String(value);\n    },\n\n    /** 获取字段跨度 */\n    getFieldSpan(value) {\n      const valueStr = this.formatFieldValue(value);\n      // 长文本占用两列\n      return valueStr.length > 20 ? 2 : 1;\n    },\n\n    /** 获取字段样式类 */\n    getFieldClass(value) {\n      if (typeof value === 'boolean') {\n        return value ? 'field-boolean-true' : 'field-boolean-false';\n      }\n      if (typeof value === 'number') {\n        return 'field-number';\n      }\n      if (Array.isArray(value)) {\n        return 'field-array';\n      }\n      return 'field-text';\n    },\n\n    /** 获取节点VForm配置 */\n    getNodeVFormConfig(record) {\n      // 从本地存储加载VForm配置\n      if (this.nodeVFormConfigs.length === 0) {\n        this.loadNodeVFormConfigs();\n      }\n\n      // 根据节点标识或任务名称查找对应的VForm配置\n      const config = this.nodeVFormConfigs.find(config =>\n        config.nodeKey === record.taskDefKey ||\n        config.nodeName === record.taskName ||\n        this.matchNodeByType(config, record)\n      );\n\n      return config;\n    },\n\n    /** 根据类型匹配节点 */\n    matchNodeByType(config, record) {\n      const taskName = record.taskName || '';\n      const nodeTypeMap = {\n        'npi_apply': ['申请', 'NPI申请'],\n        'tech_review': ['技术评审', '技术审核'],\n        'process_review': ['工艺评审', '工艺审核'],\n        'quality_review': ['质量评审', '质量审核'],\n        'cost_review': ['成本评审', '成本审核'],\n        'final_approval': ['最终审批', '终审']\n      };\n\n      const keywords = nodeTypeMap[config.nodeType] || [];\n      return keywords.some(keyword => taskName.includes(keyword));\n    },\n\n    /** 加载节点VForm配置 */\n    loadNodeVFormConfigs() {\n      try {\n        // 加载NPI流程的VForm配置\n        const saved = localStorage.getItem('node_vform_config_npi_process');\n        if (saved) {\n          const config = JSON.parse(saved);\n          this.nodeVFormConfigs = config.nodeForms || [];\n        }\n      } catch (error) {\n        console.warn('加载节点VForm配置失败:', error);\n        this.nodeVFormConfigs = [];\n      }\n    },\n\n    /** 切换节点表单显示模式 */\n    toggleNodeFormMode(taskId) {\n      const currentMode = this.nodeFormModes[taskId] || 'form';\n      this.$set(this.nodeFormModes, taskId, currentMode === 'form' ? 'data' : 'form');\n    },\n\n    /** 获取节点表单字段 */\n    getNodeFormFields(record) {\n      const vformConfig = this.getNodeVFormConfig(record);\n      if (!vformConfig || !vformConfig.formJson || !vformConfig.formJson.widgetList) {\n        return [];\n      }\n\n      return vformConfig.formJson.widgetList.map(widget => ({\n        name: widget.options?.name || widget.name,\n        label: widget.options?.label || widget.label || widget.options?.name,\n        type: widget.type,\n        options: widget.options\n      }));\n    },\n\n    /** 获取字段值 */\n    getFieldValue(taskId, fieldName) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return null;\n      return nodeData[fieldName];\n    },\n\n    /** 获取字段组件 */\n    getFieldComponent() {\n      return 'FieldRenderer';\n    },\n\n    /** 获取节点相关数据 */\n    getNodeRelatedData(record) {\n      const nodeData = this.nodeFormData[record.taskId];\n      if (!nodeData) return {};\n\n      const vformConfig = this.getNodeVFormConfig(record);\n      if (!vformConfig || !vformConfig.formJson || !vformConfig.formJson.widgetList) {\n        // 如果没有表单配置，返回所有数据\n        return this.getFormDataDisplay(record.taskId);\n      }\n\n      // 只返回当前节点表单中定义的字段数据\n      const nodeFields = vformConfig.formJson.widgetList.map(w => w.options?.name || w.name);\n      const filteredData = {};\n\n      nodeFields.forEach(fieldName => {\n        if (nodeData.hasOwnProperty(fieldName) &&\n            nodeData[fieldName] !== null &&\n            nodeData[fieldName] !== undefined &&\n            nodeData[fieldName] !== '') {\n          const widget = vformConfig.formJson.widgetList.find(w =>\n            (w.options?.name || w.name) === fieldName\n          );\n          const label = widget?.options?.label || widget?.label || fieldName;\n          filteredData[label] = nodeData[fieldName];\n        }\n      });\n\n      return filteredData;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n\n/* 节点表单样式 */\n.node-form-section {\n  margin-bottom: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-section-title {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #606266;\n  border-bottom: 1px solid #E4E7ED;\n}\n\n.form-section-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n/* 表单显示容器样式 */\n.form-display-container {\n  background-color: white;\n}\n\n/* 节点表单视图样式 */\n.node-form-view {\n  padding: 16px;\n}\n\n.form-fields {\n  .form-field-item {\n    margin-bottom: 20px;\n    display: flex;\n    align-items: flex-start;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .field-label {\n    width: 120px;\n    min-width: 120px;\n    padding: 8px 12px 8px 0;\n    color: #606266;\n    font-weight: 500;\n    text-align: right;\n    line-height: 32px;\n  }\n\n  .field-content {\n    flex: 1;\n    padding: 4px 0;\n  }\n}\n\n.no-fields {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n  font-style: italic;\n}\n\n/* 节点数据视图样式 */\n.node-data-view {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n/* 通用数据容器样式 */\n.form-data-container {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n.form-data-descriptions {\n  background-color: white;\n}\n\n/* 表单字段值样式 */\n.form-field-value {\n  word-break: break-word;\n  line-height: 1.5;\n}\n\n.field-boolean-true {\n  color: #67C23A;\n  font-weight: 600;\n}\n\n.field-boolean-false {\n  color: #F56C6C;\n  font-weight: 600;\n}\n\n.field-number {\n  color: #E6A23C;\n  font-weight: 500;\n}\n\n.field-array {\n  color: #909399;\n  font-style: italic;\n}\n\n.field-text {\n  color: #606266;\n}\n\n/* 原始数据显示样式 */\n.raw-data-container {\n  background-color: #f8f8f8;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 12px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.raw-data-container pre {\n  margin: 0;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  color: #333;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.no-data {\n  text-align: center;\n  color: #909399;\n  padding: 20px;\n  font-style: italic;\n}\n</style>\n"]}]}