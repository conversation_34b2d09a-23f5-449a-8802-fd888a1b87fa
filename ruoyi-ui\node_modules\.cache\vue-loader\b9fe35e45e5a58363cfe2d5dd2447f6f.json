{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=style&index=0&id=e836feee&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752406353550}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5mbG93LWhpc3RvcnktY29udGFpbmVyIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9CgouaGlzdG9yeS10aXRsZSB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKICBjb2xvcjogIzYwNjI2NjsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IDYwMDsKfQoKLyog5Y6G5Y+y6IqC54K55qC35byPICovCi5oaXN0b3J5LWNvbGxhcHNlIHsKICBib3JkZXI6IDFweCBzb2xpZCAjRUJFRUY1OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgouaGlzdG9yeS1jb2xsYXBzZSAuZWwtY29sbGFwc2UtaXRlbV9faGVhZGVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjVGN0ZBOwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjRUJFRUY1OwogIHBhZGRpbmc6IDAgMjBweDsKICBoZWlnaHQ6IDQ4cHg7CiAgbGluZS1oZWlnaHQ6IDQ4cHg7Cn0KCi5oaXN0b3J5LWNvbGxhcHNlIC5lbC1jb2xsYXBzZS1pdGVtX19jb250ZW50IHsKICBwYWRkaW5nOiAyMHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNGQUZBRkE7Cn0KCi5oaXN0b3J5LXRpdGxlLWNvbnRlbnQgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICB3aWR0aDogMTAwJTsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5oaXN0b3J5LXRpdGxlLWNvbnRlbnQgLm5vZGUtbmFtZSB7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBtYXJnaW4tbGVmdDogOHB4OwogIG1hcmdpbi1yaWdodDogMTVweDsKICBjb2xvcjogIzMwMzEzMzsKfQoKLmhpc3RvcnktdGl0bGUtY29udGVudCAuYXNzaWduZWUtbmFtZSB7CiAgY29sb3I6ICM2MDYyNjY7CiAgbWFyZ2luLXJpZ2h0OiAxNXB4Owp9CgouaGlzdG9yeS10aXRsZS1jb250ZW50IC5maW5pc2gtdGltZSB7CiAgY29sb3I6ICM5MDkzOTk7CiAgZm9udC1zaXplOiAxMnB4OwogIG1hcmdpbi1sZWZ0OiBhdXRvOwogIG1hcmdpbi1yaWdodDogMTBweDsKfQoKLnN0YXR1cy10YWcgewogIG1hcmdpbi1sZWZ0OiAxMHB4Owp9CgouaGlzdG9yeS1jb250ZW50IHsKICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgcGFkZGluZzogMTZweDsKfQoKLmNvbW1lbnQtY29udGVudCB7CiAgYmFja2dyb3VuZC1jb2xvcjogI0Y4RjlGQTsKICBwYWRkaW5nOiAxMnB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM0MDlFRkY7CiAgZm9udC1zdHlsZTogaXRhbGljOwogIGNvbG9yOiAjNjA2MjY2Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FlowHistory", "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlowHistory',\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: []\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0 && this.defaultExpanded) {\n          // 默认展开第一个节点\n          this.activeHistoryNames = ['history-0'];\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n</style>\n"]}]}