<template>
  <div class="node-form-container">
    <div class="form-header">
      <h4>
        <i class="el-icon-edit-outline"></i>
        {{ title || '节点表单' }}
      </h4>
      <el-button 
        v-if="!readonly" 
        type="primary" 
        size="small" 
        @click="addField"
      >
        添加字段
      </el-button>
    </div>

    <div class="form-content">
      <!-- 表单字段列表 -->
      <div v-if="formFields.length > 0" class="field-list">
        <div 
          v-for="(field, index) in formFields" 
          :key="field.id || index"
          class="field-item"
        >
          <div class="field-header">
            <span class="field-label">{{ field.label }}</span>
            <span class="field-type">{{ getFieldTypeText(field.type) }}</span>
            <el-button 
              v-if="!readonly" 
              type="text" 
              size="mini" 
              @click="removeField(index)"
              class="remove-btn"
            >
              删除
            </el-button>
          </div>
          
          <div class="field-content">
            <!-- 文本输入 -->
            <el-input 
              v-if="field.type === 'text'" 
              v-model="field.value"
              :placeholder="field.placeholder"
              :readonly="readonly"
            />
            
            <!-- 多行文本 -->
            <el-input 
              v-else-if="field.type === 'textarea'" 
              v-model="field.value"
              type="textarea"
              :rows="3"
              :placeholder="field.placeholder"
              :readonly="readonly"
            />
            
            <!-- 数字输入 -->
            <el-input-number 
              v-else-if="field.type === 'number'" 
              v-model="field.value"
              :placeholder="field.placeholder"
              :readonly="readonly"
              style="width: 100%"
            />
            
            <!-- 选择器 -->
            <el-select 
              v-else-if="field.type === 'select'" 
              v-model="field.value"
              :placeholder="field.placeholder"
              :disabled="readonly"
              style="width: 100%"
            >
              <el-option 
                v-for="option in field.options" 
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            
            <!-- 日期选择 -->
            <el-date-picker 
              v-else-if="field.type === 'date'" 
              v-model="field.value"
              type="date"
              :placeholder="field.placeholder"
              :readonly="readonly"
              style="width: 100%"
            />
            
            <!-- 开关 -->
            <el-switch 
              v-else-if="field.type === 'switch'" 
              v-model="field.value"
              :disabled="readonly"
            />
            
            <!-- 单选框组 -->
            <el-radio-group 
              v-else-if="field.type === 'radio'" 
              v-model="field.value"
              :disabled="readonly"
            >
              <el-radio 
                v-for="option in field.options" 
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
            
            <!-- 复选框组 -->
            <el-checkbox-group 
              v-else-if="field.type === 'checkbox'" 
              v-model="field.value"
              :disabled="readonly"
            >
              <el-checkbox 
                v-for="option in field.options" 
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <i class="el-icon-document-add"></i>
        <p>{{ readonly ? '暂无表单数据' : '点击"添加字段"开始创建表单' }}</p>
      </div>
    </div>

    <!-- 字段配置对话框 -->
    <el-dialog 
      title="添加表单字段" 
      :visible.sync="showFieldDialog"
      width="500px"
    >
      <el-form :model="newField" label-width="80px">
        <el-form-item label="字段标签">
          <el-input v-model="newField.label" placeholder="请输入字段标签" />
        </el-form-item>
        
        <el-form-item label="字段类型">
          <el-select v-model="newField.type" style="width: 100%">
            <el-option label="单行文本" value="text" />
            <el-option label="多行文本" value="textarea" />
            <el-option label="数字" value="number" />
            <el-option label="下拉选择" value="select" />
            <el-option label="日期" value="date" />
            <el-option label="开关" value="switch" />
            <el-option label="单选框" value="radio" />
            <el-option label="复选框" value="checkbox" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="占位符">
          <el-input v-model="newField.placeholder" placeholder="请输入占位符文本" />
        </el-form-item>
        
        <el-form-item 
          v-if="['select', 'radio', 'checkbox'].includes(newField.type)" 
          label="选项配置"
        >
          <div class="options-config">
            <div 
              v-for="(option, index) in newField.options" 
              :key="index"
              class="option-item"
            >
              <el-input 
                v-model="option.label" 
                placeholder="选项标签" 
                style="width: 45%; margin-right: 10px;"
              />
              <el-input 
                v-model="option.value" 
                placeholder="选项值" 
                style="width: 35%; margin-right: 10px;"
              />
              <el-button 
                type="text" 
                @click="removeOption(index)"
                style="color: #f56c6c;"
              >
                删除
              </el-button>
            </div>
            <el-button type="text" @click="addOption">+ 添加选项</el-button>
          </div>
        </el-form-item>
      </el-form>
      
      <div slot="footer">
        <el-button @click="showFieldDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAddField">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'NodeForm',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formFields: [],
      showFieldDialog: false,
      newField: {
        label: '',
        type: 'text',
        placeholder: '',
        value: '',
        options: []
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.formFields = newVal || [];
      },
      immediate: true,
      deep: true
    },
    formFields: {
      handler(newVal) {
        this.$emit('input', newVal);
      },
      deep: true
    }
  },
  methods: {
    /** 添加字段 */
    addField() {
      this.newField = {
        label: '',
        type: 'text',
        placeholder: '',
        value: '',
        options: []
      };
      this.showFieldDialog = true;
    },

    /** 确认添加字段 */
    confirmAddField() {
      if (!this.newField.label) {
        this.$message.warning('请输入字段标签');
        return;
      }

      const field = {
        id: Date.now(),
        label: this.newField.label,
        type: this.newField.type,
        placeholder: this.newField.placeholder,
        value: this.getDefaultValue(this.newField.type),
        options: [...this.newField.options]
      };

      this.formFields.push(field);
      this.showFieldDialog = false;
    },

    /** 移除字段 */
    removeField(index) {
      this.formFields.splice(index, 1);
    },

    /** 添加选项 */
    addOption() {
      this.newField.options.push({ label: '', value: '' });
    },

    /** 移除选项 */
    removeOption(index) {
      this.newField.options.splice(index, 1);
    },

    /** 获取字段类型文本 */
    getFieldTypeText(type) {
      const typeMap = {
        text: '文本',
        textarea: '多行文本',
        number: '数字',
        select: '下拉选择',
        date: '日期',
        switch: '开关',
        radio: '单选',
        checkbox: '多选'
      };
      return typeMap[type] || type;
    },

    /** 获取默认值 */
    getDefaultValue(type) {
      switch (type) {
        case 'number':
          return 0;
        case 'switch':
          return false;
        case 'checkbox':
          return [];
        default:
          return '';
      }
    },

    /** 获取表单数据 */
    getFormData() {
      const data = {};
      this.formFields.forEach(field => {
        data[field.label] = field.value;
      });
      return data;
    },

    /** 设置表单数据 */
    setFormData(data) {
      this.formFields.forEach(field => {
        if (data.hasOwnProperty(field.label)) {
          field.value = data[field.label];
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.node-form-container {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
}

.form-header {
  background-color: #F5F7FA;
  padding: 12px 16px;
  border-bottom: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h4 {
    margin: 0;
    color: #606266;
    font-size: 14px;
    font-weight: 600;

    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
}

.form-content {
  padding: 16px;
  background-color: white;
}

.field-list {
  .field-item {
    margin-bottom: 16px;
    border: 1px solid #E4E7ED;
    border-radius: 4px;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .field-header {
    background-color: #FAFAFA;
    padding: 8px 12px;
    border-bottom: 1px solid #E4E7ED;
    display: flex;
    align-items: center;

    .field-label {
      font-weight: 600;
      color: #303133;
      flex: 1;
    }

    .field-type {
      color: #909399;
      font-size: 12px;
      margin-right: 10px;
    }

    .remove-btn {
      color: #F56C6C;
    }
  }

  .field-content {
    padding: 12px;
  }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.options-config {
  .option-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
}
</style>
