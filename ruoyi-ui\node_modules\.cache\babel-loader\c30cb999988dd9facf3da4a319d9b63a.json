{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormProperties.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormProperties.vue", "mtime": 1752386494977}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "require", "name", "props", "item", "type", "Object", "required", "data", "activeTab", "properties", "computed", "needsValidation", "hasPlaceholder", "includes", "hasDefaultValue", "isLayoutComponent", "hasDisabled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasClearable", "hasAdvancedProps", "hasOptions", "hasStyle", "style", "_typeof2", "default", "styleWidth", "get", "width", "set", "value", "updateStyleWidth", "watch", "handler", "newItem", "_objectSpread2", "immediate", "deep", "methods", "updateProperty", "key", "$emit", "_defineProperty2", "updateOptions", "options", "updateRules", "rules", "addOption", "push", "label", "concat", "length", "removeOption", "index", "splice", "addRule", "message", "trigger", "removeRule", "getDefaultValueComponent", "componentMap", "getDefaultValueProps", "propsMap", "showPassword", "controlsPosition"], "sources": ["src/components/FormDesigner/components/FormProperties.vue"], "sourcesContent": ["<template>\n  <div class=\"form-properties\">\n    <el-tabs v-model=\"activeTab\" type=\"border-card\">\n      <!-- 基础属性 -->\n      <el-tab-pane label=\"基础属性\" name=\"basic\">\n        <el-form :model=\"properties\" label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"字段标识\">\n            <el-input v-model=\"properties.id\" disabled />\n          </el-form-item>\n          \n          <el-form-item label=\"标签文字\">\n            <el-input v-model=\"properties.label\" @input=\"updateProperty('label', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"占位提示\" v-if=\"hasPlaceholder\">\n            <el-input v-model=\"properties.placeholder\" @input=\"updateProperty('placeholder', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"默认值\" v-if=\"hasDefaultValue\">\n            <component\n              :is=\"getDefaultValueComponent()\"\n              v-model=\"properties.defaultValue\"\n              @input=\"updateProperty('defaultValue', $event)\"\n              v-bind=\"getDefaultValueProps()\"\n            />\n          </el-form-item>\n\n          <el-form-item label=\"是否必填\" v-if=\"needsValidation\">\n            <el-switch v-model=\"properties.required\" @change=\"updateProperty('required', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"是否禁用\" v-if=\"hasDisabled\">\n            <el-switch v-model=\"properties.disabled\" @change=\"updateProperty('disabled', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"是否只读\" v-if=\"hasReadonly\">\n            <el-switch v-model=\"properties.readonly\" @change=\"updateProperty('readonly', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"是否可清空\" v-if=\"hasClearable\">\n            <el-switch v-model=\"properties.clearable\" @change=\"updateProperty('clearable', $event)\" />\n          </el-form-item>\n        </el-form>\n      </el-tab-pane>\n\n      <!-- 高级属性 -->\n      <el-tab-pane label=\"高级属性\" name=\"advanced\" v-if=\"hasAdvancedProps\">\n        <el-form :model=\"properties\" label-width=\"80px\" size=\"small\">\n          <!-- 输入框特有属性 -->\n          <template v-if=\"item.type === 'input' || item.type === 'textarea'\">\n            <el-form-item label=\"最大长度\">\n              <el-input-number v-model=\"properties.maxlength\" :min=\"0\" @change=\"updateProperty('maxlength', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示字数\">\n              <el-switch v-model=\"properties.showWordLimit\" @change=\"updateProperty('showWordLimit', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 数字输入框特有属性 -->\n          <template v-if=\"item.type === 'number'\">\n            <el-form-item label=\"最小值\">\n              <el-input-number v-model=\"properties.min\" @change=\"updateProperty('min', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"最大值\">\n              <el-input-number v-model=\"properties.max\" @change=\"updateProperty('max', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"步长\">\n              <el-input-number v-model=\"properties.step\" :min=\"0\" @change=\"updateProperty('step', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"精度\">\n              <el-input-number v-model=\"properties.precision\" :min=\"0\" @change=\"updateProperty('precision', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 选择器特有属性 -->\n          <template v-if=\"item.type === 'select'\">\n            <el-form-item label=\"多选\">\n              <el-switch v-model=\"properties.multiple\" @change=\"updateProperty('multiple', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"可搜索\">\n              <el-switch v-model=\"properties.filterable\" @change=\"updateProperty('filterable', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"允许创建\">\n              <el-switch v-model=\"properties.allowCreate\" @change=\"updateProperty('allowCreate', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 开关特有属性 -->\n          <template v-if=\"item.type === 'switch'\">\n            <el-form-item label=\"开启文字\">\n              <el-input v-model=\"properties.activeText\" @input=\"updateProperty('activeText', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"关闭文字\">\n              <el-input v-model=\"properties.inactiveText\" @input=\"updateProperty('inactiveText', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"开启值\">\n              <el-input v-model=\"properties.activeValue\" @input=\"updateProperty('activeValue', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"关闭值\">\n              <el-input v-model=\"properties.inactiveValue\" @input=\"updateProperty('inactiveValue', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 滑块特有属性 -->\n          <template v-if=\"item.type === 'slider'\">\n            <el-form-item label=\"显示输入框\">\n              <el-switch v-model=\"properties.showInput\" @change=\"updateProperty('showInput', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示间断点\">\n              <el-switch v-model=\"properties.showStops\" @change=\"updateProperty('showStops', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"范围选择\">\n              <el-switch v-model=\"properties.range\" @change=\"updateProperty('range', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 评分特有属性 -->\n          <template v-if=\"item.type === 'rate'\">\n            <el-form-item label=\"最大分值\">\n              <el-input-number v-model=\"properties.max\" :min=\"1\" @change=\"updateProperty('max', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"允许半选\">\n              <el-switch v-model=\"properties.allowHalf\" @change=\"updateProperty('allowHalf', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示文字\">\n              <el-switch v-model=\"properties.showText\" @change=\"updateProperty('showText', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"显示分数\">\n              <el-switch v-model=\"properties.showScore\" @change=\"updateProperty('showScore', $event)\" />\n            </el-form-item>\n          </template>\n\n          <!-- 上传特有属性 -->\n          <template v-if=\"item.type === 'upload'\">\n            <el-form-item label=\"上传地址\">\n              <el-input v-model=\"properties.action\" @input=\"updateProperty('action', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"多选\">\n              <el-switch v-model=\"properties.multiple\" @change=\"updateProperty('multiple', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"拖拽上传\">\n              <el-switch v-model=\"properties.drag\" @change=\"updateProperty('drag', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"文件类型\">\n              <el-input v-model=\"properties.accept\" @input=\"updateProperty('accept', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"文件个数限制\">\n              <el-input-number v-model=\"properties.limit\" :min=\"0\" @change=\"updateProperty('limit', $event)\" />\n            </el-form-item>\n          </template>\n        </el-form>\n      </el-tab-pane>\n\n      <!-- 选项配置 -->\n      <el-tab-pane label=\"选项配置\" name=\"options\" v-if=\"hasOptions\">\n        <div class=\"options-config\">\n          <div class=\"options-header\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addOption\">添加选项</el-button>\n          </div>\n          <div class=\"options-list\">\n            <div \n              v-for=\"(option, index) in properties.options\" \n              :key=\"index\"\n              class=\"option-item\"\n            >\n              <el-input \n                v-model=\"option.label\" \n                placeholder=\"选项标签\"\n                size=\"small\"\n                @input=\"updateOptions\"\n              />\n              <el-input \n                v-model=\"option.value\" \n                placeholder=\"选项值\"\n                size=\"small\"\n                @input=\"updateOptions\"\n              />\n              <el-button \n                type=\"danger\" \n                size=\"small\" \n                icon=\"el-icon-delete\"\n                @click=\"removeOption(index)\"\n              />\n            </div>\n          </div>\n        </div>\n      </el-tab-pane>\n\n      <!-- 样式配置 -->\n      <el-tab-pane label=\"样式配置\" name=\"style\">\n        <el-form :model=\"properties\" label-width=\"80px\" size=\"small\">\n          <el-form-item label=\"栅格占位\">\n            <el-slider \n              v-model=\"properties.span\" \n              :min=\"1\" \n              :max=\"24\" \n              show-input\n              @change=\"updateProperty('span', $event)\"\n            />\n          </el-form-item>\n\n          <el-form-item label=\"标签宽度\">\n            <el-input v-model=\"properties.labelWidth\" @input=\"updateProperty('labelWidth', $event)\" />\n          </el-form-item>\n\n          <el-form-item label=\"组件宽度\" v-if=\"hasStyle\">\n            <el-input v-model=\"styleWidth\" @input=\"updateStyleWidth\" placeholder=\"如: 100%, 200px\" />\n          </el-form-item>\n\n          <!-- 文本样式 -->\n          <template v-if=\"item.type === 'text'\">\n            <el-form-item label=\"文字对齐\">\n              <el-select v-model=\"properties.textAlign\" @change=\"updateProperty('textAlign', $event)\">\n                <el-option label=\"左对齐\" value=\"left\" />\n                <el-option label=\"居中\" value=\"center\" />\n                <el-option label=\"右对齐\" value=\"right\" />\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"字体大小\">\n              <el-input v-model=\"properties.fontSize\" @input=\"updateProperty('fontSize', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"字体颜色\">\n              <el-color-picker v-model=\"properties.color\" @change=\"updateProperty('color', $event)\" />\n            </el-form-item>\n            <el-form-item label=\"字体粗细\">\n              <el-select v-model=\"properties.fontWeight\" @change=\"updateProperty('fontWeight', $event)\">\n                <el-option label=\"正常\" value=\"normal\" />\n                <el-option label=\"粗体\" value=\"bold\" />\n              </el-select>\n            </el-form-item>\n          </template>\n        </el-form>\n      </el-tab-pane>\n\n      <!-- 验证规则 -->\n      <el-tab-pane label=\"验证规则\" name=\"validation\" v-if=\"needsValidation\">\n        <div class=\"validation-config\">\n          <div class=\"validation-header\">\n            <el-button type=\"primary\" size=\"small\" @click=\"addRule\">添加规则</el-button>\n          </div>\n          <div class=\"validation-list\">\n            <div \n              v-for=\"(rule, index) in properties.rules\" \n              :key=\"index\"\n              class=\"rule-item\"\n            >\n              <el-form :model=\"rule\" label-width=\"60px\" size=\"small\">\n                <el-form-item label=\"类型\">\n                  <el-select v-model=\"rule.type\" @change=\"updateRules\">\n                    <el-option label=\"必填\" value=\"required\" />\n                    <el-option label=\"最小长度\" value=\"min\" />\n                    <el-option label=\"最大长度\" value=\"max\" />\n                    <el-option label=\"正则\" value=\"pattern\" />\n                    <el-option label=\"自定义\" value=\"validator\" />\n                  </el-select>\n                </el-form-item>\n                <el-form-item label=\"值\" v-if=\"rule.type !== 'required'\">\n                  <el-input v-model=\"rule.value\" @input=\"updateRules\" />\n                </el-form-item>\n                <el-form-item label=\"提示\">\n                  <el-input v-model=\"rule.message\" @input=\"updateRules\" />\n                </el-form-item>\n                <el-form-item>\n                  <el-button type=\"danger\" size=\"small\" @click=\"removeRule(index)\">删除</el-button>\n                </el-form-item>\n              </el-form>\n            </div>\n          </div>\n        </div>\n      </el-tab-pane>\n    </el-tabs>\n  </div>\n</template>\n\n<script>\nimport { isLayoutComponent, needsValidation } from '../utils/index.js'\n\nexport default {\n  name: 'FormProperties',\n  props: {\n    item: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      activeTab: 'basic',\n      properties: {}\n    }\n  },\n  computed: {\n    needsValidation() {\n      return needsValidation(this.item.type)\n    },\n    \n    hasPlaceholder() {\n      return ['input', 'textarea', 'number', 'password', 'select', 'date', 'time', 'cascader'].includes(this.item.type)\n    },\n    \n    hasDefaultValue() {\n      return !isLayoutComponent(this.item.type)\n    },\n    \n    hasDisabled() {\n      return !['divider', 'text', 'html', 'alert'].includes(this.item.type)\n    },\n    \n    hasReadonly() {\n      return ['input', 'textarea', 'password', 'date', 'time'].includes(this.item.type)\n    },\n    \n    hasClearable() {\n      return ['input', 'select', 'date', 'time', 'cascader'].includes(this.item.type)\n    },\n    \n    hasAdvancedProps() {\n      return !['divider', 'text', 'html', 'alert'].includes(this.item.type)\n    },\n    \n    hasOptions() {\n      return ['select', 'radio', 'checkbox'].includes(this.item.type)\n    },\n    \n    hasStyle() {\n      return this.properties.style && typeof this.properties.style === 'object'\n    },\n    \n    styleWidth: {\n      get() {\n        return this.hasStyle ? this.properties.style.width : ''\n      },\n      set(value) {\n        this.updateStyleWidth(value)\n      }\n    }\n  },\n  watch: {\n    item: {\n      handler(newItem) {\n        this.properties = { ...newItem }\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    updateProperty(key, value) {\n      this.properties[key] = value\n      this.$emit('update', { [key]: value })\n    },\n    \n    updateOptions() {\n      this.$emit('update', { options: this.properties.options })\n    },\n    \n    updateRules() {\n      this.$emit('update', { rules: this.properties.rules })\n    },\n    \n    updateStyleWidth(value) {\n      if (!this.properties.style) {\n        this.properties.style = {}\n      }\n      this.properties.style.width = value\n      this.$emit('update', { style: this.properties.style })\n    },\n    \n    addOption() {\n      if (!this.properties.options) {\n        this.properties.options = []\n      }\n      this.properties.options.push({\n        label: `选项${this.properties.options.length + 1}`,\n        value: `${this.properties.options.length + 1}`\n      })\n      this.updateOptions()\n    },\n    \n    removeOption(index) {\n      this.properties.options.splice(index, 1)\n      this.updateOptions()\n    },\n    \n    addRule() {\n      if (!this.properties.rules) {\n        this.properties.rules = []\n      }\n      this.properties.rules.push({\n        type: 'required',\n        message: '此字段为必填项',\n        trigger: 'blur'\n      })\n      this.updateRules()\n    },\n    \n    removeRule(index) {\n      this.properties.rules.splice(index, 1)\n      this.updateRules()\n    },\n    \n    getDefaultValueComponent() {\n      const componentMap = {\n        'input': 'el-input',\n        'textarea': 'el-input',\n        'number': 'el-input-number',\n        'password': 'el-input',\n        'switch': 'el-switch',\n        'slider': 'el-slider',\n        'rate': 'el-rate'\n      }\n      return componentMap[this.item.type] || 'el-input'\n    },\n    \n    getDefaultValueProps() {\n      const propsMap = {\n        'textarea': { type: 'textarea' },\n        'password': { type: 'password', showPassword: true },\n        'number': { controlsPosition: 'right' }\n      }\n      return propsMap[this.item.type] || {}\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-properties {\n  height: 100%;\n  \n  .options-config, .validation-config {\n    .options-header, .validation-header {\n      margin-bottom: 10px;\n    }\n    \n    .option-item, .rule-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 10px;\n      padding: 10px;\n      border: 1px solid #e4e7ed;\n      border-radius: 4px;\n      \n      .el-input {\n        margin-right: 10px;\n      }\n    }\n    \n    .rule-item {\n      flex-direction: column;\n      align-items: stretch;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AAmRA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACAC,eAAA,WAAAA,gBAAA;MACA,WAAAA,sBAAA,OAAAR,IAAA,CAAAC,IAAA;IACA;IAEAQ,cAAA,WAAAA,eAAA;MACA,yFAAAC,QAAA,MAAAV,IAAA,CAAAC,IAAA;IACA;IAEAU,eAAA,WAAAA,gBAAA;MACA,YAAAC,wBAAA,OAAAZ,IAAA,CAAAC,IAAA;IACA;IAEAY,WAAA,WAAAA,YAAA;MACA,6CAAAH,QAAA,MAAAV,IAAA,CAAAC,IAAA;IACA;IAEAa,WAAA,WAAAA,YAAA;MACA,yDAAAJ,QAAA,MAAAV,IAAA,CAAAC,IAAA;IACA;IAEAc,YAAA,WAAAA,aAAA;MACA,uDAAAL,QAAA,MAAAV,IAAA,CAAAC,IAAA;IACA;IAEAe,gBAAA,WAAAA,iBAAA;MACA,6CAAAN,QAAA,MAAAV,IAAA,CAAAC,IAAA;IACA;IAEAgB,UAAA,WAAAA,WAAA;MACA,uCAAAP,QAAA,MAAAV,IAAA,CAAAC,IAAA;IACA;IAEAiB,QAAA,WAAAA,SAAA;MACA,YAAAZ,UAAA,CAAAa,KAAA,QAAAC,QAAA,CAAAC,OAAA,OAAAf,UAAA,CAAAa,KAAA;IACA;IAEAG,UAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAL,QAAA,QAAAZ,UAAA,CAAAa,KAAA,CAAAK,KAAA;MACA;MACAC,GAAA,WAAAA,IAAAC,KAAA;QACA,KAAAC,gBAAA,CAAAD,KAAA;MACA;IACA;EACA;EACAE,KAAA;IACA5B,IAAA;MACA6B,OAAA,WAAAA,QAAAC,OAAA;QACA,KAAAxB,UAAA,OAAAyB,cAAA,CAAAV,OAAA,MAAAS,OAAA;MACA;MACAE,SAAA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,GAAA,EAAAV,KAAA;MACA,KAAApB,UAAA,CAAA8B,GAAA,IAAAV,KAAA;MACA,KAAAW,KAAA,eAAAC,gBAAA,CAAAjB,OAAA,MAAAe,GAAA,EAAAV,KAAA;IACA;IAEAa,aAAA,WAAAA,cAAA;MACA,KAAAF,KAAA;QAAAG,OAAA,OAAAlC,UAAA,CAAAkC;MAAA;IACA;IAEAC,WAAA,WAAAA,YAAA;MACA,KAAAJ,KAAA;QAAAK,KAAA,OAAApC,UAAA,CAAAoC;MAAA;IACA;IAEAf,gBAAA,WAAAA,iBAAAD,KAAA;MACA,UAAApB,UAAA,CAAAa,KAAA;QACA,KAAAb,UAAA,CAAAa,KAAA;MACA;MACA,KAAAb,UAAA,CAAAa,KAAA,CAAAK,KAAA,GAAAE,KAAA;MACA,KAAAW,KAAA;QAAAlB,KAAA,OAAAb,UAAA,CAAAa;MAAA;IACA;IAEAwB,SAAA,WAAAA,UAAA;MACA,UAAArC,UAAA,CAAAkC,OAAA;QACA,KAAAlC,UAAA,CAAAkC,OAAA;MACA;MACA,KAAAlC,UAAA,CAAAkC,OAAA,CAAAI,IAAA;QACAC,KAAA,iBAAAC,MAAA,MAAAxC,UAAA,CAAAkC,OAAA,CAAAO,MAAA;QACArB,KAAA,KAAAoB,MAAA,MAAAxC,UAAA,CAAAkC,OAAA,CAAAO,MAAA;MACA;MACA,KAAAR,aAAA;IACA;IAEAS,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAA3C,UAAA,CAAAkC,OAAA,CAAAU,MAAA,CAAAD,KAAA;MACA,KAAAV,aAAA;IACA;IAEAY,OAAA,WAAAA,QAAA;MACA,UAAA7C,UAAA,CAAAoC,KAAA;QACA,KAAApC,UAAA,CAAAoC,KAAA;MACA;MACA,KAAApC,UAAA,CAAAoC,KAAA,CAAAE,IAAA;QACA3C,IAAA;QACAmD,OAAA;QACAC,OAAA;MACA;MACA,KAAAZ,WAAA;IACA;IAEAa,UAAA,WAAAA,WAAAL,KAAA;MACA,KAAA3C,UAAA,CAAAoC,KAAA,CAAAQ,MAAA,CAAAD,KAAA;MACA,KAAAR,WAAA;IACA;IAEAc,wBAAA,WAAAA,yBAAA;MACA,IAAAC,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,MAAAxD,IAAA,CAAAC,IAAA;IACA;IAEAwD,oBAAA,WAAAA,qBAAA;MACA,IAAAC,QAAA;QACA;UAAAzD,IAAA;QAAA;QACA;UAAAA,IAAA;UAAA0D,YAAA;QAAA;QACA;UAAAC,gBAAA;QAAA;MACA;MACA,OAAAF,QAAA,MAAA1D,IAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}