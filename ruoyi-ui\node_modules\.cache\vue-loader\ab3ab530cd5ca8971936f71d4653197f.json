{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\form\\designer\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\form\\designer\\index.vue", "mtime": 1752410103982}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBOb2RlRm9ybU1hbmFnZXIgZnJvbSAnQC9jb21wb25lbnRzL05vZGVGb3JtTWFuYWdlcicKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRm9ybURlc2lnbmVyJywKICBjb21wb25lbnRzOiB7CiAgICBOb2RlRm9ybU1hbmFnZXIKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmb3JtQ29uZmlnOiBbXSwKICAgICAgc2hvd0hlbHBEaWFsb2c6IGZhbHNlCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5sb2FkRm9ybUNvbmZpZygpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOWKoOi9veihqOWNlemFjee9riAqLwogICAgbG9hZEZvcm1Db25maWcoKSB7CiAgICAgIC8vIOi/memHjOWPr+S7peS7juWQjuerr+WKoOi9veW3suS/neWtmOeahOmFjee9rgogICAgICAvLyDmmoLml7bkvb/nlKjmnKzlnLDlrZjlgqgKICAgICAgY29uc3Qgc2F2ZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnZmxvd2FibGVfZm9ybV9jb25maWcnKTsKICAgICAgaWYgKHNhdmVkKSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGNvbnN0IGNvbmZpZyA9IEpTT04ucGFyc2Uoc2F2ZWQpOwogICAgICAgICAgdGhpcy5mb3JtQ29uZmlnID0gY29uZmlnLm5vZGVGb3JtcyB8fCBbXTsKICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgY29uc29sZS53YXJuKCfliqDovb3ooajljZXphY3nva7lpLHotKU6JywgZXJyb3IpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvKiog5aSE55CG5L+d5a2YICovCiAgICBoYW5kbGVTYXZlKGNvbmZpZykgewogICAgICAvLyDkv53lrZjliLDmnKzlnLDlrZjlgqgKICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2Zsb3dhYmxlX2Zvcm1fY29uZmlnJywgSlNPTi5zdHJpbmdpZnkoY29uZmlnKSk7CiAgICAgIAogICAgICAvLyDov5nph4zlj6/ku6XosIPnlKhBUEnkv53lrZjliLDlkI7nq68KICAgICAgLy8gc2F2ZUZvcm1Db25maWcoY29uZmlnKS50aGVuKCgpID0+IHsKICAgICAgLy8gICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpOwogICAgICAvLyB9KS5jYXRjaCgoKSA9PiB7CiAgICAgIC8vICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5aSx6LSlJyk7CiAgICAgIC8vIH0pOwogICAgfSwKCiAgICAvKiog5pi+56S65biu5YqpICovCiAgICBzaG93SGVscCgpIHsKICAgICAgdGhpcy5zaG93SGVscERpYWxvZyA9IHRydWU7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/form/designer", "sourcesContent": ["<template>\n  <div class=\"form-designer-page\">\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <h2>\n          <i class=\"el-icon-edit-outline\"></i>\n          流程表单设计器\n        </h2>\n        <p>为不同的流程节点设计专属的表单</p>\n      </div>\n    </div>\n\n    <div class=\"page-content\">\n      <el-card>\n        <div slot=\"header\" class=\"card-header\">\n          <span>表单设计</span>\n          <div class=\"header-actions\">\n            <el-button type=\"text\" @click=\"showHelp\">\n              <i class=\"el-icon-question\"></i>\n              使用帮助\n            </el-button>\n          </div>\n        </div>\n\n        <node-form-manager \n          v-model=\"formConfig\" \n          @save=\"handleSave\"\n        />\n      </el-card>\n    </div>\n\n    <!-- 使用帮助对话框 -->\n    <el-dialog title=\"使用帮助\" :visible.sync=\"showHelpDialog\" width=\"600px\">\n      <div class=\"help-content\">\n        <h4>🎯 功能介绍</h4>\n        <p>这个表单设计器允许您为流程的不同节点创建专属的表单，每个节点可以有不同的字段和配置。</p>\n        \n        <h4>📋 使用步骤</h4>\n        <ol>\n          <li><strong>添加节点表单</strong>：点击\"添加节点表单\"创建新的节点</li>\n          <li><strong>配置节点信息</strong>：设置节点名称和类型</li>\n          <li><strong>设计表单字段</strong>：为每个节点添加所需的表单字段</li>\n          <li><strong>预览和保存</strong>：预览表单效果并保存配置</li>\n        </ol>\n        \n        <h4>🔧 支持的字段类型</h4>\n        <ul>\n          <li><strong>单行文本</strong>：适用于姓名、标题等短文本</li>\n          <li><strong>多行文本</strong>：适用于描述、备注等长文本</li>\n          <li><strong>数字</strong>：适用于金额、数量等数值</li>\n          <li><strong>下拉选择</strong>：适用于部门、类型等固定选项</li>\n          <li><strong>日期</strong>：适用于申请日期、截止日期等</li>\n          <li><strong>开关</strong>：适用于是否同意等布尔值</li>\n          <li><strong>单选框</strong>：适用于性别、优先级等单选</li>\n          <li><strong>复选框</strong>：适用于技能、兴趣等多选</li>\n        </ul>\n        \n        <h4>💡 最佳实践</h4>\n        <ul>\n          <li>为每个节点设置清晰的名称，便于识别</li>\n          <li>合理选择字段类型，提升用户体验</li>\n          <li>为选择类字段配置完整的选项</li>\n          <li>定期导出配置文件作为备份</li>\n        </ul>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport NodeFormManager from '@/components/NodeFormManager'\n\nexport default {\n  name: 'FormDesigner',\n  components: {\n    NodeFormManager\n  },\n  data() {\n    return {\n      formConfig: [],\n      showHelpDialog: false\n    }\n  },\n  created() {\n    this.loadFormConfig();\n  },\n  methods: {\n    /** 加载表单配置 */\n    loadFormConfig() {\n      // 这里可以从后端加载已保存的配置\n      // 暂时使用本地存储\n      const saved = localStorage.getItem('flowable_form_config');\n      if (saved) {\n        try {\n          const config = JSON.parse(saved);\n          this.formConfig = config.nodeForms || [];\n        } catch (error) {\n          console.warn('加载表单配置失败:', error);\n        }\n      }\n    },\n\n    /** 处理保存 */\n    handleSave(config) {\n      // 保存到本地存储\n      localStorage.setItem('flowable_form_config', JSON.stringify(config));\n      \n      // 这里可以调用API保存到后端\n      // saveFormConfig(config).then(() => {\n      //   this.$message.success('保存成功');\n      // }).catch(() => {\n      //   this.$message.error('保存失败');\n      // });\n    },\n\n    /** 显示帮助 */\n    showHelp() {\n      this.showHelpDialog = true;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-designer-page {\n  padding: 20px;\n  background-color: #f0f2f5;\n  min-height: calc(100vh - 84px);\n}\n\n.page-header {\n  margin-bottom: 20px;\n  \n  .header-content {\n    h2 {\n      margin: 0 0 8px 0;\n      color: #303133;\n      font-size: 24px;\n      \n      i {\n        margin-right: 8px;\n        color: #409EFF;\n      }\n    }\n    \n    p {\n      margin: 0;\n      color: #606266;\n      font-size: 14px;\n    }\n  }\n}\n\n.page-content {\n  .el-card {\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  }\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  \n  span {\n    font-weight: 600;\n    color: #303133;\n  }\n}\n\n.help-content {\n  h4 {\n    color: #303133;\n    margin: 16px 0 8px 0;\n    \n    &:first-child {\n      margin-top: 0;\n    }\n  }\n  \n  p, li {\n    color: #606266;\n    line-height: 1.6;\n  }\n  \n  ul, ol {\n    padding-left: 20px;\n  }\n  \n  li {\n    margin-bottom: 4px;\n  }\n  \n  strong {\n    color: #303133;\n  }\n}\n</style>\n"]}]}