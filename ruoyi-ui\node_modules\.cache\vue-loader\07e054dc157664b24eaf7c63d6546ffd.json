{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeForm\\index.vue?vue&type=style&index=0&id=152a7919&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeForm\\index.vue", "mtime": 1752410027356}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubm9kZS1mb3JtLWNvbnRhaW5lciB7CiAgYm9yZGVyOiAxcHggc29saWQgI0VCRUVGNTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLmZvcm0taGVhZGVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjVGN0ZBOwogIHBhZGRpbmc6IDEycHggMTZweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI0VCRUVGNTsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwoKICBoNCB7CiAgICBtYXJnaW46IDA7CiAgICBjb2xvcjogIzYwNjI2NjsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGZvbnQtd2VpZ2h0OiA2MDA7CgogICAgaSB7CiAgICAgIG1hcmdpbi1yaWdodDogOHB4OwogICAgICBjb2xvcjogIzQwOUVGRjsKICAgIH0KICB9Cn0KCi5mb3JtLWNvbnRlbnQgewogIHBhZGRpbmc6IDE2cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7Cn0KCi5maWVsZC1saXN0IHsKICAuZmllbGQtaXRlbSB7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgYm9yZGVyOiAxcHggc29saWQgI0U0RTdFRDsKICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgIG92ZXJmbG93OiBoaWRkZW47CgogICAgJjpsYXN0LWNoaWxkIHsKICAgICAgbWFyZ2luLWJvdHRvbTogMDsKICAgIH0KICB9CgogIC5maWVsZC1oZWFkZXIgewogICAgYmFja2dyb3VuZC1jb2xvcjogI0ZBRkFGQTsKICAgIHBhZGRpbmc6IDhweCAxMnB4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNFNEU3RUQ7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCiAgICAuZmllbGQtbGFiZWwgewogICAgICBmb250LXdlaWdodDogNjAwOwogICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgZmxleDogMTsKICAgIH0KCiAgICAuZmllbGQtdHlwZSB7CiAgICAgIGNvbG9yOiAjOTA5Mzk5OwogICAgICBmb250LXNpemU6IDEycHg7CiAgICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgIH0KCiAgICAucmVtb3ZlLWJ0biB7CiAgICAgIGNvbG9yOiAjRjU2QzZDOwogICAgfQogIH0KCiAgLmZpZWxkLWNvbnRlbnQgewogICAgcGFkZGluZzogMTJweDsKICB9Cn0KCi5lbXB0eS1zdGF0ZSB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDQwcHggMjBweDsKICBjb2xvcjogIzkwOTM5OTsKCiAgaSB7CiAgICBmb250LXNpemU6IDQ4cHg7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgZGlzcGxheTogYmxvY2s7CiAgfQoKICBwIHsKICAgIG1hcmdpbjogMDsKICAgIGZvbnQtc2l6ZTogMTRweDsKICB9Cn0KCi5vcHRpb25zLWNvbmZpZyB7CiAgLm9wdGlvbi1pdGVtIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgbWFyZ2luLWJvdHRvbTogOHB4OwogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiWA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/NodeForm", "sourcesContent": ["<template>\n  <div class=\"node-form-container\">\n    <div class=\"form-header\">\n      <h4>\n        <i class=\"el-icon-edit-outline\"></i>\n        {{ title || '节点表单' }}\n      </h4>\n      <el-button \n        v-if=\"!readonly\" \n        type=\"primary\" \n        size=\"small\" \n        @click=\"addField\"\n      >\n        添加字段\n      </el-button>\n    </div>\n\n    <div class=\"form-content\">\n      <!-- 表单字段列表 -->\n      <div v-if=\"formFields.length > 0\" class=\"field-list\">\n        <div \n          v-for=\"(field, index) in formFields\" \n          :key=\"field.id || index\"\n          class=\"field-item\"\n        >\n          <div class=\"field-header\">\n            <span class=\"field-label\">{{ field.label }}</span>\n            <span class=\"field-type\">{{ getFieldTypeText(field.type) }}</span>\n            <el-button \n              v-if=\"!readonly\" \n              type=\"text\" \n              size=\"mini\" \n              @click=\"removeField(index)\"\n              class=\"remove-btn\"\n            >\n              删除\n            </el-button>\n          </div>\n          \n          <div class=\"field-content\">\n            <!-- 文本输入 -->\n            <el-input \n              v-if=\"field.type === 'text'\" \n              v-model=\"field.value\"\n              :placeholder=\"field.placeholder\"\n              :readonly=\"readonly\"\n            />\n            \n            <!-- 多行文本 -->\n            <el-input \n              v-else-if=\"field.type === 'textarea'\" \n              v-model=\"field.value\"\n              type=\"textarea\"\n              :rows=\"3\"\n              :placeholder=\"field.placeholder\"\n              :readonly=\"readonly\"\n            />\n            \n            <!-- 数字输入 -->\n            <el-input-number \n              v-else-if=\"field.type === 'number'\" \n              v-model=\"field.value\"\n              :placeholder=\"field.placeholder\"\n              :readonly=\"readonly\"\n              style=\"width: 100%\"\n            />\n            \n            <!-- 选择器 -->\n            <el-select \n              v-else-if=\"field.type === 'select'\" \n              v-model=\"field.value\"\n              :placeholder=\"field.placeholder\"\n              :disabled=\"readonly\"\n              style=\"width: 100%\"\n            >\n              <el-option \n                v-for=\"option in field.options\" \n                :key=\"option.value\"\n                :label=\"option.label\"\n                :value=\"option.value\"\n              />\n            </el-select>\n            \n            <!-- 日期选择 -->\n            <el-date-picker \n              v-else-if=\"field.type === 'date'\" \n              v-model=\"field.value\"\n              type=\"date\"\n              :placeholder=\"field.placeholder\"\n              :readonly=\"readonly\"\n              style=\"width: 100%\"\n            />\n            \n            <!-- 开关 -->\n            <el-switch \n              v-else-if=\"field.type === 'switch'\" \n              v-model=\"field.value\"\n              :disabled=\"readonly\"\n            />\n            \n            <!-- 单选框组 -->\n            <el-radio-group \n              v-else-if=\"field.type === 'radio'\" \n              v-model=\"field.value\"\n              :disabled=\"readonly\"\n            >\n              <el-radio \n                v-for=\"option in field.options\" \n                :key=\"option.value\"\n                :label=\"option.value\"\n              >\n                {{ option.label }}\n              </el-radio>\n            </el-radio-group>\n            \n            <!-- 复选框组 -->\n            <el-checkbox-group \n              v-else-if=\"field.type === 'checkbox'\" \n              v-model=\"field.value\"\n              :disabled=\"readonly\"\n            >\n              <el-checkbox \n                v-for=\"option in field.options\" \n                :key=\"option.value\"\n                :label=\"option.value\"\n              >\n                {{ option.label }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 空状态 -->\n      <div v-else class=\"empty-state\">\n        <i class=\"el-icon-document-add\"></i>\n        <p>{{ readonly ? '暂无表单数据' : '点击\"添加字段\"开始创建表单' }}</p>\n      </div>\n    </div>\n\n    <!-- 字段配置对话框 -->\n    <el-dialog \n      title=\"添加表单字段\" \n      :visible.sync=\"showFieldDialog\"\n      width=\"500px\"\n    >\n      <el-form :model=\"newField\" label-width=\"80px\">\n        <el-form-item label=\"字段标签\">\n          <el-input v-model=\"newField.label\" placeholder=\"请输入字段标签\" />\n        </el-form-item>\n        \n        <el-form-item label=\"字段类型\">\n          <el-select v-model=\"newField.type\" style=\"width: 100%\">\n            <el-option label=\"单行文本\" value=\"text\" />\n            <el-option label=\"多行文本\" value=\"textarea\" />\n            <el-option label=\"数字\" value=\"number\" />\n            <el-option label=\"下拉选择\" value=\"select\" />\n            <el-option label=\"日期\" value=\"date\" />\n            <el-option label=\"开关\" value=\"switch\" />\n            <el-option label=\"单选框\" value=\"radio\" />\n            <el-option label=\"复选框\" value=\"checkbox\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"占位符\">\n          <el-input v-model=\"newField.placeholder\" placeholder=\"请输入占位符文本\" />\n        </el-form-item>\n        \n        <el-form-item \n          v-if=\"['select', 'radio', 'checkbox'].includes(newField.type)\" \n          label=\"选项配置\"\n        >\n          <div class=\"options-config\">\n            <div \n              v-for=\"(option, index) in newField.options\" \n              :key=\"index\"\n              class=\"option-item\"\n            >\n              <el-input \n                v-model=\"option.label\" \n                placeholder=\"选项标签\" \n                style=\"width: 45%; margin-right: 10px;\"\n              />\n              <el-input \n                v-model=\"option.value\" \n                placeholder=\"选项值\" \n                style=\"width: 35%; margin-right: 10px;\"\n              />\n              <el-button \n                type=\"text\" \n                @click=\"removeOption(index)\"\n                style=\"color: #f56c6c;\"\n              >\n                删除\n              </el-button>\n            </div>\n            <el-button type=\"text\" @click=\"addOption\">+ 添加选项</el-button>\n          </div>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\">\n        <el-button @click=\"showFieldDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmAddField\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'NodeForm',\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    readonly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      formFields: [],\n      showFieldDialog: false,\n      newField: {\n        label: '',\n        type: 'text',\n        placeholder: '',\n        value: '',\n        options: []\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        this.formFields = newVal || [];\n      },\n      immediate: true,\n      deep: true\n    },\n    formFields: {\n      handler(newVal) {\n        this.$emit('input', newVal);\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 添加字段 */\n    addField() {\n      this.newField = {\n        label: '',\n        type: 'text',\n        placeholder: '',\n        value: '',\n        options: []\n      };\n      this.showFieldDialog = true;\n    },\n\n    /** 确认添加字段 */\n    confirmAddField() {\n      if (!this.newField.label) {\n        this.$message.warning('请输入字段标签');\n        return;\n      }\n\n      const field = {\n        id: Date.now(),\n        label: this.newField.label,\n        type: this.newField.type,\n        placeholder: this.newField.placeholder,\n        value: this.getDefaultValue(this.newField.type),\n        options: [...this.newField.options]\n      };\n\n      this.formFields.push(field);\n      this.showFieldDialog = false;\n    },\n\n    /** 移除字段 */\n    removeField(index) {\n      this.formFields.splice(index, 1);\n    },\n\n    /** 添加选项 */\n    addOption() {\n      this.newField.options.push({ label: '', value: '' });\n    },\n\n    /** 移除选项 */\n    removeOption(index) {\n      this.newField.options.splice(index, 1);\n    },\n\n    /** 获取字段类型文本 */\n    getFieldTypeText(type) {\n      const typeMap = {\n        text: '文本',\n        textarea: '多行文本',\n        number: '数字',\n        select: '下拉选择',\n        date: '日期',\n        switch: '开关',\n        radio: '单选',\n        checkbox: '多选'\n      };\n      return typeMap[type] || type;\n    },\n\n    /** 获取默认值 */\n    getDefaultValue(type) {\n      switch (type) {\n        case 'number':\n          return 0;\n        case 'switch':\n          return false;\n        case 'checkbox':\n          return [];\n        default:\n          return '';\n      }\n    },\n\n    /** 获取表单数据 */\n    getFormData() {\n      const data = {};\n      this.formFields.forEach(field => {\n        data[field.label] = field.value;\n      });\n      return data;\n    },\n\n    /** 设置表单数据 */\n    setFormData(data) {\n      this.formFields.forEach(field => {\n        if (data.hasOwnProperty(field.label)) {\n          field.value = data[field.label];\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.node-form-container {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-header {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  border-bottom: 1px solid #EBEEF5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h4 {\n    margin: 0;\n    color: #606266;\n    font-size: 14px;\n    font-weight: 600;\n\n    i {\n      margin-right: 8px;\n      color: #409EFF;\n    }\n  }\n}\n\n.form-content {\n  padding: 16px;\n  background-color: white;\n}\n\n.field-list {\n  .field-item {\n    margin-bottom: 16px;\n    border: 1px solid #E4E7ED;\n    border-radius: 4px;\n    overflow: hidden;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .field-header {\n    background-color: #FAFAFA;\n    padding: 8px 12px;\n    border-bottom: 1px solid #E4E7ED;\n    display: flex;\n    align-items: center;\n\n    .field-label {\n      font-weight: 600;\n      color: #303133;\n      flex: 1;\n    }\n\n    .field-type {\n      color: #909399;\n      font-size: 12px;\n      margin-right: 10px;\n    }\n\n    .remove-btn {\n      color: #F56C6C;\n    }\n  }\n\n  .field-content {\n    padding: 12px;\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n\n  i {\n    font-size: 48px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.options-config {\n  .option-item {\n    display: flex;\n    align-items: center;\n    margin-bottom: 8px;\n  }\n}\n</style>\n"]}]}