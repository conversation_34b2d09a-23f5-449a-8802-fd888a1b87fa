{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\index.vue", "mtime": 1752386577717}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBkcmFnZ2FibGUgZnJvbSAndnVlZHJhZ2dhYmxlJwppbXBvcnQgRm9ybUl0ZW1XcmFwcGVyIGZyb20gJy4vY29tcG9uZW50cy9Gb3JtSXRlbVdyYXBwZXIudnVlJwppbXBvcnQgRm9ybVByb3BlcnRpZXMgZnJvbSAnLi9jb21wb25lbnRzL0Zvcm1Qcm9wZXJ0aWVzLnZ1ZScKaW1wb3J0IEZvcm1QcmV2aWV3IGZyb20gJy4vY29tcG9uZW50cy9Gb3JtUHJldmlldy52dWUnCmltcG9ydCB7IGdlbmVyYXRlSWQgfSBmcm9tICcuL3V0aWxzL2luZGV4LmpzJwppbXBvcnQgeyBiYXNpY0NvbXBvbmVudHMsIGFkdmFuY2VkQ29tcG9uZW50cywgbGF5b3V0Q29tcG9uZW50cyB9IGZyb20gJy4vY29uZmlnL2NvbXBvbmVudHMuanMnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0Zvcm1EZXNpZ25lcicsCiAgY29tcG9uZW50czogewogICAgZHJhZ2dhYmxlLAogICAgRm9ybUl0ZW1XcmFwcGVyLAogICAgRm9ybVByb3BlcnRpZXMsCiAgICBGb3JtUHJldmlldwogIH0sCiAgcHJvcHM6IHsKICAgIGZvcm1EYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZvcm1Db25maWc6IHsKICAgICAgICBmb3JtTmFtZTogJycsCiAgICAgICAgcmVtYXJrOiAnJwogICAgICB9LAogICAgICBmb3JtSXRlbXM6IFtdLAogICAgICBzZWxlY3RlZEl0ZW06IG51bGwsCiAgICAgIHByZXZpZXdWaXNpYmxlOiBmYWxzZSwKICAgICAgaW1wb3J0VmlzaWJsZTogZmFsc2UsCiAgICAgIGltcG9ydEpzb246ICcnLAogICAgICBiYXNpY0NvbXBvbmVudHMsCiAgICAgIGFkdmFuY2VkQ29tcG9uZW50cywKICAgICAgbGF5b3V0Q29tcG9uZW50cwogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuaW5pdEZvcm1EYXRhKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGluaXRGb3JtRGF0YSgpIHsKICAgICAgaWYgKHRoaXMuZm9ybURhdGEuZm9ybUlkKSB7CiAgICAgICAgdGhpcy5mb3JtQ29uZmlnLmZvcm1OYW1lID0gdGhpcy5mb3JtRGF0YS5mb3JtTmFtZSB8fCAnJwogICAgICAgIHRoaXMuZm9ybUNvbmZpZy5yZW1hcmsgPSB0aGlzLmZvcm1EYXRhLnJlbWFyayB8fCAnJwogICAgICAgIAogICAgICAgIC8vIOino+aekOihqOWNleWGheWuuQogICAgICAgIGlmICh0aGlzLmZvcm1EYXRhLmZvcm1Db250ZW50KSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICBjb25zdCBjb250ZW50ID0gSlNPTi5wYXJzZSh0aGlzLmZvcm1EYXRhLmZvcm1Db250ZW50KQogICAgICAgICAgICB0aGlzLmZvcm1JdGVtcyA9IGNvbnRlbnQuZm9ybUl0ZW1zIHx8IFtdCiAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgIGNvbnNvbGUud2Fybign6KGo5Y2V5YaF5a656Kej5p6Q5aSx6LSlOicsIGUpCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIGhhbmRsZURyYWdTdGFydChldmVudCwgY29tcG9uZW50KSB7CiAgICAgIGV2ZW50LmRhdGFUcmFuc2Zlci5zZXREYXRhKCdjb21wb25lbnQnLCBKU09OLnN0cmluZ2lmeShjb21wb25lbnQpKQogICAgfSwKCiAgICBoYW5kbGVEcmFnT3ZlcihldmVudCkgewogICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpCiAgICB9LAoKICAgIGhhbmRsZURyb3AoZXZlbnQpIHsKICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKQogICAgICBjb25zdCBjb21wb25lbnREYXRhID0gZXZlbnQuZGF0YVRyYW5zZmVyLmdldERhdGEoJ2NvbXBvbmVudCcpCiAgICAgIGlmIChjb21wb25lbnREYXRhKSB7CiAgICAgICAgY29uc3QgY29tcG9uZW50ID0gSlNPTi5wYXJzZShjb21wb25lbnREYXRhKQogICAgICAgIHRoaXMuYWRkRm9ybUl0ZW0oY29tcG9uZW50KQogICAgICB9CiAgICB9LAoKICAgIGFkZEZvcm1JdGVtKGNvbXBvbmVudCkgewogICAgICBjb25zdCBuZXdJdGVtID0gewogICAgICAgIGlkOiBnZW5lcmF0ZUlkKCksCiAgICAgICAgdHlwZTogY29tcG9uZW50LnR5cGUsCiAgICAgICAgbGFiZWw6IGNvbXBvbmVudC5sYWJlbCwKICAgICAgICBpY29uOiBjb21wb25lbnQuaWNvbiwKICAgICAgICAuLi5jb21wb25lbnQuZGVmYXVsdFByb3BzCiAgICAgIH0KICAgICAgdGhpcy5mb3JtSXRlbXMucHVzaChuZXdJdGVtKQogICAgICB0aGlzLnNlbGVjdGVkSXRlbSA9IG5ld0l0ZW0KICAgIH0sCgogICAgaGFuZGxlU2VsZWN0SXRlbShpdGVtKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRJdGVtID0gaXRlbQogICAgfSwKCiAgICBoYW5kbGVEZWxldGVJdGVtKGluZGV4KSB7CiAgICAgIHRoaXMuZm9ybUl0ZW1zLnNwbGljZShpbmRleCwgMSkKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRJdGVtICYmIHRoaXMuc2VsZWN0ZWRJdGVtLmlkID09PSB0aGlzLmZvcm1JdGVtc1tpbmRleF0/LmlkKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEl0ZW0gPSBudWxsCiAgICAgIH0KICAgIH0sCgogICAgaGFuZGxlQ2xvbmVJdGVtKGl0ZW0pIHsKICAgICAgY29uc3QgY2xvbmVkSXRlbSA9IHsKICAgICAgICAuLi5KU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGl0ZW0pKSwKICAgICAgICBpZDogZ2VuZXJhdGVJZCgpCiAgICAgIH0KICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmZvcm1JdGVtcy5maW5kSW5kZXgoaSA9PiBpLmlkID09PSBpdGVtLmlkKQogICAgICB0aGlzLmZvcm1JdGVtcy5zcGxpY2UoaW5kZXggKyAxLCAwLCBjbG9uZWRJdGVtKQogICAgfSwKCiAgICBoYW5kbGVVcGRhdGVQcm9wZXJ0aWVzKHByb3BlcnRpZXMpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRJdGVtKSB7CiAgICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLnNlbGVjdGVkSXRlbSwgcHJvcGVydGllcykKICAgICAgfQogICAgfSwKCiAgICBoYW5kbGVTb3J0RW5kKCkgewogICAgICAvLyDmi5bmi73mjpLluo/lrozmiJDlkI7nmoTlpITnkIYKICAgIH0sCgogICAgY2xlYXJTZWxlY3Rpb24oKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRJdGVtID0gbnVsbAogICAgfSwKCiAgICBoYW5kbGVTYXZlKCkgewogICAgICBpZiAoIXRoaXMuZm9ybUNvbmZpZy5mb3JtTmFtZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+i+k+WFpeihqOWNleWQjeensCcpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGNvbnN0IGZvcm1EYXRhID0gewogICAgICAgIC4uLnRoaXMuZm9ybURhdGEsCiAgICAgICAgZm9ybU5hbWU6IHRoaXMuZm9ybUNvbmZpZy5mb3JtTmFtZSwKICAgICAgICByZW1hcms6IHRoaXMuZm9ybUNvbmZpZy5yZW1hcmssCiAgICAgICAgZm9ybUNvbnRlbnQ6IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgIGZvcm1JdGVtczogdGhpcy5mb3JtSXRlbXMsCiAgICAgICAgICBjb25maWc6IHRoaXMuZm9ybUNvbmZpZwogICAgICAgIH0pCiAgICAgIH0KCiAgICAgIHRoaXMuJGVtaXQoJ3NhdmUnLCBmb3JtRGF0YSkKICAgIH0sCgogICAgaGFuZGxlQ2FuY2VsKCkgewogICAgICB0aGlzLiRlbWl0KCdjYW5jZWwnKQogICAgfSwKCiAgICBoYW5kbGVQcmV2aWV3KCkgewogICAgICB0aGlzLnByZXZpZXdWaXNpYmxlID0gdHJ1ZQogICAgfSwKCiAgICBoYW5kbGVFeHBvcnRKc29uKCkgewogICAgICBjb25zdCBleHBvcnREYXRhID0gewogICAgICAgIGZvcm1JdGVtczogdGhpcy5mb3JtSXRlbXMsCiAgICAgICAgY29uZmlnOiB0aGlzLmZvcm1Db25maWcKICAgICAgfQogICAgICAKICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtKU09OLnN0cmluZ2lmeShleHBvcnREYXRhLCBudWxsLCAyKV0sIHsgdHlwZTogJ2FwcGxpY2F0aW9uL2pzb24nIH0pCiAgICAgIGNvbnN0IHVybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYikKICAgICAgY29uc3QgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKQogICAgICBhLmhyZWYgPSB1cmwKICAgICAgYS5kb3dubG9hZCA9IGAke3RoaXMuZm9ybUNvbmZpZy5mb3JtTmFtZSB8fCAnZm9ybSd9Lmpzb25gCiAgICAgIGEuY2xpY2soKQogICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybCkKICAgIH0sCgogICAgaGFuZGxlSW1wb3J0SnNvbigpIHsKICAgICAgdGhpcy5pbXBvcnRWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLmltcG9ydEpzb24gPSAnJwogICAgfSwKCiAgICBoYW5kbGVJbXBvcnRDb25maXJtKCkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnBhcnNlKHRoaXMuaW1wb3J0SnNvbikKICAgICAgICBpZiAoZGF0YS5mb3JtSXRlbXMpIHsKICAgICAgICAgIHRoaXMuZm9ybUl0ZW1zID0gZGF0YS5mb3JtSXRlbXMKICAgICAgICB9CiAgICAgICAgaWYgKGRhdGEuY29uZmlnKSB7CiAgICAgICAgICB0aGlzLmZvcm1Db25maWcgPSB7IC4uLnRoaXMuZm9ybUNvbmZpZywgLi4uZGF0YS5jb25maWcgfQogICAgICAgIH0KICAgICAgICB0aGlzLmltcG9ydFZpc2libGUgPSBmYWxzZQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a+85YWl5oiQ5YqfJykKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ0pTT07moLzlvI/plJnor6/vvIzor7fmo4Dmn6XlkI7ph43or5UnKQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyJA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FormDesigner", "sourcesContent": ["<template>\n  <div class=\"form-designer\">\n    <div class=\"designer-header\">\n      <el-form :model=\"formConfig\" :inline=\"true\" size=\"small\">\n        <el-form-item label=\"表单名称\">\n          <el-input v-model=\"formConfig.formName\" placeholder=\"请输入表单名称\" style=\"width: 200px\" />\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"formConfig.remark\" placeholder=\"请输入备注\" style=\"width: 200px\" />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"handleSave\">保存表单</el-button>\n          <el-button @click=\"handleCancel\">取消</el-button>\n          <el-button type=\"success\" @click=\"handlePreview\">预览</el-button>\n          <el-button type=\"info\" @click=\"handleExportJson\">导出JSON</el-button>\n          <el-button type=\"warning\" @click=\"handleImportJson\">导入JSON</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <div class=\"designer-body\">\n      <!-- 左侧组件面板 -->\n      <div class=\"components-panel\">\n        <div class=\"panel-title\">组件库</div>\n        <div class=\"component-groups\">\n          <!-- 基础组件 -->\n          <div class=\"component-group\">\n            <div class=\"group-title\">基础组件</div>\n            <div class=\"component-list\">\n              <div \n                v-for=\"component in basicComponents\" \n                :key=\"component.type\"\n                class=\"component-item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, component)\"\n              >\n                <i :class=\"component.icon\"></i>\n                <span>{{ component.label }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 高级组件 -->\n          <div class=\"component-group\">\n            <div class=\"group-title\">高级组件</div>\n            <div class=\"component-list\">\n              <div \n                v-for=\"component in advancedComponents\" \n                :key=\"component.type\"\n                class=\"component-item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, component)\"\n              >\n                <i :class=\"component.icon\"></i>\n                <span>{{ component.label }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 布局组件 -->\n          <div class=\"component-group\">\n            <div class=\"group-title\">布局组件</div>\n            <div class=\"component-list\">\n              <div \n                v-for=\"component in layoutComponents\" \n                :key=\"component.type\"\n                class=\"component-item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, component)\"\n              >\n                <i :class=\"component.icon\"></i>\n                <span>{{ component.label }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 中间设计区域 -->\n      <div class=\"design-panel\">\n        <div class=\"panel-title\">表单设计区域</div>\n        <div \n          class=\"design-canvas\"\n          @drop=\"handleDrop\"\n          @dragover=\"handleDragOver\"\n          @click=\"clearSelection\"\n        >\n          <div v-if=\"formItems.length === 0\" class=\"empty-canvas\">\n            <i class=\"el-icon-plus\"></i>\n            <p>从左侧拖拽组件到此处开始设计表单</p>\n          </div>\n          \n          <draggable \n            v-model=\"formItems\" \n            group=\"form-items\"\n            :animation=\"200\"\n            ghost-class=\"ghost\"\n            chosen-class=\"chosen\"\n            @end=\"handleSortEnd\"\n          >\n            <form-item-wrapper\n              v-for=\"(item, index) in formItems\"\n              :key=\"item.id\"\n              :item=\"item\"\n              :index=\"index\"\n              :selected=\"selectedItem && selectedItem.id === item.id\"\n              @select=\"handleSelectItem\"\n              @delete=\"handleDeleteItem\"\n              @clone=\"handleCloneItem\"\n            />\n          </draggable>\n        </div>\n      </div>\n\n      <!-- 右侧属性面板 -->\n      <div class=\"properties-panel\">\n        <div class=\"panel-title\">属性配置</div>\n        <div class=\"properties-content\">\n          <form-properties\n            v-if=\"selectedItem\"\n            :item=\"selectedItem\"\n            @update=\"handleUpdateProperties\"\n          />\n          <div v-else class=\"no-selection\">\n            <i class=\"el-icon-info\"></i>\n            <p>请选择一个组件进行配置</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewVisible\" width=\"80%\" append-to-body>\n      <form-preview :form-items=\"formItems\" />\n    </el-dialog>\n\n    <!-- JSON导入对话框 -->\n    <el-dialog title=\"导入JSON\" :visible.sync=\"importVisible\" width=\"60%\" append-to-body>\n      <el-input\n        v-model=\"importJson\"\n        type=\"textarea\"\n        :rows=\"10\"\n        placeholder=\"请粘贴表单JSON配置\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"importVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleImportConfirm\">确定导入</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport draggable from 'vuedraggable'\nimport FormItemWrapper from './components/FormItemWrapper.vue'\nimport FormProperties from './components/FormProperties.vue'\nimport FormPreview from './components/FormPreview.vue'\nimport { generateId } from './utils/index.js'\nimport { basicComponents, advancedComponents, layoutComponents } from './config/components.js'\n\nexport default {\n  name: 'FormDesigner',\n  components: {\n    draggable,\n    FormItemWrapper,\n    FormProperties,\n    FormPreview\n  },\n  props: {\n    formData: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      formConfig: {\n        formName: '',\n        remark: ''\n      },\n      formItems: [],\n      selectedItem: null,\n      previewVisible: false,\n      importVisible: false,\n      importJson: '',\n      basicComponents,\n      advancedComponents,\n      layoutComponents\n    }\n  },\n  created() {\n    this.initFormData()\n  },\n  methods: {\n    initFormData() {\n      if (this.formData.formId) {\n        this.formConfig.formName = this.formData.formName || ''\n        this.formConfig.remark = this.formData.remark || ''\n        \n        // 解析表单内容\n        if (this.formData.formContent) {\n          try {\n            const content = JSON.parse(this.formData.formContent)\n            this.formItems = content.formItems || []\n          } catch (e) {\n            console.warn('表单内容解析失败:', e)\n          }\n        }\n      }\n    },\n\n    handleDragStart(event, component) {\n      event.dataTransfer.setData('component', JSON.stringify(component))\n    },\n\n    handleDragOver(event) {\n      event.preventDefault()\n    },\n\n    handleDrop(event) {\n      event.preventDefault()\n      const componentData = event.dataTransfer.getData('component')\n      if (componentData) {\n        const component = JSON.parse(componentData)\n        this.addFormItem(component)\n      }\n    },\n\n    addFormItem(component) {\n      const newItem = {\n        id: generateId(),\n        type: component.type,\n        label: component.label,\n        icon: component.icon,\n        ...component.defaultProps\n      }\n      this.formItems.push(newItem)\n      this.selectedItem = newItem\n    },\n\n    handleSelectItem(item) {\n      this.selectedItem = item\n    },\n\n    handleDeleteItem(index) {\n      this.formItems.splice(index, 1)\n      if (this.selectedItem && this.selectedItem.id === this.formItems[index]?.id) {\n        this.selectedItem = null\n      }\n    },\n\n    handleCloneItem(item) {\n      const clonedItem = {\n        ...JSON.parse(JSON.stringify(item)),\n        id: generateId()\n      }\n      const index = this.formItems.findIndex(i => i.id === item.id)\n      this.formItems.splice(index + 1, 0, clonedItem)\n    },\n\n    handleUpdateProperties(properties) {\n      if (this.selectedItem) {\n        Object.assign(this.selectedItem, properties)\n      }\n    },\n\n    handleSortEnd() {\n      // 拖拽排序完成后的处理\n    },\n\n    clearSelection() {\n      this.selectedItem = null\n    },\n\n    handleSave() {\n      if (!this.formConfig.formName) {\n        this.$message.error('请输入表单名称')\n        return\n      }\n\n      const formData = {\n        ...this.formData,\n        formName: this.formConfig.formName,\n        remark: this.formConfig.remark,\n        formContent: JSON.stringify({\n          formItems: this.formItems,\n          config: this.formConfig\n        })\n      }\n\n      this.$emit('save', formData)\n    },\n\n    handleCancel() {\n      this.$emit('cancel')\n    },\n\n    handlePreview() {\n      this.previewVisible = true\n    },\n\n    handleExportJson() {\n      const exportData = {\n        formItems: this.formItems,\n        config: this.formConfig\n      }\n      \n      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })\n      const url = URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = `${this.formConfig.formName || 'form'}.json`\n      a.click()\n      URL.revokeObjectURL(url)\n    },\n\n    handleImportJson() {\n      this.importVisible = true\n      this.importJson = ''\n    },\n\n    handleImportConfirm() {\n      try {\n        const data = JSON.parse(this.importJson)\n        if (data.formItems) {\n          this.formItems = data.formItems\n        }\n        if (data.config) {\n          this.formConfig = { ...this.formConfig, ...data.config }\n        }\n        this.importVisible = false\n        this.$message.success('导入成功')\n      } catch (e) {\n        this.$message.error('JSON格式错误，请检查后重试')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-designer {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n\n  .designer-header {\n    background: #fff;\n    padding: 10px 20px;\n    border-bottom: 1px solid #e4e7ed;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  }\n\n  .designer-body {\n    flex: 1;\n    display: flex;\n    height: calc(100vh - 80px);\n    overflow: hidden;\n\n    .components-panel {\n      width: 260px;\n      background: #fff;\n      border-right: 1px solid #e4e7ed;\n      overflow-y: auto;\n\n      .panel-title {\n        padding: 15px;\n        font-size: 16px;\n        font-weight: bold;\n        color: #303133;\n        border-bottom: 1px solid #e4e7ed;\n        background: #fafafa;\n      }\n\n      .component-groups {\n        .component-group {\n          .group-title {\n            padding: 10px 15px;\n            font-size: 14px;\n            font-weight: 600;\n            color: #606266;\n            background: #f8f9fa;\n            border-bottom: 1px solid #e4e7ed;\n          }\n\n          .component-list {\n            padding: 10px;\n\n            .component-item {\n              display: flex;\n              align-items: center;\n              padding: 8px 12px;\n              margin-bottom: 8px;\n              background: #fff;\n              border: 1px solid #e4e7ed;\n              border-radius: 4px;\n              cursor: grab;\n              transition: all 0.3s;\n\n              &:hover {\n                border-color: #409EFF;\n                background: #f0f9ff;\n                transform: translateY(-1px);\n                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n              }\n\n              &:active {\n                cursor: grabbing;\n              }\n\n              i {\n                margin-right: 8px;\n                font-size: 16px;\n                color: #409EFF;\n              }\n\n              span {\n                font-size: 13px;\n                color: #606266;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .design-panel {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      background: #fff;\n      margin: 0 1px;\n\n      .panel-title {\n        padding: 15px;\n        font-size: 16px;\n        font-weight: bold;\n        color: #303133;\n        border-bottom: 1px solid #e4e7ed;\n        background: #fafafa;\n      }\n\n      .design-canvas {\n        flex: 1;\n        padding: 20px;\n        overflow-y: auto;\n        min-height: 400px;\n        position: relative;\n\n        .empty-canvas {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          height: 300px;\n          border: 2px dashed #c0c4cc;\n          border-radius: 8px;\n          color: #909399;\n          background: #fafbfc;\n\n          i {\n            font-size: 48px;\n            margin-bottom: 16px;\n            color: #c0c4cc;\n          }\n\n          p {\n            font-size: 14px;\n            margin: 0;\n          }\n        }\n\n        .ghost {\n          opacity: 0.5;\n          background: #409EFF;\n        }\n\n        .chosen {\n          border: 2px solid #409EFF !important;\n        }\n      }\n    }\n\n    .properties-panel {\n      width: 320px;\n      background: #fff;\n      border-left: 1px solid #e4e7ed;\n      display: flex;\n      flex-direction: column;\n\n      .panel-title {\n        padding: 15px;\n        font-size: 16px;\n        font-weight: bold;\n        color: #303133;\n        border-bottom: 1px solid #e4e7ed;\n        background: #fafafa;\n      }\n\n      .properties-content {\n        flex: 1;\n        overflow-y: auto;\n\n        .no-selection {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          height: 200px;\n          color: #909399;\n\n          i {\n            font-size: 48px;\n            margin-bottom: 16px;\n            color: #c0c4cc;\n          }\n\n          p {\n            font-size: 14px;\n            margin: 0;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 全局样式\n:deep(.el-tabs--border-card) {\n  border: none;\n  box-shadow: none;\n\n  .el-tabs__header {\n    background: #fafafa;\n    border-bottom: 1px solid #e4e7ed;\n    margin: 0;\n  }\n\n  .el-tabs__content {\n    padding: 15px;\n  }\n}\n\n:deep(.el-form-item) {\n  margin-bottom: 15px;\n}\n\n:deep(.el-input), :deep(.el-select), :deep(.el-date-editor) {\n  width: 100%;\n}\n</style>\n"]}]}