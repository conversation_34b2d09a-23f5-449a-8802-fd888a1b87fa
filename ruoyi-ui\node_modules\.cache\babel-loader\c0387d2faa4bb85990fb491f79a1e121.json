{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\utils\\index.js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\utils\\index.js", "mtime": 1752386387422}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1752199756045}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["generateId", "Math", "random", "toString", "substr", "Date", "now", "deepClone", "obj", "_typeof2", "default", "getTime", "Array", "map", "item", "clonedObj", "key", "hasOwnProperty", "validateFormItem", "type", "valid", "message", "label", "generateRules", "rules", "required", "push", "concat", "trigger", "getValidationTrigger", "isArray", "apply", "_toConsumableArray2", "triggerMap", "formatComponentProps", "props", "_objectSpread2", "id", "icon", "span", "labelWidth", "generateFormModel", "formItems", "model", "for<PERSON>ach", "activeValue", "exportFormConfig", "formConfig", "version", "importFormConfig", "config", "Error", "index", "validation", "getComponentDefaultValue", "defaultValues", "isLayoutComponent", "layoutTypes", "includes", "needsValidation"], "sources": ["D:/RuoYi-flowable/ruoyi-ui/src/components/FormDesigner/utils/index.js"], "sourcesContent": ["/**\n * 生成唯一ID\n */\nexport function generateId() {\n  return 'form_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36)\n}\n\n/**\n * 深拷贝对象\n */\nexport function deepClone(obj) {\n  if (obj === null || typeof obj !== 'object') return obj\n  if (obj instanceof Date) return new Date(obj.getTime())\n  if (obj instanceof Array) return obj.map(item => deepClone(item))\n  if (typeof obj === 'object') {\n    const clonedObj = {}\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n}\n\n/**\n * 验证表单项配置\n */\nexport function validateFormItem(item) {\n  if (!item.type) {\n    return { valid: false, message: '组件类型不能为空' }\n  }\n  if (!item.label) {\n    return { valid: false, message: '组件标签不能为空' }\n  }\n  return { valid: true }\n}\n\n/**\n * 生成表单验证规则\n */\nexport function generateRules(item) {\n  const rules = []\n  \n  if (item.required) {\n    rules.push({\n      required: true,\n      message: `请输入${item.label}`,\n      trigger: getValidationTrigger(item.type)\n    })\n  }\n  \n  // 添加自定义规则\n  if (item.rules && Array.isArray(item.rules)) {\n    rules.push(...item.rules)\n  }\n  \n  return rules\n}\n\n/**\n * 获取验证触发方式\n */\nfunction getValidationTrigger(type) {\n  const triggerMap = {\n    'input': 'blur',\n    'textarea': 'blur',\n    'number': 'blur',\n    'password': 'blur',\n    'select': 'change',\n    'radio': 'change',\n    'checkbox': 'change',\n    'switch': 'change',\n    'slider': 'change',\n    'date': 'change',\n    'time': 'change',\n    'rate': 'change',\n    'cascader': 'change',\n    'color': 'change'\n  }\n  return triggerMap[type] || 'blur'\n}\n\n/**\n * 格式化组件属性\n */\nexport function formatComponentProps(item) {\n  const props = { ...item }\n  \n  // 移除不需要的属性\n  delete props.id\n  delete props.type\n  delete props.label\n  delete props.icon\n  delete props.span\n  delete props.labelWidth\n  delete props.required\n  delete props.rules\n  \n  return props\n}\n\n/**\n * 生成表单数据模型\n */\nexport function generateFormModel(formItems) {\n  const model = {}\n  \n  formItems.forEach(item => {\n    if (item.type !== 'divider' && item.type !== 'text' && item.type !== 'html' && item.type !== 'button' && item.type !== 'alert') {\n      // 根据组件类型设置默认值\n      switch (item.type) {\n        case 'checkbox':\n          model[item.id] = []\n          break\n        case 'switch':\n          model[item.id] = item.activeValue || false\n          break\n        case 'number':\n        case 'slider':\n          model[item.id] = 0\n          break\n        case 'rate':\n          model[item.id] = 0\n          break\n        default:\n          model[item.id] = ''\n      }\n    }\n  })\n  \n  return model\n}\n\n/**\n * 导出表单配置为JSON\n */\nexport function exportFormConfig(formItems, formConfig) {\n  return {\n    version: '1.0.0',\n    formConfig,\n    formItems: formItems.map(item => ({\n      ...item,\n      // 确保每个组件都有必要的属性\n      id: item.id || generateId(),\n      type: item.type,\n      label: item.label || '',\n      span: item.span || 24,\n      labelWidth: item.labelWidth || '100px'\n    }))\n  }\n}\n\n/**\n * 导入表单配置\n */\nexport function importFormConfig(config) {\n  if (!config || typeof config !== 'object') {\n    throw new Error('配置格式错误')\n  }\n  \n  if (!config.formItems || !Array.isArray(config.formItems)) {\n    throw new Error('表单项配置错误')\n  }\n  \n  // 验证每个表单项\n  config.formItems.forEach((item, index) => {\n    const validation = validateFormItem(item)\n    if (!validation.valid) {\n      throw new Error(`第${index + 1}个组件配置错误: ${validation.message}`)\n    }\n  })\n  \n  return {\n    formConfig: config.formConfig || {},\n    formItems: config.formItems.map(item => ({\n      ...item,\n      id: item.id || generateId()\n    }))\n  }\n}\n\n/**\n * 获取组件的默认值\n */\nexport function getComponentDefaultValue(type) {\n  const defaultValues = {\n    'input': '',\n    'textarea': '',\n    'number': 0,\n    'password': '',\n    'select': '',\n    'radio': '',\n    'checkbox': [],\n    'switch': false,\n    'slider': 0,\n    'date': '',\n    'time': '',\n    'rate': 0,\n    'upload': [],\n    'cascader': [],\n    'color': ''\n  }\n  \n  return defaultValues[type] || ''\n}\n\n/**\n * 检查组件是否为布局组件\n */\nexport function isLayoutComponent(type) {\n  const layoutTypes = ['divider', 'text', 'html', 'button', 'alert']\n  return layoutTypes.includes(type)\n}\n\n/**\n * 检查组件是否需要表单验证\n */\nexport function needsValidation(type) {\n  return !isLayoutComponent(type)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACO,SAASA,UAAUA,CAAA,EAAG;EAC3B,OAAO,OAAO,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACH,QAAQ,CAAC,EAAE,CAAC;AACpF;;AAEA;AACA;AACA;AACO,SAASI,SAASA,CAACC,GAAG,EAAE;EAC7B,IAAIA,GAAG,KAAK,IAAI,IAAI,IAAAC,QAAA,CAAAC,OAAA,EAAOF,GAAG,MAAK,QAAQ,EAAE,OAAOA,GAAG;EACvD,IAAIA,GAAG,YAAYH,IAAI,EAAE,OAAO,IAAIA,IAAI,CAACG,GAAG,CAACG,OAAO,CAAC,CAAC,CAAC;EACvD,IAAIH,GAAG,YAAYI,KAAK,EAAE,OAAOJ,GAAG,CAACK,GAAG,CAAC,UAAAC,IAAI;IAAA,OAAIP,SAAS,CAACO,IAAI,CAAC;EAAA,EAAC;EACjE,IAAI,IAAAL,QAAA,CAAAC,OAAA,EAAOF,GAAG,MAAK,QAAQ,EAAE;IAC3B,IAAMO,SAAS,GAAG,CAAC,CAAC;IACpB,KAAK,IAAMC,GAAG,IAAIR,GAAG,EAAE;MACrB,IAAIA,GAAG,CAACS,cAAc,CAACD,GAAG,CAAC,EAAE;QAC3BD,SAAS,CAACC,GAAG,CAAC,GAAGT,SAAS,CAACC,GAAG,CAACQ,GAAG,CAAC,CAAC;MACtC;IACF;IACA,OAAOD,SAAS;EAClB;AACF;;AAEA;AACA;AACA;AACO,SAASG,gBAAgBA,CAACJ,IAAI,EAAE;EACrC,IAAI,CAACA,IAAI,CAACK,IAAI,EAAE;IACd,OAAO;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAW,CAAC;EAC9C;EACA,IAAI,CAACP,IAAI,CAACQ,KAAK,EAAE;IACf,OAAO;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAW,CAAC;EAC9C;EACA,OAAO;IAAED,KAAK,EAAE;EAAK,CAAC;AACxB;;AAEA;AACA;AACA;AACO,SAASG,aAAaA,CAACT,IAAI,EAAE;EAClC,IAAMU,KAAK,GAAG,EAAE;EAEhB,IAAIV,IAAI,CAACW,QAAQ,EAAE;IACjBD,KAAK,CAACE,IAAI,CAAC;MACTD,QAAQ,EAAE,IAAI;MACdJ,OAAO,uBAAAM,MAAA,CAAQb,IAAI,CAACQ,KAAK,CAAE;MAC3BM,OAAO,EAAEC,oBAAoB,CAACf,IAAI,CAACK,IAAI;IACzC,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIL,IAAI,CAACU,KAAK,IAAIZ,KAAK,CAACkB,OAAO,CAAChB,IAAI,CAACU,KAAK,CAAC,EAAE;IAC3CA,KAAK,CAACE,IAAI,CAAAK,KAAA,CAAVP,KAAK,MAAAQ,mBAAA,CAAAtB,OAAA,EAASI,IAAI,CAACU,KAAK,EAAC;EAC3B;EAEA,OAAOA,KAAK;AACd;;AAEA;AACA;AACA;AACA,SAASK,oBAAoBA,CAACV,IAAI,EAAE;EAClC,IAAMc,UAAU,GAAG;IACjB,OAAO,EAAE,MAAM;IACf,UAAU,EAAE,MAAM;IAClB,QAAQ,EAAE,MAAM;IAChB,UAAU,EAAE,MAAM;IAClB,QAAQ,EAAE,QAAQ;IAClB,OAAO,EAAE,QAAQ;IACjB,UAAU,EAAE,QAAQ;IACpB,QAAQ,EAAE,QAAQ;IAClB,QAAQ,EAAE,QAAQ;IAClB,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;IAChB,UAAU,EAAE,QAAQ;IACpB,OAAO,EAAE;EACX,CAAC;EACD,OAAOA,UAAU,CAACd,IAAI,CAAC,IAAI,MAAM;AACnC;;AAEA;AACA;AACA;AACO,SAASe,oBAAoBA,CAACpB,IAAI,EAAE;EACzC,IAAMqB,KAAK,OAAAC,cAAA,CAAA1B,OAAA,MAAQI,IAAI,CAAE;;EAEzB;EACA,OAAOqB,KAAK,CAACE,EAAE;EACf,OAAOF,KAAK,CAAChB,IAAI;EACjB,OAAOgB,KAAK,CAACb,KAAK;EAClB,OAAOa,KAAK,CAACG,IAAI;EACjB,OAAOH,KAAK,CAACI,IAAI;EACjB,OAAOJ,KAAK,CAACK,UAAU;EACvB,OAAOL,KAAK,CAACV,QAAQ;EACrB,OAAOU,KAAK,CAACX,KAAK;EAElB,OAAOW,KAAK;AACd;;AAEA;AACA;AACA;AACO,SAASM,iBAAiBA,CAACC,SAAS,EAAE;EAC3C,IAAMC,KAAK,GAAG,CAAC,CAAC;EAEhBD,SAAS,CAACE,OAAO,CAAC,UAAA9B,IAAI,EAAI;IACxB,IAAIA,IAAI,CAACK,IAAI,KAAK,SAAS,IAAIL,IAAI,CAACK,IAAI,KAAK,MAAM,IAAIL,IAAI,CAACK,IAAI,KAAK,MAAM,IAAIL,IAAI,CAACK,IAAI,KAAK,QAAQ,IAAIL,IAAI,CAACK,IAAI,KAAK,OAAO,EAAE;MAC9H;MACA,QAAQL,IAAI,CAACK,IAAI;QACf,KAAK,UAAU;UACbwB,KAAK,CAAC7B,IAAI,CAACuB,EAAE,CAAC,GAAG,EAAE;UACnB;QACF,KAAK,QAAQ;UACXM,KAAK,CAAC7B,IAAI,CAACuB,EAAE,CAAC,GAAGvB,IAAI,CAAC+B,WAAW,IAAI,KAAK;UAC1C;QACF,KAAK,QAAQ;QACb,KAAK,QAAQ;UACXF,KAAK,CAAC7B,IAAI,CAACuB,EAAE,CAAC,GAAG,CAAC;UAClB;QACF,KAAK,MAAM;UACTM,KAAK,CAAC7B,IAAI,CAACuB,EAAE,CAAC,GAAG,CAAC;UAClB;QACF;UACEM,KAAK,CAAC7B,IAAI,CAACuB,EAAE,CAAC,GAAG,EAAE;MACvB;IACF;EACF,CAAC,CAAC;EAEF,OAAOM,KAAK;AACd;;AAEA;AACA;AACA;AACO,SAASG,gBAAgBA,CAACJ,SAAS,EAAEK,UAAU,EAAE;EACtD,OAAO;IACLC,OAAO,EAAE,OAAO;IAChBD,UAAU,EAAVA,UAAU;IACVL,SAAS,EAAEA,SAAS,CAAC7B,GAAG,CAAC,UAAAC,IAAI;MAAA,WAAAsB,cAAA,CAAA1B,OAAA,MAAA0B,cAAA,CAAA1B,OAAA,MACxBI,IAAI;QACP;QACAuB,EAAE,EAAEvB,IAAI,CAACuB,EAAE,IAAIrC,UAAU,CAAC,CAAC;QAC3BmB,IAAI,EAAEL,IAAI,CAACK,IAAI;QACfG,KAAK,EAAER,IAAI,CAACQ,KAAK,IAAI,EAAE;QACvBiB,IAAI,EAAEzB,IAAI,CAACyB,IAAI,IAAI,EAAE;QACrBC,UAAU,EAAE1B,IAAI,CAAC0B,UAAU,IAAI;MAAO;IAAA,CACtC;EACJ,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAASS,gBAAgBA,CAACC,MAAM,EAAE;EACvC,IAAI,CAACA,MAAM,IAAI,IAAAzC,QAAA,CAAAC,OAAA,EAAOwC,MAAM,MAAK,QAAQ,EAAE;IACzC,MAAM,IAAIC,KAAK,CAAC,QAAQ,CAAC;EAC3B;EAEA,IAAI,CAACD,MAAM,CAACR,SAAS,IAAI,CAAC9B,KAAK,CAACkB,OAAO,CAACoB,MAAM,CAACR,SAAS,CAAC,EAAE;IACzD,MAAM,IAAIS,KAAK,CAAC,SAAS,CAAC;EAC5B;;EAEA;EACAD,MAAM,CAACR,SAAS,CAACE,OAAO,CAAC,UAAC9B,IAAI,EAAEsC,KAAK,EAAK;IACxC,IAAMC,UAAU,GAAGnC,gBAAgB,CAACJ,IAAI,CAAC;IACzC,IAAI,CAACuC,UAAU,CAACjC,KAAK,EAAE;MACrB,MAAM,IAAI+B,KAAK,UAAAxB,MAAA,CAAKyB,KAAK,GAAG,CAAC,kDAAAzB,MAAA,CAAY0B,UAAU,CAAChC,OAAO,CAAE,CAAC;IAChE;EACF,CAAC,CAAC;EAEF,OAAO;IACL0B,UAAU,EAAEG,MAAM,CAACH,UAAU,IAAI,CAAC,CAAC;IACnCL,SAAS,EAAEQ,MAAM,CAACR,SAAS,CAAC7B,GAAG,CAAC,UAAAC,IAAI;MAAA,WAAAsB,cAAA,CAAA1B,OAAA,MAAA0B,cAAA,CAAA1B,OAAA,MAC/BI,IAAI;QACPuB,EAAE,EAAEvB,IAAI,CAACuB,EAAE,IAAIrC,UAAU,CAAC;MAAC;IAAA,CAC3B;EACJ,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAASsD,wBAAwBA,CAACnC,IAAI,EAAE;EAC7C,IAAMoC,aAAa,GAAG;IACpB,OAAO,EAAE,EAAE;IACX,UAAU,EAAE,EAAE;IACd,QAAQ,EAAE,CAAC;IACX,UAAU,EAAE,EAAE;IACd,QAAQ,EAAE,EAAE;IACZ,OAAO,EAAE,EAAE;IACX,UAAU,EAAE,EAAE;IACd,QAAQ,EAAE,KAAK;IACf,QAAQ,EAAE,CAAC;IACX,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,CAAC;IACT,QAAQ,EAAE,EAAE;IACZ,UAAU,EAAE,EAAE;IACd,OAAO,EAAE;EACX,CAAC;EAED,OAAOA,aAAa,CAACpC,IAAI,CAAC,IAAI,EAAE;AAClC;;AAEA;AACA;AACA;AACO,SAASqC,iBAAiBA,CAACrC,IAAI,EAAE;EACtC,IAAMsC,WAAW,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;EAClE,OAAOA,WAAW,CAACC,QAAQ,CAACvC,IAAI,CAAC;AACnC;;AAEA;AACA;AACA;AACO,SAASwC,eAAeA,CAACxC,IAAI,EAAE;EACpC,OAAO,CAACqC,iBAAiB,CAACrC,IAAI,CAAC;AACjC", "ignoreList": []}]}