{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FieldRenderer\\index.vue?vue&type=style&index=0&id=254273c0&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FieldRenderer\\index.vue", "mtime": 1752412497993}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5maWVsZC1yZW5kZXJlciB7CiAgd2lkdGg6IDEwMCU7CiAgCiAgLmRlZmF1bHQtdGV4dCB7CiAgICBjb2xvcjogIzYwNjI2NjsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGxpbmUtaGVpZ2h0OiAzMnB4OwogIH0KICAKICAvLyDnpoHnlKjnirbmgIHkuIvnmoTmoLflvI/osIPmlbQKICA6ZGVlcCguZWwtaW5wdXQuaXMtZGlzYWJsZWQgLmVsLWlucHV0X19pbm5lcikgewogICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsKICAgIGJvcmRlci1jb2xvcjogI2U0ZTdlZDsKICAgIGNvbG9yOiAjNjA2MjY2OwogIH0KICAKICA6ZGVlcCguZWwtdGV4dGFyZWEuaXMtZGlzYWJsZWQgLmVsLXRleHRhcmVhX19pbm5lcikgewogICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsKICAgIGJvcmRlci1jb2xvcjogI2U0ZTdlZDsKICAgIGNvbG9yOiAjNjA2MjY2OwogIH0KICAKICA6ZGVlcCguZWwtc2VsZWN0LmlzLWRpc2FibGVkIC5lbC1pbnB1dF9faW5uZXIpIHsKICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgICBib3JkZXItY29sb3I6ICNlNGU3ZWQ7CiAgICBjb2xvcjogIzYwNjI2NjsKICB9CiAgCiAgOmRlZXAoLmVsLWRhdGUtZWRpdG9yLmlzLWRpc2FibGVkIC5lbC1pbnB1dF9faW5uZXIpIHsKICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgICBib3JkZXItY29sb3I6ICNlNGU3ZWQ7CiAgICBjb2xvcjogIzYwNjI2NjsKICB9CiAgCiAgOmRlZXAoLmVsLXN3aXRjaC5pcy1kaXNhYmxlZCkgewogICAgb3BhY2l0eTogMC42OwogIH0KICAKICA6ZGVlcCguZWwtcmFkaW8uaXMtZGlzYWJsZWQpIHsKICAgIGNvbG9yOiAjYzBjNGNjOwogIH0KICAKICA6ZGVlcCguZWwtY2hlY2tib3guaXMtZGlzYWJsZWQpIHsKICAgIGNvbG9yOiAjYzBjNGNjOwogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8IA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FieldRenderer", "sourcesContent": ["<template>\n  <div class=\"field-renderer\">\n    <!-- 文本输入 -->\n    <el-input\n      v-if=\"field.type === 'input'\"\n      :value=\"value\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      readonly\n    />\n\n    <!-- 多行文本 -->\n    <el-input\n      v-else-if=\"field.type === 'textarea'\"\n      :value=\"value\"\n      type=\"textarea\"\n      :rows=\"getFieldOption('rows') || 3\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      readonly\n    />\n\n    <!-- 数字输入 -->\n    <el-input-number\n      v-else-if=\"field.type === 'number'\"\n      :value=\"value\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      style=\"width: 100%\"\n      :controls=\"false\"\n    />\n\n    <!-- 选择器 -->\n    <el-select\n      v-else-if=\"field.type === 'select'\"\n      :value=\"value\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      style=\"width: 100%\"\n    >\n      <el-option\n        v-for=\"option in getFieldOption('optionItems') || []\"\n        :key=\"option.value\"\n        :label=\"option.label\"\n        :value=\"option.value\"\n      />\n    </el-select>\n\n    <!-- 日期选择 -->\n    <el-date-picker\n      v-else-if=\"field.type === 'date'\"\n      :value=\"value\"\n      type=\"date\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      style=\"width: 100%\"\n    />\n\n    <!-- 开关 -->\n    <el-switch\n      v-else-if=\"field.type === 'switch'\"\n      :value=\"value\"\n      :disabled=\"disabled\"\n    />\n\n    <!-- 单选框组 -->\n    <el-radio-group\n      v-else-if=\"field.type === 'radio'\"\n      :value=\"value\"\n      :disabled=\"disabled\"\n    >\n      <el-radio\n        v-for=\"option in getFieldOption('optionItems') || []\"\n        :key=\"option.value\"\n        :label=\"option.value\"\n      >\n        {{ option.label }}\n      </el-radio>\n    </el-radio-group>\n\n    <!-- 复选框组 -->\n    <el-checkbox-group\n      v-else-if=\"field.type === 'checkbox'\"\n      :value=\"value || []\"\n      :disabled=\"disabled\"\n    >\n      <el-checkbox\n        v-for=\"option in getFieldOption('optionItems') || []\"\n        :key=\"option.value\"\n        :label=\"option.value\"\n      >\n        {{ option.label }}\n      </el-checkbox>\n    </el-checkbox-group>\n    \n    <!-- 默认文本显示 -->\n    <span v-else class=\"default-text\">{{ formatValue(value) }}</span>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FieldRenderer',\n  props: {\n    value: {\n      type: [String, Number, Boolean, Array, Object],\n      default: null\n    },\n    field: {\n      type: Object,\n      required: true\n    },\n    disabled: {\n      type: Boolean,\n      default: true\n    }\n  },\n  methods: {\n    formatValue(value) {\n      if (value === null || value === undefined) {\n        return '';\n      }\n      if (Array.isArray(value)) {\n        return value.join(', ');\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      if (typeof value === 'boolean') {\n        return value ? '是' : '否';\n      }\n      return String(value);\n    },\n\n    getFieldOption(optionName) {\n      return this.field.options && this.field.options[optionName] ? this.field.options[optionName] : null;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.field-renderer {\n  width: 100%;\n  \n  .default-text {\n    color: #606266;\n    font-size: 14px;\n    line-height: 32px;\n  }\n  \n  // 禁用状态下的样式调整\n  :deep(.el-input.is-disabled .el-input__inner) {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #606266;\n  }\n  \n  :deep(.el-textarea.is-disabled .el-textarea__inner) {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #606266;\n  }\n  \n  :deep(.el-select.is-disabled .el-input__inner) {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #606266;\n  }\n  \n  :deep(.el-date-editor.is-disabled .el-input__inner) {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #606266;\n  }\n  \n  :deep(.el-switch.is-disabled) {\n    opacity: 0.6;\n  }\n  \n  :deep(.el-radio.is-disabled) {\n    color: #c0c4cc;\n  }\n  \n  :deep(.el-checkbox.is-disabled) {\n    color: #c0c4cc;\n  }\n}\n</style>\n"]}]}