{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\npi\\formConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\npi\\formConfig\\index.vue", "mtime": 1752411081456}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9SdW9ZaS1mbG93YWJsZS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnZhciBfTm9kZVZGb3JtTWFuYWdlciA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9jb21wb25lbnRzL05vZGVWRm9ybU1hbmFnZXIiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnTlBJRm9ybUNvbmZpZycsCiAgY29tcG9uZW50czogewogICAgTm9kZVZGb3JtTWFuYWdlcjogX05vZGVWRm9ybU1hbmFnZXIuZGVmYXVsdAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG5waUZvcm1Db25maWc6IFtdLAogICAgICBzaG93UXVpY2tTZXR1cDogZmFsc2UsCiAgICAgIHNob3dIZWxwRGlhbG9nOiBmYWxzZQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmxvYWROUElGb3JtQ29uZmlnKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5Yqg6L29TlBJ6KGo5Y2V6YWN572uICovbG9hZE5QSUZvcm1Db25maWc6IGZ1bmN0aW9uIGxvYWROUElGb3JtQ29uZmlnKCkgewogICAgICB2YXIgc2F2ZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnbm9kZV92Zm9ybV9jb25maWdfbnBpX3Byb2Nlc3MnKTsKICAgICAgaWYgKHNhdmVkKSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIHZhciBjb25maWcgPSBKU09OLnBhcnNlKHNhdmVkKTsKICAgICAgICAgIHRoaXMubnBpRm9ybUNvbmZpZyA9IGNvbmZpZy5ub2RlRm9ybXMgfHwgW107CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUud2Fybign5Yqg6L29TlBJ6KGo5Y2V6YWN572u5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvKiog5aSE55CG5L+d5a2YICovaGFuZGxlU2F2ZTogZnVuY3Rpb24gaGFuZGxlU2F2ZShjb25maWcpIHsKICAgICAgLy8g6L+Z6YeM5Y+v5Lul6LCD55SoQVBJ5L+d5a2Y5Yiw5ZCO56uvCiAgICAgIGNvbnNvbGUubG9nKCfkv53lrZhOUEnooajljZXphY3nva46JywgY29uZmlnKTsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCdOUEnooajljZXphY3nva7kv53lrZjmiJDlip8nKTsKICAgIH0sCiAgICAvKiog5b+r6YCf6YWN572uICovcXVpY2tTZXR1cDogZnVuY3Rpb24gcXVpY2tTZXR1cCgpIHsKICAgICAgdGhpcy5zaG93UXVpY2tTZXR1cCA9IHRydWU7CiAgICB9LAogICAgLyoqIOaJp+ihjOW/q+mAn+mFjee9riAqL2V4ZWN1dGVRdWlja1NldHVwOiBmdW5jdGlvbiBleGVjdXRlUXVpY2tTZXR1cCgpIHsKICAgICAgdmFyIHF1aWNrQ29uZmlnID0gW3sKICAgICAgICBpZDogJ25waV9hcHBseV9ub2RlJywKICAgICAgICBub2RlTmFtZTogJ05QSeeUs+ivtycsCiAgICAgICAgbm9kZVR5cGU6ICducGlfYXBwbHknLAogICAgICAgIG5vZGVLZXk6ICducGlfYXBwbHlfdGFzaycsCiAgICAgICAgZm9ybUpzb246IHRoaXMuY3JlYXRlQXBwbHlGb3JtSnNvbigpLAogICAgICAgIHByZXZpZXdLZXk6IERhdGUubm93KCkKICAgICAgfSwgewogICAgICAgIGlkOiAndGVjaF9yZXZpZXdfbm9kZScsCiAgICAgICAgbm9kZU5hbWU6ICfmioDmnK/or4TlrqEnLAogICAgICAgIG5vZGVUeXBlOiAndGVjaF9yZXZpZXcnLAogICAgICAgIG5vZGVLZXk6ICd0ZWNoX3Jldmlld190YXNrJywKICAgICAgICBmb3JtSnNvbjogdGhpcy5jcmVhdGVUZWNoUmV2aWV3Rm9ybUpzb24oKSwKICAgICAgICBwcmV2aWV3S2V5OiBEYXRlLm5vdygpCiAgICAgIH0sIHsKICAgICAgICBpZDogJ3Byb2Nlc3NfcmV2aWV3X25vZGUnLAogICAgICAgIG5vZGVOYW1lOiAn5bel6Im66K+E5a6hJywKICAgICAgICBub2RlVHlwZTogJ3Byb2Nlc3NfcmV2aWV3JywKICAgICAgICBub2RlS2V5OiAncHJvY2Vzc19yZXZpZXdfdGFzaycsCiAgICAgICAgZm9ybUpzb246IHRoaXMuY3JlYXRlUHJvY2Vzc1Jldmlld0Zvcm1Kc29uKCksCiAgICAgICAgcHJldmlld0tleTogRGF0ZS5ub3coKQogICAgICB9LCB7CiAgICAgICAgaWQ6ICdxdWFsaXR5X3Jldmlld19ub2RlJywKICAgICAgICBub2RlTmFtZTogJ+i0qOmHj+ivhOWuoScsCiAgICAgICAgbm9kZVR5cGU6ICdxdWFsaXR5X3JldmlldycsCiAgICAgICAgbm9kZUtleTogJ3F1YWxpdHlfcmV2aWV3X3Rhc2snLAogICAgICAgIGZvcm1Kc29uOiB0aGlzLmNyZWF0ZVF1YWxpdHlSZXZpZXdGb3JtSnNvbigpLAogICAgICAgIHByZXZpZXdLZXk6IERhdGUubm93KCkKICAgICAgfSwgewogICAgICAgIGlkOiAnY29zdF9yZXZpZXdfbm9kZScsCiAgICAgICAgbm9kZU5hbWU6ICfmiJDmnKzor4TlrqEnLAogICAgICAgIG5vZGVUeXBlOiAnY29zdF9yZXZpZXcnLAogICAgICAgIG5vZGVLZXk6ICdjb3N0X3Jldmlld190YXNrJywKICAgICAgICBmb3JtSnNvbjogdGhpcy5jcmVhdGVDb3N0UmV2aWV3Rm9ybUpzb24oKSwKICAgICAgICBwcmV2aWV3S2V5OiBEYXRlLm5vdygpCiAgICAgIH0sIHsKICAgICAgICBpZDogJ2ZpbmFsX2FwcHJvdmFsX25vZGUnLAogICAgICAgIG5vZGVOYW1lOiAn5pyA57uI5a6h5om5JywKICAgICAgICBub2RlVHlwZTogJ2ZpbmFsX2FwcHJvdmFsJywKICAgICAgICBub2RlS2V5OiAnZmluYWxfYXBwcm92YWxfdGFzaycsCiAgICAgICAgZm9ybUpzb246IHRoaXMuY3JlYXRlRmluYWxBcHByb3ZhbEZvcm1Kc29uKCksCiAgICAgICAgcHJldmlld0tleTogRGF0ZS5ub3coKQogICAgICB9XTsKICAgICAgdGhpcy5ucGlGb3JtQ29uZmlnID0gcXVpY2tDb25maWc7CiAgICAgIHRoaXMuc2hvd1F1aWNrU2V0dXAgPSBmYWxzZTsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflv6vpgJ/phY3nva7lrozmiJDvvIEnKTsKICAgIH0sCiAgICAvKiog5Yib5bu655Sz6K+36KGo5Y2VSlNPTiAqL2NyZWF0ZUFwcGx5Rm9ybUpzb246IGZ1bmN0aW9uIGNyZWF0ZUFwcGx5Rm9ybUpzb24oKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgd2lkZ2V0TGlzdDogW3sKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgIG5hbWU6ICdwcm9kdWN0TmFtZScsCiAgICAgICAgICAgIGxhYmVsOiAn5Lqn5ZOB5ZCN56ewJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5Lqn5ZOB5ZCN56ewJwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgIG5hbWU6ICdwcm9kdWN0Q29kZScsCiAgICAgICAgICAgIGxhYmVsOiAn5Lqn5ZOB57yW56CBJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5Lqn5ZOB57yW56CBJwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLAogICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICBuYW1lOiAncHJvZHVjdENhdGVnb3J5JywKICAgICAgICAgICAgbGFiZWw6ICfkuqflk4HnsbvliKsnLAogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgb3B0aW9uSXRlbXM6IFt7CiAgICAgICAgICAgICAgbGFiZWw6ICfnoazku7bkuqflk4EnLAogICAgICAgICAgICAgIHZhbHVlOiAnaGFyZHdhcmUnCiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBsYWJlbDogJ+i9r+S7tuS6p+WTgScsCiAgICAgICAgICAgICAgdmFsdWU6ICdzb2Z0d2FyZScKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAn5pyN5Yqh5Lqn5ZOBJywKICAgICAgICAgICAgICB2YWx1ZTogJ3NlcnZpY2UnCiAgICAgICAgICAgIH1dCiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdHlwZTogJ3RleHRhcmVhJywKICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgbmFtZTogJ3Byb2R1Y3REZXNjcmlwdGlvbicsCiAgICAgICAgICAgIGxhYmVsOiAn5Lqn5ZOB5o+P6L+wJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHJvd3M6IDQsCiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36K+m57uG5o+P6L+w5Lqn5ZOB5Yqf6IO95ZKM54m55oCnJwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHR5cGU6ICdkYXRlJywKICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgbmFtZTogJ2V4cGVjdGVkTGF1bmNoRGF0ZScsCiAgICAgICAgICAgIGxhYmVsOiAn6aKE5pyf5LiK5biC5pe26Ze0JywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUKICAgICAgICAgIH0KICAgICAgICB9XSwKICAgICAgICBmb3JtQ29uZmlnOiB7CiAgICAgICAgICBtb2RlbE5hbWU6ICdmb3JtRGF0YScsCiAgICAgICAgICByZWZOYW1lOiAndkZvcm0nLAogICAgICAgICAgcnVsZXNOYW1lOiAncnVsZXMnLAogICAgICAgICAgbGFiZWxXaWR0aDogMTIwLAogICAgICAgICAgbGFiZWxQb3NpdGlvbjogJ2xlZnQnLAogICAgICAgICAgc2l6ZTogJycsCiAgICAgICAgICBsYWJlbEFsaWduOiAnbGFiZWwtbGVmdC1hbGlnbicsCiAgICAgICAgICBjc3NDb2RlOiAnJywKICAgICAgICAgIGN1c3RvbUNsYXNzOiAnJywKICAgICAgICAgIGZ1bmN0aW9uczogJycsCiAgICAgICAgICBsYXlvdXRUeXBlOiAnUEMnCiAgICAgICAgfQogICAgICB9OwogICAgfSwKICAgIC8qKiDliJvlu7rmioDmnK/or4TlrqHooajljZVKU09OICovY3JlYXRlVGVjaFJldmlld0Zvcm1Kc29uOiBmdW5jdGlvbiBjcmVhdGVUZWNoUmV2aWV3Rm9ybUpzb24oKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgd2lkZ2V0TGlzdDogW3sKICAgICAgICAgIHR5cGU6ICdyYWRpbycsCiAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgIG5hbWU6ICd0ZWNoRmVhc2liaWxpdHknLAogICAgICAgICAgICBsYWJlbDogJ+aKgOacr+WPr+ihjOaApycsCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBvcHRpb25JdGVtczogW3sKICAgICAgICAgICAgICBsYWJlbDogJ+WujOWFqOWPr+ihjCcsCiAgICAgICAgICAgICAgdmFsdWU6ICdmZWFzaWJsZScKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAn6ZyA6KaB5pS56L+bJywKICAgICAgICAgICAgICB2YWx1ZTogJ25lZWRzX2ltcHJvdmVtZW50JwogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICfmioDmnK/po47pmanpq5gnLAogICAgICAgICAgICAgIHZhbHVlOiAnaGlnaF9yaXNrJwogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICfkuI3lj6/ooYwnLAogICAgICAgICAgICAgIHZhbHVlOiAnbm90X2ZlYXNpYmxlJwogICAgICAgICAgICB9XQogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHR5cGU6ICd0ZXh0YXJlYScsCiAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgIG5hbWU6ICd0ZWNoUmlza0FuYWx5c2lzJywKICAgICAgICAgICAgbGFiZWw6ICfmioDmnK/po47pmanliIbmnpAnLAogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgcm93czogNCwKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fliIbmnpDkuLvopoHmioDmnK/po47pmanlkozlupTlr7nmjqrmlr0nCiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdHlwZTogJ3RleHRhcmVhJywKICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgbmFtZTogJ3RlY2hSZWNvbW1lbmRhdGlvbicsCiAgICAgICAgICAgIGxhYmVsOiAn5oqA5pyv5bu66K6uJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHJvd3M6IDMsCiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+35o+Q5L6b5oqA5pyv5pS56L+b5bu66K6uJwogICAgICAgICAgfQogICAgICAgIH1dLAogICAgICAgIGZvcm1Db25maWc6IHsKICAgICAgICAgIG1vZGVsTmFtZTogJ2Zvcm1EYXRhJywKICAgICAgICAgIHJlZk5hbWU6ICd2Rm9ybScsCiAgICAgICAgICBydWxlc05hbWU6ICdydWxlcycsCiAgICAgICAgICBsYWJlbFdpZHRoOiAxMjAsCiAgICAgICAgICBsYWJlbFBvc2l0aW9uOiAnbGVmdCcsCiAgICAgICAgICBzaXplOiAnJywKICAgICAgICAgIGxhYmVsQWxpZ246ICdsYWJlbC1sZWZ0LWFsaWduJywKICAgICAgICAgIGNzc0NvZGU6ICcnLAogICAgICAgICAgY3VzdG9tQ2xhc3M6ICcnLAogICAgICAgICAgZnVuY3Rpb25zOiAnJywKICAgICAgICAgIGxheW91dFR5cGU6ICdQQycKICAgICAgICB9CiAgICAgIH07CiAgICB9LAogICAgLyoqIOWIm+W7uuW3peiJuuivhOWuoeihqOWNlUpTT04gKi9jcmVhdGVQcm9jZXNzUmV2aWV3Rm9ybUpzb246IGZ1bmN0aW9uIGNyZWF0ZVByb2Nlc3NSZXZpZXdGb3JtSnNvbigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICB3aWRnZXRMaXN0OiBbewogICAgICAgICAgdHlwZTogJ3JhZGlvJywKICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgbmFtZTogJ3Byb2Nlc3NDb21wbGV4aXR5JywKICAgICAgICAgICAgbGFiZWw6ICflt6XoibrlpI3mnYLluqYnLAogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgb3B0aW9uSXRlbXM6IFt7CiAgICAgICAgICAgICAgbGFiZWw6ICfnroDljZUnLAogICAgICAgICAgICAgIHZhbHVlOiAnc2ltcGxlJwogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICfkuK3nrYknLAogICAgICAgICAgICAgIHZhbHVlOiAnbWVkaXVtJwogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICflpI3mnYInLAogICAgICAgICAgICAgIHZhbHVlOiAnY29tcGxleCcKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAn5p6B5YW25aSN5p2CJywKICAgICAgICAgICAgICB2YWx1ZTogJ3ZlcnlfY29tcGxleCcKICAgICAgICAgICAgfV0KICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICB0eXBlOiAnbnVtYmVyJywKICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgbmFtZTogJ2VzdGltYXRlZENvc3QnLAogICAgICAgICAgICBsYWJlbDogJ+mihOS8sOWItumAoOaIkOacrCcsCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpemihOS8sOaIkOacrO+8iOWFg++8iScKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICB0eXBlOiAndGV4dGFyZWEnLAogICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICBuYW1lOiAncHJvY2Vzc1JlY29tbWVuZGF0aW9uJywKICAgICAgICAgICAgbGFiZWw6ICflt6Xoibrlu7rorq4nLAogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgcm93czogNCwKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fmj5Dkvpvlt6XoibrmlLnov5vlu7rorq4nCiAgICAgICAgICB9CiAgICAgICAgfV0sCiAgICAgICAgZm9ybUNvbmZpZzogewogICAgICAgICAgbW9kZWxOYW1lOiAnZm9ybURhdGEnLAogICAgICAgICAgcmVmTmFtZTogJ3ZGb3JtJywKICAgICAgICAgIHJ1bGVzTmFtZTogJ3J1bGVzJywKICAgICAgICAgIGxhYmVsV2lkdGg6IDEyMCwKICAgICAgICAgIGxhYmVsUG9zaXRpb246ICdsZWZ0JywKICAgICAgICAgIHNpemU6ICcnLAogICAgICAgICAgbGFiZWxBbGlnbjogJ2xhYmVsLWxlZnQtYWxpZ24nLAogICAgICAgICAgY3NzQ29kZTogJycsCiAgICAgICAgICBjdXN0b21DbGFzczogJycsCiAgICAgICAgICBmdW5jdGlvbnM6ICcnLAogICAgICAgICAgbGF5b3V0VHlwZTogJ1BDJwogICAgICAgIH0KICAgICAgfTsKICAgIH0sCiAgICAvKiog5Yib5bu66LSo6YeP6K+E5a6h6KGo5Y2VSlNPTiAqL2NyZWF0ZVF1YWxpdHlSZXZpZXdGb3JtSnNvbjogZnVuY3Rpb24gY3JlYXRlUXVhbGl0eVJldmlld0Zvcm1Kc29uKCkgewogICAgICByZXR1cm4gewogICAgICAgIHdpZGdldExpc3Q6IFt7CiAgICAgICAgICB0eXBlOiAnY2hlY2tib3gnLAogICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICBuYW1lOiAncXVhbGl0eVN0YW5kYXJkcycsCiAgICAgICAgICAgIGxhYmVsOiAn6LSo6YeP5qCH5YeGJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIG9wdGlvbkl0ZW1zOiBbewogICAgICAgICAgICAgIGxhYmVsOiAnSVNPIDkwMDEnLAogICAgICAgICAgICAgIHZhbHVlOiAnaXNvOTAwMScKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAnSVNPIDE0MDAxJywKICAgICAgICAgICAgICB2YWx1ZTogJ2lzbzE0MDAxJwogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICdDReiupOivgScsCiAgICAgICAgICAgICAgdmFsdWU6ICdjZScKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAnRkND6K6k6K+BJywKICAgICAgICAgICAgICB2YWx1ZTogJ2ZjYycKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAn5YW25LuWJywKICAgICAgICAgICAgICB2YWx1ZTogJ290aGVyJwogICAgICAgICAgICB9XQogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHR5cGU6ICd0ZXh0YXJlYScsCiAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgIG5hbWU6ICd0ZXN0UGxhbicsCiAgICAgICAgICAgIGxhYmVsOiAn5rWL6K+V6K6h5YiSJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHJvd3M6IDQsCiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+35o+P6L+w6K+m57uG55qE5rWL6K+V6K6h5YiSJwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHR5cGU6ICdyYWRpbycsCiAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgIG5hbWU6ICdxdWFsaXR5UmlzaycsCiAgICAgICAgICAgIGxhYmVsOiAn6LSo6YeP6aOO6Zmp6K+E5LywJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIG9wdGlvbkl0ZW1zOiBbewogICAgICAgICAgICAgIGxhYmVsOiAn5L2O6aOO6ZmpJywKICAgICAgICAgICAgICB2YWx1ZTogJ2xvdycKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAn5Lit562J6aOO6ZmpJywKICAgICAgICAgICAgICB2YWx1ZTogJ21lZGl1bScKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAn6auY6aOO6ZmpJywKICAgICAgICAgICAgICB2YWx1ZTogJ2hpZ2gnCiAgICAgICAgICAgIH1dCiAgICAgICAgICB9CiAgICAgICAgfV0sCiAgICAgICAgZm9ybUNvbmZpZzogewogICAgICAgICAgbW9kZWxOYW1lOiAnZm9ybURhdGEnLAogICAgICAgICAgcmVmTmFtZTogJ3ZGb3JtJywKICAgICAgICAgIHJ1bGVzTmFtZTogJ3J1bGVzJywKICAgICAgICAgIGxhYmVsV2lkdGg6IDEyMCwKICAgICAgICAgIGxhYmVsUG9zaXRpb246ICdsZWZ0JywKICAgICAgICAgIHNpemU6ICcnLAogICAgICAgICAgbGFiZWxBbGlnbjogJ2xhYmVsLWxlZnQtYWxpZ24nLAogICAgICAgICAgY3NzQ29kZTogJycsCiAgICAgICAgICBjdXN0b21DbGFzczogJycsCiAgICAgICAgICBmdW5jdGlvbnM6ICcnLAogICAgICAgICAgbGF5b3V0VHlwZTogJ1BDJwogICAgICAgIH0KICAgICAgfTsKICAgIH0sCiAgICAvKiog5Yib5bu65oiQ5pys6K+E5a6h6KGo5Y2VSlNPTiAqL2NyZWF0ZUNvc3RSZXZpZXdGb3JtSnNvbjogZnVuY3Rpb24gY3JlYXRlQ29zdFJldmlld0Zvcm1Kc29uKCkgewogICAgICByZXR1cm4gewogICAgICAgIHdpZGdldExpc3Q6IFt7CiAgICAgICAgICB0eXBlOiAnbnVtYmVyJywKICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgbmFtZTogJ2RldmVsb3BtZW50Q29zdCcsCiAgICAgICAgICAgIGxhYmVsOiAn5byA5Y+R5oiQ5pysJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5byA5Y+R5oiQ5pys77yI5LiH5YWD77yJJwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHR5cGU6ICdudW1iZXInLAogICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICBuYW1lOiAndW5pdENvc3QnLAogICAgICAgICAgICBsYWJlbDogJ+WNleS9jeaIkOacrCcsCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeWNleS9jeaIkOacrO+8iOWFg++8iScKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICB0eXBlOiAnbnVtYmVyJywKICAgICAgICAgIG9wdGlvbnM6IHsKICAgICAgICAgICAgbmFtZTogJ2V4cGVjdGVkUHJpY2UnLAogICAgICAgICAgICBsYWJlbDogJ+mihOacn+WUruS7tycsCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpemihOacn+WUruS7t++8iOWFg++8iScKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICB0eXBlOiAndGV4dGFyZWEnLAogICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICBuYW1lOiAnY29zdEFuYWx5c2lzJywKICAgICAgICAgICAgbGFiZWw6ICfmiJDmnKzliIbmnpAnLAogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgcm93czogNCwKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fmj5Dkvpvor6bnu4bnmoTmiJDmnKzliIbmnpAnCiAgICAgICAgICB9CiAgICAgICAgfV0sCiAgICAgICAgZm9ybUNvbmZpZzogewogICAgICAgICAgbW9kZWxOYW1lOiAnZm9ybURhdGEnLAogICAgICAgICAgcmVmTmFtZTogJ3ZGb3JtJywKICAgICAgICAgIHJ1bGVzTmFtZTogJ3J1bGVzJywKICAgICAgICAgIGxhYmVsV2lkdGg6IDEyMCwKICAgICAgICAgIGxhYmVsUG9zaXRpb246ICdsZWZ0JywKICAgICAgICAgIHNpemU6ICcnLAogICAgICAgICAgbGFiZWxBbGlnbjogJ2xhYmVsLWxlZnQtYWxpZ24nLAogICAgICAgICAgY3NzQ29kZTogJycsCiAgICAgICAgICBjdXN0b21DbGFzczogJycsCiAgICAgICAgICBmdW5jdGlvbnM6ICcnLAogICAgICAgICAgbGF5b3V0VHlwZTogJ1BDJwogICAgICAgIH0KICAgICAgfTsKICAgIH0sCiAgICAvKiog5Yib5bu65pyA57uI5a6h5om56KGo5Y2VSlNPTiAqL2NyZWF0ZUZpbmFsQXBwcm92YWxGb3JtSnNvbjogZnVuY3Rpb24gY3JlYXRlRmluYWxBcHByb3ZhbEZvcm1Kc29uKCkgewogICAgICByZXR1cm4gewogICAgICAgIHdpZGdldExpc3Q6IFt7CiAgICAgICAgICB0eXBlOiAncmFkaW8nLAogICAgICAgICAgb3B0aW9uczogewogICAgICAgICAgICBuYW1lOiAnZmluYWxEZWNpc2lvbicsCiAgICAgICAgICAgIGxhYmVsOiAn5pyA57uI5Yaz562WJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIG9wdGlvbkl0ZW1zOiBbewogICAgICAgICAgICAgIGxhYmVsOiAn5om55YeG56uL6aG5JywKICAgICAgICAgICAgICB2YWx1ZTogJ2FwcHJvdmVkJwogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICfmnInmnaHku7bmibnlh4YnLAogICAgICAgICAgICAgIHZhbHVlOiAnY29uZGl0aW9uYWwnCiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBsYWJlbDogJ+mcgOimgeS/ruaUuScsCiAgICAgICAgICAgICAgdmFsdWU6ICduZWVkc19yZXZpc2lvbicKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAn5ouS57ud56uL6aG5JywKICAgICAgICAgICAgICB2YWx1ZTogJ3JlamVjdGVkJwogICAgICAgICAgICB9XQogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHR5cGU6ICd0ZXh0YXJlYScsCiAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgIG5hbWU6ICdkZWNpc2lvblJlYXNvbicsCiAgICAgICAgICAgIGxhYmVsOiAn5Yaz562W55CG55SxJywKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHJvd3M6IDQsCiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36K+05piO5Yaz562W55CG55SxJwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHR5cGU6ICd0ZXh0YXJlYScsCiAgICAgICAgICBvcHRpb25zOiB7CiAgICAgICAgICAgIG5hbWU6ICduZXh0U3RlcHMnLAogICAgICAgICAgICBsYWJlbDogJ+WQjue7reiuoeWIkicsCiAgICAgICAgICAgIHJlcXVpcmVkOiBmYWxzZSwKICAgICAgICAgICAgcm93czogMywKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fmj4/ov7DlkI7nu63miafooYzorqHliJInCiAgICAgICAgICB9CiAgICAgICAgfV0sCiAgICAgICAgZm9ybUNvbmZpZzogewogICAgICAgICAgbW9kZWxOYW1lOiAnZm9ybURhdGEnLAogICAgICAgICAgcmVmTmFtZTogJ3ZGb3JtJywKICAgICAgICAgIHJ1bGVzTmFtZTogJ3J1bGVzJywKICAgICAgICAgIGxhYmVsV2lkdGg6IDEyMCwKICAgICAgICAgIGxhYmVsUG9zaXRpb246ICdsZWZ0JywKICAgICAgICAgIHNpemU6ICcnLAogICAgICAgICAgbGFiZWxBbGlnbjogJ2xhYmVsLWxlZnQtYWxpZ24nLAogICAgICAgICAgY3NzQ29kZTogJycsCiAgICAgICAgICBjdXN0b21DbGFzczogJycsCiAgICAgICAgICBmdW5jdGlvbnM6ICcnLAogICAgICAgICAgbGF5b3V0VHlwZTogJ1BDJwogICAgICAgIH0KICAgICAgfTsKICAgIH0sCiAgICAvKiog5pi+56S65biu5YqpICovc2hvd0hlbHA6IGZ1bmN0aW9uIHNob3dIZWxwKCkgewogICAgICB0aGlzLnNob3dIZWxwRGlhbG9nID0gdHJ1ZTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_NodeVFormManager", "_interopRequireDefault", "require", "name", "components", "NodeVFormManager", "data", "npiFormConfig", "showQuickSetup", "showHelpDialog", "created", "loadNPIFormConfig", "methods", "saved", "localStorage", "getItem", "config", "JSON", "parse", "nodeForms", "error", "console", "warn", "handleSave", "log", "$message", "success", "quickSetup", "executeQuickSetup", "quickConfig", "id", "nodeName", "nodeType", "nodeKey", "formJson", "createApplyFormJson", "<PERSON><PERSON><PERSON>", "Date", "now", "createTechReviewFormJson", "createProcessReviewFormJson", "createQualityReviewFormJson", "createCostReviewFormJson", "createFinalApprovalFormJson", "widgetList", "type", "options", "label", "required", "placeholder", "optionItems", "value", "rows", "formConfig", "modelName", "refName", "rulesName", "labelWidth", "labelPosition", "size", "labelAlign", "cssCode", "customClass", "functions", "layoutType", "showHelp"], "sources": ["src/views/flowable/npi/formConfig/index.vue"], "sourcesContent": ["<template>\n  <div class=\"npi-form-config-page\">\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <h2>\n          <i class=\"el-icon-setting\"></i>\n          NPI流程表单配置\n        </h2>\n        <p>为NPI审核流程的每个节点配置专属的VForm表单</p>\n      </div>\n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"quickSetup\">\n          <i class=\"el-icon-magic-stick\"></i>\n          快速配置\n        </el-button>\n      </div>\n    </div>\n\n    <div class=\"page-content\">\n      <el-card>\n        <div slot=\"header\" class=\"card-header\">\n          <span>NPI流程节点表单管理</span>\n          <div class=\"header-actions\">\n            <el-button type=\"text\" @click=\"showHelp\">\n              <i class=\"el-icon-question\"></i>\n              配置说明\n            </el-button>\n          </div>\n        </div>\n\n        <node-v-form-manager \n          v-model=\"npiFormConfig\" \n          process-key=\"npi_process\"\n          @save=\"handleSave\"\n        />\n      </el-card>\n    </div>\n\n    <!-- 快速配置对话框 -->\n    <el-dialog title=\"NPI流程快速配置\" :visible.sync=\"showQuickSetup\" width=\"600px\">\n      <div class=\"quick-setup-content\">\n        <p>将为您创建标准的NPI审核流程节点配置：</p>\n        <ul>\n          <li><strong>NPI申请节点</strong>：产品信息、技术规格、市场需求等</li>\n          <li><strong>技术评审节点</strong>：技术可行性、设计评估、风险分析等</li>\n          <li><strong>工艺评审节点</strong>：生产工艺、制造难度、设备需求等</li>\n          <li><strong>质量评审节点</strong>：质量标准、测试方案、认证要求等</li>\n          <li><strong>成本评审节点</strong>：成本分析、价格策略、盈利预测等</li>\n          <li><strong>最终审批节点</strong>：综合评估、决策意见、后续计划等</li>\n        </ul>\n        <el-alert \n          title=\"注意：此操作将覆盖现有配置\" \n          type=\"warning\" \n          :closable=\"false\"\n          style=\"margin-top: 16px;\"\n        />\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"showQuickSetup = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"executeQuickSetup\">确定配置</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 配置说明对话框 -->\n    <el-dialog title=\"NPI流程表单配置说明\" :visible.sync=\"showHelpDialog\" width=\"700px\">\n      <div class=\"help-content\">\n        <h4>🎯 配置目标</h4>\n        <p>为NPI（New Product Introduction）审核流程的每个节点配置专属的VForm表单，实现不同审核阶段的差异化数据收集。</p>\n        \n        <h4>📋 配置步骤</h4>\n        <ol>\n          <li><strong>添加节点</strong>：点击\"添加节点表单\"创建新的审核节点</li>\n          <li><strong>配置节点信息</strong>：\n            <ul>\n              <li>节点名称：显示在界面上的名称</li>\n              <li>节点类型：预定义的NPI审核类型</li>\n              <li>节点标识：对应流程图中的节点ID（重要！）</li>\n            </ul>\n          </li>\n          <li><strong>设计表单</strong>：点击\"设计表单\"使用VForm设计器创建专属表单</li>\n          <li><strong>保存配置</strong>：完成后保存整体配置</li>\n        </ol>\n        \n        <h4>🔧 节点标识说明</h4>\n        <p>节点标识必须与Flowable流程图中的节点ID完全一致，系统将根据此标识匹配对应的表单配置。</p>\n        \n        <h4>📊 NPI审核节点建议</h4>\n        <ul>\n          <li><strong>NPI申请</strong>：产品基本信息、市场分析、技术概述</li>\n          <li><strong>技术评审</strong>：技术方案、设计文档、技术风险</li>\n          <li><strong>工艺评审</strong>：生产工艺、制造成本、产能评估</li>\n          <li><strong>质量评审</strong>：质量计划、测试标准、认证需求</li>\n          <li><strong>成本评审</strong>：成本结构、定价策略、ROI分析</li>\n          <li><strong>最终审批</strong>：综合决策、资源分配、时间计划</li>\n        </ul>\n        \n        <h4>💡 最佳实践</h4>\n        <ul>\n          <li>每个节点的表单应该专注于该阶段的核心评审内容</li>\n          <li>使用合适的字段类型提升用户体验</li>\n          <li>为重要字段设置必填验证</li>\n          <li>定期备份表单配置</li>\n        </ul>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport NodeVFormManager from '@/components/NodeVFormManager'\n\nexport default {\n  name: 'NPIFormConfig',\n  components: {\n    NodeVFormManager\n  },\n  data() {\n    return {\n      npiFormConfig: [],\n      showQuickSetup: false,\n      showHelpDialog: false\n    }\n  },\n  created() {\n    this.loadNPIFormConfig();\n  },\n  methods: {\n    /** 加载NPI表单配置 */\n    loadNPIFormConfig() {\n      const saved = localStorage.getItem('node_vform_config_npi_process');\n      if (saved) {\n        try {\n          const config = JSON.parse(saved);\n          this.npiFormConfig = config.nodeForms || [];\n        } catch (error) {\n          console.warn('加载NPI表单配置失败:', error);\n        }\n      }\n    },\n\n    /** 处理保存 */\n    handleSave(config) {\n      // 这里可以调用API保存到后端\n      console.log('保存NPI表单配置:', config);\n      this.$message.success('NPI表单配置保存成功');\n    },\n\n    /** 快速配置 */\n    quickSetup() {\n      this.showQuickSetup = true;\n    },\n\n    /** 执行快速配置 */\n    executeQuickSetup() {\n      const quickConfig = [\n        {\n          id: 'npi_apply_node',\n          nodeName: 'NPI申请',\n          nodeType: 'npi_apply',\n          nodeKey: 'npi_apply_task',\n          formJson: this.createApplyFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'tech_review_node',\n          nodeName: '技术评审',\n          nodeType: 'tech_review',\n          nodeKey: 'tech_review_task',\n          formJson: this.createTechReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'process_review_node',\n          nodeName: '工艺评审',\n          nodeType: 'process_review',\n          nodeKey: 'process_review_task',\n          formJson: this.createProcessReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'quality_review_node',\n          nodeName: '质量评审',\n          nodeType: 'quality_review',\n          nodeKey: 'quality_review_task',\n          formJson: this.createQualityReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'cost_review_node',\n          nodeName: '成本评审',\n          nodeType: 'cost_review',\n          nodeKey: 'cost_review_task',\n          formJson: this.createCostReviewFormJson(),\n          previewKey: Date.now()\n        },\n        {\n          id: 'final_approval_node',\n          nodeName: '最终审批',\n          nodeType: 'final_approval',\n          nodeKey: 'final_approval_task',\n          formJson: this.createFinalApprovalFormJson(),\n          previewKey: Date.now()\n        }\n      ];\n\n      this.npiFormConfig = quickConfig;\n      this.showQuickSetup = false;\n      this.$message.success('快速配置完成！');\n    },\n\n    /** 创建申请表单JSON */\n    createApplyFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'input',\n            options: {\n              name: 'productName',\n              label: '产品名称',\n              required: true,\n              placeholder: '请输入产品名称'\n            }\n          },\n          {\n            type: 'input',\n            options: {\n              name: 'productCode',\n              label: '产品编码',\n              required: true,\n              placeholder: '请输入产品编码'\n            }\n          },\n          {\n            type: 'select',\n            options: {\n              name: 'productCategory',\n              label: '产品类别',\n              required: true,\n              optionItems: [\n                { label: '硬件产品', value: 'hardware' },\n                { label: '软件产品', value: 'software' },\n                { label: '服务产品', value: 'service' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'productDescription',\n              label: '产品描述',\n              required: true,\n              rows: 4,\n              placeholder: '请详细描述产品功能和特性'\n            }\n          },\n          {\n            type: 'date',\n            options: {\n              name: 'expectedLaunchDate',\n              label: '预期上市时间',\n              required: true\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建技术评审表单JSON */\n    createTechReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'radio',\n            options: {\n              name: 'techFeasibility',\n              label: '技术可行性',\n              required: true,\n              optionItems: [\n                { label: '完全可行', value: 'feasible' },\n                { label: '需要改进', value: 'needs_improvement' },\n                { label: '技术风险高', value: 'high_risk' },\n                { label: '不可行', value: 'not_feasible' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'techRiskAnalysis',\n              label: '技术风险分析',\n              required: true,\n              rows: 4,\n              placeholder: '请分析主要技术风险和应对措施'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'techRecommendation',\n              label: '技术建议',\n              required: true,\n              rows: 3,\n              placeholder: '请提供技术改进建议'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建工艺评审表单JSON */\n    createProcessReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'radio',\n            options: {\n              name: 'processComplexity',\n              label: '工艺复杂度',\n              required: true,\n              optionItems: [\n                { label: '简单', value: 'simple' },\n                { label: '中等', value: 'medium' },\n                { label: '复杂', value: 'complex' },\n                { label: '极其复杂', value: 'very_complex' }\n              ]\n            }\n          },\n          {\n            type: 'number',\n            options: {\n              name: 'estimatedCost',\n              label: '预估制造成本',\n              required: true,\n              placeholder: '请输入预估成本（元）'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'processRecommendation',\n              label: '工艺建议',\n              required: true,\n              rows: 4,\n              placeholder: '请提供工艺改进建议'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建质量评审表单JSON */\n    createQualityReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'checkbox',\n            options: {\n              name: 'qualityStandards',\n              label: '质量标准',\n              required: true,\n              optionItems: [\n                { label: 'ISO 9001', value: 'iso9001' },\n                { label: 'ISO 14001', value: 'iso14001' },\n                { label: 'CE认证', value: 'ce' },\n                { label: 'FCC认证', value: 'fcc' },\n                { label: '其他', value: 'other' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'testPlan',\n              label: '测试计划',\n              required: true,\n              rows: 4,\n              placeholder: '请描述详细的测试计划'\n            }\n          },\n          {\n            type: 'radio',\n            options: {\n              name: 'qualityRisk',\n              label: '质量风险评估',\n              required: true,\n              optionItems: [\n                { label: '低风险', value: 'low' },\n                { label: '中等风险', value: 'medium' },\n                { label: '高风险', value: 'high' }\n              ]\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建成本评审表单JSON */\n    createCostReviewFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'number',\n            options: {\n              name: 'developmentCost',\n              label: '开发成本',\n              required: true,\n              placeholder: '请输入开发成本（万元）'\n            }\n          },\n          {\n            type: 'number',\n            options: {\n              name: 'unitCost',\n              label: '单位成本',\n              required: true,\n              placeholder: '请输入单位成本（元）'\n            }\n          },\n          {\n            type: 'number',\n            options: {\n              name: 'expectedPrice',\n              label: '预期售价',\n              required: true,\n              placeholder: '请输入预期售价（元）'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'costAnalysis',\n              label: '成本分析',\n              required: true,\n              rows: 4,\n              placeholder: '请提供详细的成本分析'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 创建最终审批表单JSON */\n    createFinalApprovalFormJson() {\n      return {\n        widgetList: [\n          {\n            type: 'radio',\n            options: {\n              name: 'finalDecision',\n              label: '最终决策',\n              required: true,\n              optionItems: [\n                { label: '批准立项', value: 'approved' },\n                { label: '有条件批准', value: 'conditional' },\n                { label: '需要修改', value: 'needs_revision' },\n                { label: '拒绝立项', value: 'rejected' }\n              ]\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'decisionReason',\n              label: '决策理由',\n              required: true,\n              rows: 4,\n              placeholder: '请说明决策理由'\n            }\n          },\n          {\n            type: 'textarea',\n            options: {\n              name: 'nextSteps',\n              label: '后续计划',\n              required: false,\n              rows: 3,\n              placeholder: '请描述后续执行计划'\n            }\n          }\n        ],\n        formConfig: {\n          modelName: 'formData',\n          refName: 'vForm',\n          rulesName: 'rules',\n          labelWidth: 120,\n          labelPosition: 'left',\n          size: '',\n          labelAlign: 'label-left-align',\n          cssCode: '',\n          customClass: '',\n          functions: '',\n          layoutType: 'PC'\n        }\n      };\n    },\n\n    /** 显示帮助 */\n    showHelp() {\n      this.showHelpDialog = true;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.npi-form-config-page {\n  padding: 20px;\n  background-color: #f0f2f5;\n  min-height: calc(100vh - 84px);\n}\n\n.page-header {\n  margin-bottom: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  \n  .header-content {\n    h2 {\n      margin: 0 0 8px 0;\n      color: #303133;\n      font-size: 24px;\n      \n      i {\n        margin-right: 8px;\n        color: #409EFF;\n      }\n    }\n    \n    p {\n      margin: 0;\n      color: #606266;\n      font-size: 14px;\n    }\n  }\n}\n\n.page-content {\n  .el-card {\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  }\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  \n  span {\n    font-weight: 600;\n    color: #303133;\n  }\n}\n\n.quick-setup-content {\n  p {\n    margin-bottom: 16px;\n    color: #606266;\n  }\n  \n  ul {\n    padding-left: 20px;\n    \n    li {\n      margin-bottom: 8px;\n      color: #606266;\n      line-height: 1.6;\n      \n      strong {\n        color: #303133;\n      }\n    }\n  }\n}\n\n.help-content {\n  h4 {\n    color: #303133;\n    margin: 16px 0 8px 0;\n    \n    &:first-child {\n      margin-top: 0;\n    }\n  }\n  \n  p, li {\n    color: #606266;\n    line-height: 1.6;\n  }\n  \n  ul, ol {\n    padding-left: 20px;\n  }\n  \n  li {\n    margin-bottom: 4px;\n  }\n  \n  strong {\n    color: #303133;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;AA6GA,IAAAA,iBAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,gBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,cAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IACA,gBACAD,iBAAA,WAAAA,kBAAA;MACA,IAAAE,KAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,IAAAF,KAAA;QACA;UACA,IAAAG,MAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,KAAA;UACA,KAAAN,aAAA,GAAAS,MAAA,CAAAG,SAAA;QACA,SAAAC,KAAA;UACAC,OAAA,CAAAC,IAAA,iBAAAF,KAAA;QACA;MACA;IACA;IAEA,WACAG,UAAA,WAAAA,WAAAP,MAAA;MACA;MACAK,OAAA,CAAAG,GAAA,eAAAR,MAAA;MACA,KAAAS,QAAA,CAAAC,OAAA;IACA;IAEA,WACAC,UAAA,WAAAA,WAAA;MACA,KAAAnB,cAAA;IACA;IAEA,aACAoB,iBAAA,WAAAA,kBAAA;MACA,IAAAC,WAAA,IACA;QACAC,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA,OAAAC,mBAAA;QACAC,UAAA,EAAAC,IAAA,CAAAC,GAAA;MACA,GACA;QACAR,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA,OAAAK,wBAAA;QACAH,UAAA,EAAAC,IAAA,CAAAC,GAAA;MACA,GACA;QACAR,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA,OAAAM,2BAAA;QACAJ,UAAA,EAAAC,IAAA,CAAAC,GAAA;MACA,GACA;QACAR,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA,OAAAO,2BAAA;QACAL,UAAA,EAAAC,IAAA,CAAAC,GAAA;MACA,GACA;QACAR,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA,OAAAQ,wBAAA;QACAN,UAAA,EAAAC,IAAA,CAAAC,GAAA;MACA,GACA;QACAR,EAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA,OAAAS,2BAAA;QACAP,UAAA,EAAAC,IAAA,CAAAC,GAAA;MACA,EACA;MAEA,KAAA/B,aAAA,GAAAsB,WAAA;MACA,KAAArB,cAAA;MACA,KAAAiB,QAAA,CAAAC,OAAA;IACA;IAEA,iBACAS,mBAAA,WAAAA,oBAAA;MACA;QACAS,UAAA,GACA;UACAC,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAC,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAC,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAE,WAAA,GACA;cAAAH,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA;UAEA;QACA,GACA;UACAN,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAI,IAAA;YACAH,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;UACA;QACA,EACA;QACAK,UAAA;UACAC,SAAA;UACAC,OAAA;UACAC,SAAA;UACAC,UAAA;UACAC,aAAA;UACAC,IAAA;UACAC,UAAA;UACAC,OAAA;UACAC,WAAA;UACAC,SAAA;UACAC,UAAA;QACA;MACA;IACA;IAEA,mBACAzB,wBAAA,WAAAA,yBAAA;MACA;QACAK,UAAA,GACA;UACAC,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAE,WAAA,GACA;cAAAH,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA;UAEA;QACA,GACA;UACAN,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAI,IAAA;YACAH,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAI,IAAA;YACAH,WAAA;UACA;QACA,EACA;QACAI,UAAA;UACAC,SAAA;UACAC,OAAA;UACAC,SAAA;UACAC,UAAA;UACAC,aAAA;UACAC,IAAA;UACAC,UAAA;UACAC,OAAA;UACAC,WAAA;UACAC,SAAA;UACAC,UAAA;QACA;MACA;IACA;IAEA,mBACAxB,2BAAA,WAAAA,4BAAA;MACA;QACAI,UAAA,GACA;UACAC,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAE,WAAA,GACA;cAAAH,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA;UAEA;QACA,GACA;UACAN,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAC,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAI,IAAA;YACAH,WAAA;UACA;QACA,EACA;QACAI,UAAA;UACAC,SAAA;UACAC,OAAA;UACAC,SAAA;UACAC,UAAA;UACAC,aAAA;UACAC,IAAA;UACAC,UAAA;UACAC,OAAA;UACAC,WAAA;UACAC,SAAA;UACAC,UAAA;QACA;MACA;IACA;IAEA,mBACAvB,2BAAA,WAAAA,4BAAA;MACA;QACAG,UAAA,GACA;UACAC,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAE,WAAA,GACA;cAAAH,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA;UAEA;QACA,GACA;UACAN,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAI,IAAA;YACAH,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAE,WAAA,GACA;cAAAH,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA;UAEA;QACA,EACA;QACAE,UAAA;UACAC,SAAA;UACAC,OAAA;UACAC,SAAA;UACAC,UAAA;UACAC,aAAA;UACAC,IAAA;UACAC,UAAA;UACAC,OAAA;UACAC,WAAA;UACAC,SAAA;UACAC,UAAA;QACA;MACA;IACA;IAEA,mBACAtB,wBAAA,WAAAA,yBAAA;MACA;QACAE,UAAA,GACA;UACAC,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAC,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAC,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAC,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAI,IAAA;YACAH,WAAA;UACA;QACA,EACA;QACAI,UAAA;UACAC,SAAA;UACAC,OAAA;UACAC,SAAA;UACAC,UAAA;UACAC,aAAA;UACAC,IAAA;UACAC,UAAA;UACAC,OAAA;UACAC,WAAA;UACAC,SAAA;UACAC,UAAA;QACA;MACA;IACA;IAEA,mBACArB,2BAAA,WAAAA,4BAAA;MACA;QACAC,UAAA,GACA;UACAC,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAE,WAAA,GACA;cAAAH,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA,GACA;cAAAJ,KAAA;cAAAI,KAAA;YAAA;UAEA;QACA,GACA;UACAN,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAI,IAAA;YACAH,WAAA;UACA;QACA,GACA;UACAJ,IAAA;UACAC,OAAA;YACA3C,IAAA;YACA4C,KAAA;YACAC,QAAA;YACAI,IAAA;YACAH,WAAA;UACA;QACA,EACA;QACAI,UAAA;UACAC,SAAA;UACAC,OAAA;UACAC,SAAA;UACAC,UAAA;UACAC,aAAA;UACAC,IAAA;UACAC,UAAA;UACAC,OAAA;UACAC,WAAA;UACAC,SAAA;UACAC,UAAA;QACA;MACA;IACA;IAEA,WACAC,QAAA,WAAAA,SAAA;MACA,KAAAxD,cAAA;IACA;EACA;AACA", "ignoreList": []}]}