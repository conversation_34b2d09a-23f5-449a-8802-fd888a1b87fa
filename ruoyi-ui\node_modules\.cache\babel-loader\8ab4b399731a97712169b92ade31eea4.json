{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752409366200}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_definition", "require", "name", "props", "flowRecordList", "type", "Array", "default", "defaultExpanded", "Boolean", "data", "activeHistoryNames", "nodeFormData", "formKeys", "loadingNodes", "Set", "watch", "handler", "newVal", "length", "loadAllNodeForms", "immediate", "_this", "for<PERSON>ach", "index", "parseInt", "replace", "record", "taskId", "has", "loadNodeForm", "methods", "getHistoryIcon", "finishTime", "getHistoryColor", "getStatusTagType", "getStatusText", "_this2", "_this3", "add", "getProcessVariables", "then", "res", "$set", "catch", "error", "console", "warn", "concat", "finally", "delete", "getFormDataDisplay", "_this4", "nodeData", "filteredData", "Object", "keys", "key", "undefined", "getFieldLabel", "formJson", "<PERSON><PERSON><PERSON>", "widgetList", "widget", "find", "w", "options", "label", "formatFieldValue", "value", "isArray", "join", "_typeof2", "JSON", "stringify", "String", "getFieldSpan", "valueStr", "getFieldClass"], "sources": ["src/components/FlowHistory/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <!-- 节点表单数据 -->\n          <div v-if=\"record.taskId && nodeFormData[record.taskId]\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> 节点表单数据\n            </h5>\n            <div class=\"form-data-container\">\n              <el-descriptions :column=\"2\" size=\"small\" border class=\"form-data-descriptions\">\n                <el-descriptions-item\n                  v-for=\"(value, key) in getFormDataDisplay(record.taskId)\"\n                  :key=\"key\"\n                  :label=\"key\"\n                  :span=\"getFieldSpan(value)\"\n                >\n                  <div class=\"form-field-value\" :class=\"getFieldClass(value)\">\n                    {{ formatFieldValue(value) }}\n                  </div>\n                </el-descriptions-item>\n              </el-descriptions>\n            </div>\n          </div>\n\n          <!-- 办理信息 -->\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nimport { getProcessVariables } from '@/api/flowable/definition'\n\nexport default {\n  name: 'FlowHistory',\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: [],\n      nodeFormData: {}, // 存储每个节点的表单数据\n      formKeys: {}, // 存储每个表单的key，用于强制重新渲染\n      loadingNodes: new Set() // 正在加载的节点\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0) {\n          if (this.defaultExpanded) {\n            // 默认展开第一个节点\n            this.activeHistoryNames = ['history-0'];\n          }\n          // 加载所有节点的表单数据\n          this.loadAllNodeForms();\n        }\n      },\n      immediate: true\n    },\n    activeHistoryNames: {\n      handler(newVal) {\n        // 当展开节点时，加载对应的表单数据\n        newVal.forEach(name => {\n          const index = parseInt(name.replace('history-', ''));\n          const record = this.flowRecordList[index];\n          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n            this.loadNodeForm(record.taskId);\n          }\n        });\n      }\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    },\n\n    /** 加载所有节点的表单数据 */\n    loadAllNodeForms() {\n      this.flowRecordList.forEach(record => {\n        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n          this.loadNodeForm(record.taskId);\n        }\n      });\n    },\n\n    /** 加载单个节点的表单数据 */\n    loadNodeForm(taskId) {\n      if (!taskId || this.loadingNodes.has(taskId)) {\n        return;\n      }\n\n      this.loadingNodes.add(taskId);\n\n      getProcessVariables(taskId).then(res => {\n        if (res.data) {\n          // 设置表单数据\n          this.$set(this.nodeFormData, taskId, res.data);\n        }\n      }).catch(error => {\n        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);\n      }).finally(() => {\n        this.loadingNodes.delete(taskId);\n      });\n    },\n\n    /** 获取表单数据用于显示 */\n    getFormDataDisplay(taskId) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return {};\n\n      // 过滤掉系统字段和空值\n      const filteredData = {};\n      Object.keys(nodeData).forEach(key => {\n        if (key !== 'formJson' &&\n            key !== 'taskId' &&\n            key !== 'procInsId' &&\n            nodeData[key] !== null &&\n            nodeData[key] !== undefined &&\n            nodeData[key] !== '') {\n          filteredData[this.getFieldLabel(key, nodeData.formJson)] = nodeData[key];\n        }\n      });\n\n      return filteredData;\n    },\n\n    /** 获取字段标签 */\n    getFieldLabel(fieldKey, formJson) {\n      if (!formJson || !formJson.widgetList) {\n        return fieldKey;\n      }\n\n      // 在表单JSON中查找字段标签\n      const widget = formJson.widgetList.find(w => w.options && w.options.name === fieldKey);\n      return widget && widget.options && widget.options.label ? widget.options.label : fieldKey;\n    },\n\n    /** 格式化字段值 */\n    formatFieldValue(value) {\n      if (Array.isArray(value)) {\n        return value.join(', ');\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      if (typeof value === 'boolean') {\n        return value ? '是' : '否';\n      }\n      return String(value);\n    },\n\n    /** 获取字段跨度 */\n    getFieldSpan(value) {\n      const valueStr = this.formatFieldValue(value);\n      // 长文本占用两列\n      return valueStr.length > 20 ? 2 : 1;\n    },\n\n    /** 获取字段样式类 */\n    getFieldClass(value) {\n      if (typeof value === 'boolean') {\n        return value ? 'field-boolean-true' : 'field-boolean-false';\n      }\n      if (typeof value === 'number') {\n        return 'field-number';\n      }\n      if (Array.isArray(value)) {\n        return 'field-array';\n      }\n      return 'field-text';\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n\n/* 节点表单数据样式 */\n.node-form-section {\n  margin-bottom: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-section-title {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #606266;\n  border-bottom: 1px solid #E4E7ED;\n}\n\n.form-section-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n.form-data-container {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n.form-data-descriptions {\n  background-color: white;\n}\n\n/* 表单字段值样式 */\n.form-field-value {\n  word-break: break-word;\n  line-height: 1.5;\n}\n\n.field-boolean-true {\n  color: #67C23A;\n  font-weight: 600;\n}\n\n.field-boolean-false {\n  color: #F56C6C;\n  font-weight: 600;\n}\n\n.field-number {\n  color: #E6A23C;\n  font-weight: 500;\n}\n\n.field-array {\n  color: #909399;\n  font-style: italic;\n}\n\n.field-text {\n  color: #606266;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,cAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,eAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;MACAC,YAAA;MAAA;MACAC,QAAA;MAAA;MACAC,YAAA,MAAAC,GAAA;IACA;EACA;EACAC,KAAA;IACAZ,cAAA;MACAa,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,MAAA;UACA,SAAAX,eAAA;YACA;YACA,KAAAG,kBAAA;UACA;UACA;UACA,KAAAS,gBAAA;QACA;MACA;MACAC,SAAA;IACA;IACAV,kBAAA;MACAM,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAI,KAAA;QACA;QACAJ,MAAA,CAAAK,OAAA,WAAArB,IAAA;UACA,IAAAsB,KAAA,GAAAC,QAAA,CAAAvB,IAAA,CAAAwB,OAAA;UACA,IAAAC,MAAA,GAAAL,KAAA,CAAAlB,cAAA,CAAAoB,KAAA;UACA,IAAAG,MAAA,IAAAA,MAAA,CAAAC,MAAA,KAAAN,KAAA,CAAAV,YAAA,CAAAe,MAAA,CAAAC,MAAA,MAAAN,KAAA,CAAAR,YAAA,CAAAe,GAAA,CAAAF,MAAA,CAAAC,MAAA;YACAN,KAAA,CAAAQ,YAAA,CAAAH,MAAA,CAAAC,MAAA;UACA;QACA;MACA;IACA;EACA;EACAG,OAAA;IACA,eACAC,cAAA,WAAAA,eAAAL,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAC,eAAA,WAAAA,gBAAAP,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAE,gBAAA,WAAAA,iBAAAR,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,aACAG,aAAA,WAAAA,cAAAT,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,kBACAb,gBAAA,WAAAA,iBAAA;MAAA,IAAAiB,MAAA;MACA,KAAAjC,cAAA,CAAAmB,OAAA,WAAAI,MAAA;QACA,IAAAA,MAAA,CAAAC,MAAA,KAAAS,MAAA,CAAAzB,YAAA,CAAAe,MAAA,CAAAC,MAAA,MAAAS,MAAA,CAAAvB,YAAA,CAAAe,GAAA,CAAAF,MAAA,CAAAC,MAAA;UACAS,MAAA,CAAAP,YAAA,CAAAH,MAAA,CAAAC,MAAA;QACA;MACA;IACA;IAEA,kBACAE,YAAA,WAAAA,aAAAF,MAAA;MAAA,IAAAU,MAAA;MACA,KAAAV,MAAA,SAAAd,YAAA,CAAAe,GAAA,CAAAD,MAAA;QACA;MACA;MAEA,KAAAd,YAAA,CAAAyB,GAAA,CAAAX,MAAA;MAEA,IAAAY,+BAAA,EAAAZ,MAAA,EAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAhC,IAAA;UACA;UACA4B,MAAA,CAAAK,IAAA,CAAAL,MAAA,CAAA1B,YAAA,EAAAgB,MAAA,EAAAc,GAAA,CAAAhC,IAAA;QACA;MACA,GAAAkC,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAC,IAAA,6BAAAC,MAAA,CAAApB,MAAA,mDAAAiB,KAAA;MACA,GAAAI,OAAA;QACAX,MAAA,CAAAxB,YAAA,CAAAoC,MAAA,CAAAtB,MAAA;MACA;IACA;IAEA,iBACAuB,kBAAA,WAAAA,mBAAAvB,MAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,QAAA,QAAAzC,YAAA,CAAAgB,MAAA;MACA,KAAAyB,QAAA;;MAEA;MACA,IAAAC,YAAA;MACAC,MAAA,CAAAC,IAAA,CAAAH,QAAA,EAAA9B,OAAA,WAAAkC,GAAA;QACA,IAAAA,GAAA,mBACAA,GAAA,iBACAA,GAAA,oBACAJ,QAAA,CAAAI,GAAA,cACAJ,QAAA,CAAAI,GAAA,MAAAC,SAAA,IACAL,QAAA,CAAAI,GAAA;UACAH,YAAA,CAAAF,MAAA,CAAAO,aAAA,CAAAF,GAAA,EAAAJ,QAAA,CAAAO,QAAA,KAAAP,QAAA,CAAAI,GAAA;QACA;MACA;MAEA,OAAAH,YAAA;IACA;IAEA,aACAK,aAAA,WAAAA,cAAAE,QAAA,EAAAD,QAAA;MACA,KAAAA,QAAA,KAAAA,QAAA,CAAAE,UAAA;QACA,OAAAD,QAAA;MACA;;MAEA;MACA,IAAAE,MAAA,GAAAH,QAAA,CAAAE,UAAA,CAAAE,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,OAAA,IAAAD,CAAA,CAAAC,OAAA,CAAAhE,IAAA,KAAA2D,QAAA;MAAA;MACA,OAAAE,MAAA,IAAAA,MAAA,CAAAG,OAAA,IAAAH,MAAA,CAAAG,OAAA,CAAAC,KAAA,GAAAJ,MAAA,CAAAG,OAAA,CAAAC,KAAA,GAAAN,QAAA;IACA;IAEA,aACAO,gBAAA,WAAAA,iBAAAC,KAAA;MACA,IAAA/D,KAAA,CAAAgE,OAAA,CAAAD,KAAA;QACA,OAAAA,KAAA,CAAAE,IAAA;MACA;MACA,QAAAC,QAAA,CAAAjE,OAAA,EAAA8D,KAAA;QACA,OAAAI,IAAA,CAAAC,SAAA,CAAAL,KAAA;MACA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA;MACA;MACA,OAAAM,MAAA,CAAAN,KAAA;IACA;IAEA,aACAO,YAAA,WAAAA,aAAAP,KAAA;MACA,IAAAQ,QAAA,QAAAT,gBAAA,CAAAC,KAAA;MACA;MACA,OAAAQ,QAAA,CAAA1D,MAAA;IACA;IAEA,cACA2D,aAAA,WAAAA,cAAAT,KAAA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA;MACA;MACA,WAAAA,KAAA;QACA;MACA;MACA,IAAA/D,KAAA,CAAAgE,OAAA,CAAAD,KAAA;QACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}