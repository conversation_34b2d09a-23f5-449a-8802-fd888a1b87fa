{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752410174898}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_definition", "require", "_NodeForm", "_interopRequireDefault", "name", "components", "NodeForm", "props", "flowRecordList", "type", "Array", "default", "defaultExpanded", "Boolean", "data", "activeHistoryNames", "nodeFormData", "formKeys", "loadingNodes", "Set", "formModes", "nodeFormConfigs", "watch", "handler", "newVal", "length", "loadAllNodeForms", "immediate", "_this", "for<PERSON>ach", "index", "parseInt", "replace", "record", "taskId", "has", "loadNodeForm", "methods", "getHistoryIcon", "finishTime", "getHistoryColor", "getStatusTagType", "getStatusText", "_this2", "_this3", "add", "getProcessVariables", "then", "res", "$set", "catch", "error", "console", "warn", "concat", "finally", "delete", "getFormDataDisplay", "_this4", "nodeData", "log", "systemFields", "filteredData", "Object", "keys", "key", "value", "includes", "undefined", "label", "getFieldLabel", "formJson", "<PERSON><PERSON><PERSON>", "widgets", "widgetList", "formConfig", "isArray", "widget", "find", "w", "options", "__config__", "formId", "__vModel__", "_widget$options", "_widget$__config__", "formatFieldValue", "join", "_typeof2", "JSON", "stringify", "String", "getFieldSpan", "valueStr", "getFieldClass", "toggleFormMode", "currentMode", "getNodeFormConfig", "_this5", "loadNodeFormConfigs", "config", "nodeName", "taskName", "nodeType", "getNodeTypeFromRecord", "fields", "saved", "localStorage", "getItem", "parse", "nodeForms"], "sources": ["src/components/FlowHistory/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <!-- 节点表单 -->\n          <div v-if=\"getNodeFormConfig(record)\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> {{ record.taskName }} - 表单\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"toggleFormMode(record.taskId)\"\n                style=\"float: right; margin-top: -2px;\"\n              >\n                {{ formModes[record.taskId] === 'design' ? '切换到数据视图' : '切换到表单视图' }}\n              </el-button>\n            </h5>\n            <div class=\"form-container\">\n              <!-- 表单设计视图 -->\n              <div v-if=\"formModes[record.taskId] === 'design'\">\n                <node-form\n                  :value=\"getNodeFormConfig(record)\"\n                  :title=\"record.taskName\"\n                  :readonly=\"true\"\n                />\n              </div>\n\n              <!-- 数据视图 -->\n              <div v-else>\n                <el-descriptions :column=\"2\" size=\"small\" border class=\"form-data-descriptions\">\n                  <el-descriptions-item\n                    v-for=\"(value, key) in getFormDataDisplay(record.taskId)\"\n                    :key=\"key\"\n                    :label=\"key\"\n                    :span=\"getFieldSpan(value)\"\n                  >\n                    <div class=\"form-field-value\" :class=\"getFieldClass(value)\">\n                      {{ formatFieldValue(value) }}\n                    </div>\n                  </el-descriptions-item>\n                </el-descriptions>\n                <div v-if=\"Object.keys(getFormDataDisplay(record.taskId)).length === 0\" class=\"no-data\">\n                  暂无表单数据\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 办理信息 -->\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nimport { getProcessVariables } from '@/api/flowable/definition'\nimport NodeForm from '@/components/NodeForm'\n\nexport default {\n  name: 'FlowHistory',\n  components: {\n    NodeForm\n  },\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: [],\n      nodeFormData: {}, // 存储每个节点的表单数据\n      formKeys: {}, // 存储每个表单的key，用于强制重新渲染\n      loadingNodes: new Set(), // 正在加载的节点\n      formModes: {}, // 控制表单显示模式：design(表单视图) 或 data(数据视图)\n      nodeFormConfigs: [] // 节点表单配置\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0) {\n          if (this.defaultExpanded) {\n            // 默认展开第一个节点\n            this.activeHistoryNames = ['history-0'];\n          }\n          // 加载所有节点的表单数据\n          this.loadAllNodeForms();\n        }\n      },\n      immediate: true\n    },\n    activeHistoryNames: {\n      handler(newVal) {\n        // 当展开节点时，加载对应的表单数据\n        newVal.forEach(name => {\n          const index = parseInt(name.replace('history-', ''));\n          const record = this.flowRecordList[index];\n          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n            this.loadNodeForm(record.taskId);\n          }\n        });\n      }\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    },\n\n    /** 加载所有节点的表单数据 */\n    loadAllNodeForms() {\n      this.flowRecordList.forEach(record => {\n        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n          this.loadNodeForm(record.taskId);\n        }\n      });\n    },\n\n    /** 加载单个节点的表单数据 */\n    loadNodeForm(taskId) {\n      if (!taskId || this.loadingNodes.has(taskId)) {\n        return;\n      }\n\n      this.loadingNodes.add(taskId);\n\n      getProcessVariables(taskId).then(res => {\n        if (res.data) {\n          // 设置表单数据\n          this.$set(this.nodeFormData, taskId, res.data);\n        }\n      }).catch(error => {\n        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);\n      }).finally(() => {\n        this.loadingNodes.delete(taskId);\n      });\n    },\n\n    /** 获取表单数据用于显示 */\n    getFormDataDisplay(taskId) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return {};\n\n      console.log('节点数据:', taskId, nodeData); // 调试信息\n\n      // 系统字段列表\n      const systemFields = ['formJson', 'taskId', 'procInsId', 'deployId', 'procDefId', 'instanceId'];\n\n      const filteredData = {};\n      Object.keys(nodeData).forEach(key => {\n        const value = nodeData[key];\n\n        // 跳过系统字段\n        if (systemFields.includes(key)) {\n          return;\n        }\n\n        // 包含所有非空值，包括数字0、false等有意义的值\n        if (value !== null && value !== undefined && value !== '') {\n          const label = this.getFieldLabel(key, nodeData.formJson);\n          filteredData[label] = value;\n        }\n      });\n\n      console.log('过滤后的数据:', filteredData); // 调试信息\n      return filteredData;\n    },\n\n    /** 获取字段标签 */\n    getFieldLabel(fieldKey, formJson) {\n      if (!formJson) {\n        return fieldKey;\n      }\n\n      // 尝试多种可能的表单结构\n      let widgets = [];\n\n      if (formJson.widgetList) {\n        widgets = formJson.widgetList;\n      } else if (formJson.formConfig && formJson.formConfig.widgetList) {\n        widgets = formJson.formConfig.widgetList;\n      } else if (Array.isArray(formJson)) {\n        widgets = formJson;\n      }\n\n      console.log('查找字段标签:', fieldKey, '在widgets:', widgets); // 调试信息\n\n      // 在表单组件中查找字段标签\n      const widget = widgets.find(w => {\n        if (w.options && w.options.name === fieldKey) return true;\n        if (w.__config__ && w.__config__.formId === fieldKey) return true;\n        if (w.__vModel__ === fieldKey) return true;\n        return false;\n      });\n\n      if (widget) {\n        // 尝试多种可能的标签字段\n        const label = widget.options?.label ||\n                     widget.__config__?.label ||\n                     widget.label ||\n                     fieldKey;\n        console.log('找到标签:', fieldKey, '->', label); // 调试信息\n        return label;\n      }\n\n      return fieldKey;\n    },\n\n    /** 格式化字段值 */\n    formatFieldValue(value) {\n      if (Array.isArray(value)) {\n        return value.join(', ');\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      if (typeof value === 'boolean') {\n        return value ? '是' : '否';\n      }\n      return String(value);\n    },\n\n    /** 获取字段跨度 */\n    getFieldSpan(value) {\n      const valueStr = this.formatFieldValue(value);\n      // 长文本占用两列\n      return valueStr.length > 20 ? 2 : 1;\n    },\n\n    /** 获取字段样式类 */\n    getFieldClass(value) {\n      if (typeof value === 'boolean') {\n        return value ? 'field-boolean-true' : 'field-boolean-false';\n      }\n      if (typeof value === 'number') {\n        return 'field-number';\n      }\n      if (Array.isArray(value)) {\n        return 'field-array';\n      }\n      return 'field-text';\n    },\n\n    /** 切换表单显示模式 */\n    toggleFormMode(taskId) {\n      const currentMode = this.formModes[taskId] || 'design';\n      this.$set(this.formModes, taskId, currentMode === 'design' ? 'data' : 'design');\n    },\n\n    /** 获取节点表单配置 */\n    getNodeFormConfig(record) {\n      // 从本地存储加载表单配置\n      if (this.nodeFormConfigs.length === 0) {\n        this.loadNodeFormConfigs();\n      }\n\n      // 根据节点名称或任务名称查找对应的表单配置\n      const config = this.nodeFormConfigs.find(config =>\n        config.nodeName === record.taskName ||\n        config.nodeType === this.getNodeTypeFromRecord(record)\n      );\n\n      return config ? config.fields : null;\n    },\n\n    /** 从记录中推断节点类型 */\n    getNodeTypeFromRecord(record) {\n      // 根据任务名称推断节点类型\n      const taskName = record.taskName || '';\n      if (taskName.includes('开始') || taskName.includes('申请')) {\n        return 'start';\n      } else if (taskName.includes('审批') || taskName.includes('审核')) {\n        return 'approval';\n      } else if (taskName.includes('结束')) {\n        return 'end';\n      }\n      return 'userTask';\n    },\n\n    /** 加载节点表单配置 */\n    loadNodeFormConfigs() {\n      try {\n        const saved = localStorage.getItem('flowable_form_config');\n        if (saved) {\n          const config = JSON.parse(saved);\n          this.nodeFormConfigs = config.nodeForms || [];\n        }\n      } catch (error) {\n        console.warn('加载节点表单配置失败:', error);\n        this.nodeFormConfigs = [];\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n\n/* 节点表单数据样式 */\n.node-form-section {\n  margin-bottom: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-section-title {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #606266;\n  border-bottom: 1px solid #E4E7ED;\n}\n\n.form-section-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n.form-data-container {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n.form-data-descriptions {\n  background-color: white;\n}\n\n/* 表单字段值样式 */\n.form-field-value {\n  word-break: break-word;\n  line-height: 1.5;\n}\n\n.field-boolean-true {\n  color: #67C23A;\n  font-weight: 600;\n}\n\n.field-boolean-false {\n  color: #F56C6C;\n  font-weight: 600;\n}\n\n.field-number {\n  color: #E6A23C;\n  font-weight: 500;\n}\n\n.field-array {\n  color: #909399;\n  font-style: italic;\n}\n\n.field-text {\n  color: #606266;\n}\n\n/* 原始数据显示样式 */\n.raw-data-container {\n  background-color: #f8f8f8;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 12px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.raw-data-container pre {\n  margin: 0;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  color: #333;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.no-data {\n  text-align: center;\n  color: #909399;\n  padding: 20px;\n  font-style: italic;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuGA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA;EACA;EACAC,KAAA;IACAC,cAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,eAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;MACAC,YAAA;MAAA;MACAC,QAAA;MAAA;MACAC,YAAA,MAAAC,GAAA;MAAA;MACAC,SAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,KAAA;IACAd,cAAA;MACAe,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,MAAA;UACA,SAAAb,eAAA;YACA;YACA,KAAAG,kBAAA;UACA;UACA;UACA,KAAAW,gBAAA;QACA;MACA;MACAC,SAAA;IACA;IACAZ,kBAAA;MACAQ,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAI,KAAA;QACA;QACAJ,MAAA,CAAAK,OAAA,WAAAzB,IAAA;UACA,IAAA0B,KAAA,GAAAC,QAAA,CAAA3B,IAAA,CAAA4B,OAAA;UACA,IAAAC,MAAA,GAAAL,KAAA,CAAApB,cAAA,CAAAsB,KAAA;UACA,IAAAG,MAAA,IAAAA,MAAA,CAAAC,MAAA,KAAAN,KAAA,CAAAZ,YAAA,CAAAiB,MAAA,CAAAC,MAAA,MAAAN,KAAA,CAAAV,YAAA,CAAAiB,GAAA,CAAAF,MAAA,CAAAC,MAAA;YACAN,KAAA,CAAAQ,YAAA,CAAAH,MAAA,CAAAC,MAAA;UACA;QACA;MACA;IACA;EACA;EACAG,OAAA;IACA,eACAC,cAAA,WAAAA,eAAAL,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAC,eAAA,WAAAA,gBAAAP,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAE,gBAAA,WAAAA,iBAAAR,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,aACAG,aAAA,WAAAA,cAAAT,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,kBACAb,gBAAA,WAAAA,iBAAA;MAAA,IAAAiB,MAAA;MACA,KAAAnC,cAAA,CAAAqB,OAAA,WAAAI,MAAA;QACA,IAAAA,MAAA,CAAAC,MAAA,KAAAS,MAAA,CAAA3B,YAAA,CAAAiB,MAAA,CAAAC,MAAA,MAAAS,MAAA,CAAAzB,YAAA,CAAAiB,GAAA,CAAAF,MAAA,CAAAC,MAAA;UACAS,MAAA,CAAAP,YAAA,CAAAH,MAAA,CAAAC,MAAA;QACA;MACA;IACA;IAEA,kBACAE,YAAA,WAAAA,aAAAF,MAAA;MAAA,IAAAU,MAAA;MACA,KAAAV,MAAA,SAAAhB,YAAA,CAAAiB,GAAA,CAAAD,MAAA;QACA;MACA;MAEA,KAAAhB,YAAA,CAAA2B,GAAA,CAAAX,MAAA;MAEA,IAAAY,+BAAA,EAAAZ,MAAA,EAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAlC,IAAA;UACA;UACA8B,MAAA,CAAAK,IAAA,CAAAL,MAAA,CAAA5B,YAAA,EAAAkB,MAAA,EAAAc,GAAA,CAAAlC,IAAA;QACA;MACA,GAAAoC,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAC,IAAA,6BAAAC,MAAA,CAAApB,MAAA,mDAAAiB,KAAA;MACA,GAAAI,OAAA;QACAX,MAAA,CAAA1B,YAAA,CAAAsC,MAAA,CAAAtB,MAAA;MACA;IACA;IAEA,iBACAuB,kBAAA,WAAAA,mBAAAvB,MAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,QAAA,QAAA3C,YAAA,CAAAkB,MAAA;MACA,KAAAyB,QAAA;MAEAP,OAAA,CAAAQ,GAAA,UAAA1B,MAAA,EAAAyB,QAAA;;MAEA;MACA,IAAAE,YAAA;MAEA,IAAAC,YAAA;MACAC,MAAA,CAAAC,IAAA,CAAAL,QAAA,EAAA9B,OAAA,WAAAoC,GAAA;QACA,IAAAC,KAAA,GAAAP,QAAA,CAAAM,GAAA;;QAEA;QACA,IAAAJ,YAAA,CAAAM,QAAA,CAAAF,GAAA;UACA;QACA;;QAEA;QACA,IAAAC,KAAA,aAAAA,KAAA,KAAAE,SAAA,IAAAF,KAAA;UACA,IAAAG,KAAA,GAAAX,MAAA,CAAAY,aAAA,CAAAL,GAAA,EAAAN,QAAA,CAAAY,QAAA;UACAT,YAAA,CAAAO,KAAA,IAAAH,KAAA;QACA;MACA;MAEAd,OAAA,CAAAQ,GAAA,YAAAE,YAAA;MACA,OAAAA,YAAA;IACA;IAEA,aACAQ,aAAA,WAAAA,cAAAE,QAAA,EAAAD,QAAA;MACA,KAAAA,QAAA;QACA,OAAAC,QAAA;MACA;;MAEA;MACA,IAAAC,OAAA;MAEA,IAAAF,QAAA,CAAAG,UAAA;QACAD,OAAA,GAAAF,QAAA,CAAAG,UAAA;MACA,WAAAH,QAAA,CAAAI,UAAA,IAAAJ,QAAA,CAAAI,UAAA,CAAAD,UAAA;QACAD,OAAA,GAAAF,QAAA,CAAAI,UAAA,CAAAD,UAAA;MACA,WAAAhE,KAAA,CAAAkE,OAAA,CAAAL,QAAA;QACAE,OAAA,GAAAF,QAAA;MACA;MAEAnB,OAAA,CAAAQ,GAAA,YAAAY,QAAA,eAAAC,OAAA;;MAEA;MACA,IAAAI,MAAA,GAAAJ,OAAA,CAAAK,IAAA,WAAAC,CAAA;QACA,IAAAA,CAAA,CAAAC,OAAA,IAAAD,CAAA,CAAAC,OAAA,CAAA5E,IAAA,KAAAoE,QAAA;QACA,IAAAO,CAAA,CAAAE,UAAA,IAAAF,CAAA,CAAAE,UAAA,CAAAC,MAAA,KAAAV,QAAA;QACA,IAAAO,CAAA,CAAAI,UAAA,KAAAX,QAAA;QACA;MACA;MAEA,IAAAK,MAAA;QAAA,IAAAO,eAAA,EAAAC,kBAAA;QACA;QACA,IAAAhB,KAAA,KAAAe,eAAA,GAAAP,MAAA,CAAAG,OAAA,cAAAI,eAAA,uBAAAA,eAAA,CAAAf,KAAA,OAAAgB,kBAAA,GACAR,MAAA,CAAAI,UAAA,cAAAI,kBAAA,uBAAAA,kBAAA,CAAAhB,KAAA,KACAQ,MAAA,CAAAR,KAAA,IACAG,QAAA;QACApB,OAAA,CAAAQ,GAAA,UAAAY,QAAA,QAAAH,KAAA;QACA,OAAAA,KAAA;MACA;MAEA,OAAAG,QAAA;IACA;IAEA,aACAc,gBAAA,WAAAA,iBAAApB,KAAA;MACA,IAAAxD,KAAA,CAAAkE,OAAA,CAAAV,KAAA;QACA,OAAAA,KAAA,CAAAqB,IAAA;MACA;MACA,QAAAC,QAAA,CAAA7E,OAAA,EAAAuD,KAAA;QACA,OAAAuB,IAAA,CAAAC,SAAA,CAAAxB,KAAA;MACA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA;MACA;MACA,OAAAyB,MAAA,CAAAzB,KAAA;IACA;IAEA,aACA0B,YAAA,WAAAA,aAAA1B,KAAA;MACA,IAAA2B,QAAA,QAAAP,gBAAA,CAAApB,KAAA;MACA;MACA,OAAA2B,QAAA,CAAApE,MAAA;IACA;IAEA,cACAqE,aAAA,WAAAA,cAAA5B,KAAA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA;MACA;MACA,WAAAA,KAAA;QACA;MACA;MACA,IAAAxD,KAAA,CAAAkE,OAAA,CAAAV,KAAA;QACA;MACA;MACA;IACA;IAEA,eACA6B,cAAA,WAAAA,eAAA7D,MAAA;MACA,IAAA8D,WAAA,QAAA5E,SAAA,CAAAc,MAAA;MACA,KAAAe,IAAA,MAAA7B,SAAA,EAAAc,MAAA,EAAA8D,WAAA;IACA;IAEA,eACAC,iBAAA,WAAAA,kBAAAhE,MAAA;MAAA,IAAAiE,MAAA;MACA;MACA,SAAA7E,eAAA,CAAAI,MAAA;QACA,KAAA0E,mBAAA;MACA;;MAEA;MACA,IAAAC,MAAA,QAAA/E,eAAA,CAAAyD,IAAA,WAAAsB,MAAA;QAAA,OACAA,MAAA,CAAAC,QAAA,KAAApE,MAAA,CAAAqE,QAAA,IACAF,MAAA,CAAAG,QAAA,KAAAL,MAAA,CAAAM,qBAAA,CAAAvE,MAAA;MAAA,CACA;MAEA,OAAAmE,MAAA,GAAAA,MAAA,CAAAK,MAAA;IACA;IAEA,iBACAD,qBAAA,WAAAA,sBAAAvE,MAAA;MACA;MACA,IAAAqE,QAAA,GAAArE,MAAA,CAAAqE,QAAA;MACA,IAAAA,QAAA,CAAAnC,QAAA,UAAAmC,QAAA,CAAAnC,QAAA;QACA;MACA,WAAAmC,QAAA,CAAAnC,QAAA,UAAAmC,QAAA,CAAAnC,QAAA;QACA;MACA,WAAAmC,QAAA,CAAAnC,QAAA;QACA;MACA;MACA;IACA;IAEA,eACAgC,mBAAA,WAAAA,oBAAA;MACA;QACA,IAAAO,KAAA,GAAAC,YAAA,CAAAC,OAAA;QACA,IAAAF,KAAA;UACA,IAAAN,MAAA,GAAAX,IAAA,CAAAoB,KAAA,CAAAH,KAAA;UACA,KAAArF,eAAA,GAAA+E,MAAA,CAAAU,SAAA;QACA;MACA,SAAA3D,KAAA;QACAC,OAAA,CAAAC,IAAA,gBAAAF,KAAA;QACA,KAAA9B,eAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}