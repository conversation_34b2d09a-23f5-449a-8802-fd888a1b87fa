{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752407982321}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_definition", "require", "name", "props", "flowRecordList", "type", "Array", "default", "defaultExpanded", "Boolean", "data", "activeHistoryNames", "nodeFormData", "formKeys", "loadingNodes", "Set", "watch", "handler", "newVal", "length", "loadAllNodeForms", "immediate", "_this", "for<PERSON>ach", "index", "parseInt", "replace", "record", "taskId", "has", "loadNodeForm", "methods", "getHistoryIcon", "finishTime", "getHistoryColor", "getStatusTagType", "getStatusText", "_this2", "_this3", "add", "getProcessVariables", "then", "res", "formJson", "$set", "Date", "now", "$nextTick", "formRef", "$refs", "concat", "set<PERSON><PERSON><PERSON><PERSON>", "setFormData", "disableForm", "catch", "error", "console", "warn", "finally", "delete"], "sources": ["src/components/FlowHistory/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <!-- 节点表单 -->\n          <div v-if=\"record.taskId && nodeFormData[record.taskId]\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> 节点表单\n            </h5>\n            <div class=\"form-container\">\n              <v-form-render\n                :ref=\"`nodeForm_${record.taskId}`\"\n                :key=\"`form_${record.taskId}_${formKeys[record.taskId] || 0}`\"\n              />\n            </div>\n          </div>\n\n          <!-- 办理信息 -->\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nimport { getProcessVariables } from '@/api/flowable/definition'\n\nexport default {\n  name: 'FlowHistory',\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: [],\n      nodeFormData: {}, // 存储每个节点的表单数据\n      formKeys: {}, // 存储每个表单的key，用于强制重新渲染\n      loadingNodes: new Set() // 正在加载的节点\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0) {\n          if (this.defaultExpanded) {\n            // 默认展开第一个节点\n            this.activeHistoryNames = ['history-0'];\n          }\n          // 加载所有节点的表单数据\n          this.loadAllNodeForms();\n        }\n      },\n      immediate: true\n    },\n    activeHistoryNames: {\n      handler(newVal) {\n        // 当展开节点时，加载对应的表单数据\n        newVal.forEach(name => {\n          const index = parseInt(name.replace('history-', ''));\n          const record = this.flowRecordList[index];\n          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n            this.loadNodeForm(record.taskId);\n          }\n        });\n      }\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    },\n\n    /** 加载所有节点的表单数据 */\n    loadAllNodeForms() {\n      this.flowRecordList.forEach(record => {\n        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n          this.loadNodeForm(record.taskId);\n        }\n      });\n    },\n\n    /** 加载单个节点的表单数据 */\n    loadNodeForm(taskId) {\n      if (!taskId || this.loadingNodes.has(taskId)) {\n        return;\n      }\n\n      this.loadingNodes.add(taskId);\n\n      getProcessVariables(taskId).then(res => {\n        if (res.data && res.data.formJson) {\n          // 设置表单数据\n          this.$set(this.nodeFormData, taskId, res.data);\n          this.$set(this.formKeys, taskId, Date.now());\n\n          this.$nextTick(() => {\n            const formRef = this.$refs[`nodeForm_${taskId}`];\n            if (formRef && formRef[0]) {\n              // 设置表单JSON\n              formRef[0].setFormJson(res.data.formJson);\n              this.$nextTick(() => {\n                // 设置表单数据\n                formRef[0].setFormData(res.data);\n                this.$nextTick(() => {\n                  // 禁用表单\n                  formRef[0].disableForm();\n                });\n              });\n            }\n          });\n        }\n      }).catch(error => {\n        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);\n      }).finally(() => {\n        this.loadingNodes.delete(taskId);\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n\n/* 节点表单样式 */\n.node-form-section {\n  margin-bottom: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-section-title {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #606266;\n  border-bottom: 1px solid #E4E7ED;\n}\n\n.form-section-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n.form-container {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAwEA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,cAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,eAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;MACAC,YAAA;MAAA;MACAC,QAAA;MAAA;MACAC,YAAA,MAAAC,GAAA;IACA;EACA;EACAC,KAAA;IACAZ,cAAA;MACAa,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,MAAA;UACA,SAAAX,eAAA;YACA;YACA,KAAAG,kBAAA;UACA;UACA;UACA,KAAAS,gBAAA;QACA;MACA;MACAC,SAAA;IACA;IACAV,kBAAA;MACAM,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAI,KAAA;QACA;QACAJ,MAAA,CAAAK,OAAA,WAAArB,IAAA;UACA,IAAAsB,KAAA,GAAAC,QAAA,CAAAvB,IAAA,CAAAwB,OAAA;UACA,IAAAC,MAAA,GAAAL,KAAA,CAAAlB,cAAA,CAAAoB,KAAA;UACA,IAAAG,MAAA,IAAAA,MAAA,CAAAC,MAAA,KAAAN,KAAA,CAAAV,YAAA,CAAAe,MAAA,CAAAC,MAAA,MAAAN,KAAA,CAAAR,YAAA,CAAAe,GAAA,CAAAF,MAAA,CAAAC,MAAA;YACAN,KAAA,CAAAQ,YAAA,CAAAH,MAAA,CAAAC,MAAA;UACA;QACA;MACA;IACA;EACA;EACAG,OAAA;IACA,eACAC,cAAA,WAAAA,eAAAL,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAC,eAAA,WAAAA,gBAAAP,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAE,gBAAA,WAAAA,iBAAAR,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,aACAG,aAAA,WAAAA,cAAAT,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,kBACAb,gBAAA,WAAAA,iBAAA;MAAA,IAAAiB,MAAA;MACA,KAAAjC,cAAA,CAAAmB,OAAA,WAAAI,MAAA;QACA,IAAAA,MAAA,CAAAC,MAAA,KAAAS,MAAA,CAAAzB,YAAA,CAAAe,MAAA,CAAAC,MAAA,MAAAS,MAAA,CAAAvB,YAAA,CAAAe,GAAA,CAAAF,MAAA,CAAAC,MAAA;UACAS,MAAA,CAAAP,YAAA,CAAAH,MAAA,CAAAC,MAAA;QACA;MACA;IACA;IAEA,kBACAE,YAAA,WAAAA,aAAAF,MAAA;MAAA,IAAAU,MAAA;MACA,KAAAV,MAAA,SAAAd,YAAA,CAAAe,GAAA,CAAAD,MAAA;QACA;MACA;MAEA,KAAAd,YAAA,CAAAyB,GAAA,CAAAX,MAAA;MAEA,IAAAY,+BAAA,EAAAZ,MAAA,EAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAhC,IAAA,IAAAgC,GAAA,CAAAhC,IAAA,CAAAiC,QAAA;UACA;UACAL,MAAA,CAAAM,IAAA,CAAAN,MAAA,CAAA1B,YAAA,EAAAgB,MAAA,EAAAc,GAAA,CAAAhC,IAAA;UACA4B,MAAA,CAAAM,IAAA,CAAAN,MAAA,CAAAzB,QAAA,EAAAe,MAAA,EAAAiB,IAAA,CAAAC,GAAA;UAEAR,MAAA,CAAAS,SAAA;YACA,IAAAC,OAAA,GAAAV,MAAA,CAAAW,KAAA,aAAAC,MAAA,CAAAtB,MAAA;YACA,IAAAoB,OAAA,IAAAA,OAAA;cACA;cACAA,OAAA,IAAAG,WAAA,CAAAT,GAAA,CAAAhC,IAAA,CAAAiC,QAAA;cACAL,MAAA,CAAAS,SAAA;gBACA;gBACAC,OAAA,IAAAI,WAAA,CAAAV,GAAA,CAAAhC,IAAA;gBACA4B,MAAA,CAAAS,SAAA;kBACA;kBACAC,OAAA,IAAAK,WAAA;gBACA;cACA;YACA;UACA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAC,IAAA,6BAAAP,MAAA,CAAAtB,MAAA,mDAAA2B,KAAA;MACA,GAAAG,OAAA;QACApB,MAAA,CAAAxB,YAAA,CAAA6C,MAAA,CAAA/B,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}