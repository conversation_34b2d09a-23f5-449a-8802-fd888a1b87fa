{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752411162863}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_definition", "require", "name", "props", "flowRecordList", "type", "Array", "default", "defaultExpanded", "Boolean", "data", "activeHistoryNames", "nodeFormData", "loadingNodes", "Set", "nodeVFormConfigs", "vformKeys", "watch", "handler", "newVal", "length", "loadAllNodeForms", "immediate", "_this", "for<PERSON>ach", "index", "parseInt", "replace", "record", "taskId", "has", "loadNodeForm", "methods", "getHistoryIcon", "finishTime", "getHistoryColor", "getStatusTagType", "getStatusText", "_this2", "_this3", "add", "getProcessVariables", "then", "res", "$set", "catch", "error", "console", "warn", "concat", "finally", "delete", "getFormDataDisplay", "_this4", "nodeData", "log", "systemFields", "filteredData", "Object", "keys", "key", "value", "includes", "undefined", "label", "getFieldLabel", "formJson", "<PERSON><PERSON><PERSON>", "widgets", "widgetList", "formConfig", "isArray", "widget", "find", "w", "options", "__config__", "formId", "__vModel__", "_widget$options", "_widget$__config__", "formatFieldValue", "join", "_typeof2", "JSON", "stringify", "String", "getFieldSpan", "valueStr", "getFieldClass", "getNodeVFormConfig", "_this5", "loadNodeVFormConfigs", "config", "nodeKey", "taskDefKey", "nodeName", "taskName", "matchNodeByType", "nodeTypeMap", "keywords", "nodeType", "some", "keyword", "saved", "localStorage", "getItem", "parse", "nodeForms"], "sources": ["src/components/FlowHistory/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <!-- 节点VForm表单 -->\n          <div v-if=\"getNodeVFormConfig(record)\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> {{ record.taskName }} - 专属表单\n            </h5>\n            <div class=\"vform-container\">\n              <v-form-render\n                :ref=\"`nodeVForm_${record.taskId}`\"\n                :key=\"`vform_${record.taskId}_${vformKeys[record.taskId] || 0}`\"\n              />\n            </div>\n          </div>\n\n          <!-- 通用数据视图（当没有专属表单时） -->\n          <div v-else-if=\"record.taskId && nodeFormData[record.taskId]\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> {{ record.taskName }} - 数据记录\n            </h5>\n            <div class=\"form-data-container\">\n              <el-descriptions :column=\"2\" size=\"small\" border class=\"form-data-descriptions\">\n                <el-descriptions-item\n                  v-for=\"(value, key) in getFormDataDisplay(record.taskId)\"\n                  :key=\"key\"\n                  :label=\"key\"\n                  :span=\"getFieldSpan(value)\"\n                >\n                  <div class=\"form-field-value\" :class=\"getFieldClass(value)\">\n                    {{ formatFieldValue(value) }}\n                  </div>\n                </el-descriptions-item>\n              </el-descriptions>\n              <div v-if=\"Object.keys(getFormDataDisplay(record.taskId)).length === 0\" class=\"no-data\">\n                暂无表单数据\n              </div>\n            </div>\n          </div>\n\n          <!-- 办理信息 -->\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nimport { getProcessVariables } from '@/api/flowable/definition'\n\nexport default {\n  name: 'FlowHistory',\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: [],\n      nodeFormData: {}, // 存储每个节点的表单数据\n      loadingNodes: new Set(), // 正在加载的节点\n      nodeVFormConfigs: [], // 节点VForm配置\n      vformKeys: {} // VForm组件的key，用于强制重新渲染\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0) {\n          if (this.defaultExpanded) {\n            // 默认展开第一个节点\n            this.activeHistoryNames = ['history-0'];\n          }\n          // 加载所有节点的表单数据\n          this.loadAllNodeForms();\n        }\n      },\n      immediate: true\n    },\n    activeHistoryNames: {\n      handler(newVal) {\n        // 当展开节点时，加载对应的表单数据\n        newVal.forEach(name => {\n          const index = parseInt(name.replace('history-', ''));\n          const record = this.flowRecordList[index];\n          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n            this.loadNodeForm(record.taskId);\n          }\n        });\n      }\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    },\n\n    /** 加载所有节点的表单数据 */\n    loadAllNodeForms() {\n      this.flowRecordList.forEach(record => {\n        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n          this.loadNodeForm(record.taskId);\n        }\n      });\n    },\n\n    /** 加载单个节点的表单数据 */\n    loadNodeForm(taskId) {\n      if (!taskId || this.loadingNodes.has(taskId)) {\n        return;\n      }\n\n      this.loadingNodes.add(taskId);\n\n      getProcessVariables(taskId).then(res => {\n        if (res.data) {\n          // 设置表单数据\n          this.$set(this.nodeFormData, taskId, res.data);\n        }\n      }).catch(error => {\n        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);\n      }).finally(() => {\n        this.loadingNodes.delete(taskId);\n      });\n    },\n\n    /** 获取表单数据用于显示 */\n    getFormDataDisplay(taskId) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return {};\n\n      console.log('节点数据:', taskId, nodeData); // 调试信息\n\n      // 系统字段列表\n      const systemFields = ['formJson', 'taskId', 'procInsId', 'deployId', 'procDefId', 'instanceId'];\n\n      const filteredData = {};\n      Object.keys(nodeData).forEach(key => {\n        const value = nodeData[key];\n\n        // 跳过系统字段\n        if (systemFields.includes(key)) {\n          return;\n        }\n\n        // 包含所有非空值，包括数字0、false等有意义的值\n        if (value !== null && value !== undefined && value !== '') {\n          const label = this.getFieldLabel(key, nodeData.formJson);\n          filteredData[label] = value;\n        }\n      });\n\n      console.log('过滤后的数据:', filteredData); // 调试信息\n      return filteredData;\n    },\n\n    /** 获取字段标签 */\n    getFieldLabel(fieldKey, formJson) {\n      if (!formJson) {\n        return fieldKey;\n      }\n\n      // 尝试多种可能的表单结构\n      let widgets = [];\n\n      if (formJson.widgetList) {\n        widgets = formJson.widgetList;\n      } else if (formJson.formConfig && formJson.formConfig.widgetList) {\n        widgets = formJson.formConfig.widgetList;\n      } else if (Array.isArray(formJson)) {\n        widgets = formJson;\n      }\n\n      console.log('查找字段标签:', fieldKey, '在widgets:', widgets); // 调试信息\n\n      // 在表单组件中查找字段标签\n      const widget = widgets.find(w => {\n        if (w.options && w.options.name === fieldKey) return true;\n        if (w.__config__ && w.__config__.formId === fieldKey) return true;\n        if (w.__vModel__ === fieldKey) return true;\n        return false;\n      });\n\n      if (widget) {\n        // 尝试多种可能的标签字段\n        const label = widget.options?.label ||\n                     widget.__config__?.label ||\n                     widget.label ||\n                     fieldKey;\n        console.log('找到标签:', fieldKey, '->', label); // 调试信息\n        return label;\n      }\n\n      return fieldKey;\n    },\n\n    /** 格式化字段值 */\n    formatFieldValue(value) {\n      if (Array.isArray(value)) {\n        return value.join(', ');\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      if (typeof value === 'boolean') {\n        return value ? '是' : '否';\n      }\n      return String(value);\n    },\n\n    /** 获取字段跨度 */\n    getFieldSpan(value) {\n      const valueStr = this.formatFieldValue(value);\n      // 长文本占用两列\n      return valueStr.length > 20 ? 2 : 1;\n    },\n\n    /** 获取字段样式类 */\n    getFieldClass(value) {\n      if (typeof value === 'boolean') {\n        return value ? 'field-boolean-true' : 'field-boolean-false';\n      }\n      if (typeof value === 'number') {\n        return 'field-number';\n      }\n      if (Array.isArray(value)) {\n        return 'field-array';\n      }\n      return 'field-text';\n    },\n\n    /** 获取节点VForm配置 */\n    getNodeVFormConfig(record) {\n      // 从本地存储加载VForm配置\n      if (this.nodeVFormConfigs.length === 0) {\n        this.loadNodeVFormConfigs();\n      }\n\n      // 根据节点标识或任务名称查找对应的VForm配置\n      const config = this.nodeVFormConfigs.find(config =>\n        config.nodeKey === record.taskDefKey ||\n        config.nodeName === record.taskName ||\n        this.matchNodeByType(config, record)\n      );\n\n      return config;\n    },\n\n    /** 根据类型匹配节点 */\n    matchNodeByType(config, record) {\n      const taskName = record.taskName || '';\n      const nodeTypeMap = {\n        'npi_apply': ['申请', 'NPI申请'],\n        'tech_review': ['技术评审', '技术审核'],\n        'process_review': ['工艺评审', '工艺审核'],\n        'quality_review': ['质量评审', '质量审核'],\n        'cost_review': ['成本评审', '成本审核'],\n        'final_approval': ['最终审批', '终审']\n      };\n\n      const keywords = nodeTypeMap[config.nodeType] || [];\n      return keywords.some(keyword => taskName.includes(keyword));\n    },\n\n    /** 加载节点VForm配置 */\n    loadNodeVFormConfigs() {\n      try {\n        // 加载NPI流程的VForm配置\n        const saved = localStorage.getItem('node_vform_config_npi_process');\n        if (saved) {\n          const config = JSON.parse(saved);\n          this.nodeVFormConfigs = config.nodeForms || [];\n        }\n      } catch (error) {\n        console.warn('加载节点VForm配置失败:', error);\n        this.nodeVFormConfigs = [];\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n\n/* 节点表单数据样式 */\n.node-form-section {\n  margin-bottom: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-section-title {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #606266;\n  border-bottom: 1px solid #E4E7ED;\n}\n\n.form-section-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n.form-data-container {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n.form-data-descriptions {\n  background-color: white;\n}\n\n/* 表单字段值样式 */\n.form-field-value {\n  word-break: break-word;\n  line-height: 1.5;\n}\n\n.field-boolean-true {\n  color: #67C23A;\n  font-weight: 600;\n}\n\n.field-boolean-false {\n  color: #F56C6C;\n  font-weight: 600;\n}\n\n.field-number {\n  color: #E6A23C;\n  font-weight: 500;\n}\n\n.field-array {\n  color: #909399;\n  font-style: italic;\n}\n\n.field-text {\n  color: #606266;\n}\n\n/* 原始数据显示样式 */\n.raw-data-container {\n  background-color: #f8f8f8;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 12px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.raw-data-container pre {\n  margin: 0;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  color: #333;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.no-data {\n  text-align: center;\n  color: #909399;\n  padding: 20px;\n  font-style: italic;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,cAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,eAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;MACAC,YAAA;MAAA;MACAC,YAAA,MAAAC,GAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAb,cAAA;MACAc,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,MAAA;UACA,SAAAZ,eAAA;YACA;YACA,KAAAG,kBAAA;UACA;UACA;UACA,KAAAU,gBAAA;QACA;MACA;MACAC,SAAA;IACA;IACAX,kBAAA;MACAO,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAI,KAAA;QACA;QACAJ,MAAA,CAAAK,OAAA,WAAAtB,IAAA;UACA,IAAAuB,KAAA,GAAAC,QAAA,CAAAxB,IAAA,CAAAyB,OAAA;UACA,IAAAC,MAAA,GAAAL,KAAA,CAAAnB,cAAA,CAAAqB,KAAA;UACA,IAAAG,MAAA,IAAAA,MAAA,CAAAC,MAAA,KAAAN,KAAA,CAAAX,YAAA,CAAAgB,MAAA,CAAAC,MAAA,MAAAN,KAAA,CAAAV,YAAA,CAAAiB,GAAA,CAAAF,MAAA,CAAAC,MAAA;YACAN,KAAA,CAAAQ,YAAA,CAAAH,MAAA,CAAAC,MAAA;UACA;QACA;MACA;IACA;EACA;EACAG,OAAA;IACA,eACAC,cAAA,WAAAA,eAAAL,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAC,eAAA,WAAAA,gBAAAP,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAE,gBAAA,WAAAA,iBAAAR,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,aACAG,aAAA,WAAAA,cAAAT,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,kBACAb,gBAAA,WAAAA,iBAAA;MAAA,IAAAiB,MAAA;MACA,KAAAlC,cAAA,CAAAoB,OAAA,WAAAI,MAAA;QACA,IAAAA,MAAA,CAAAC,MAAA,KAAAS,MAAA,CAAA1B,YAAA,CAAAgB,MAAA,CAAAC,MAAA,MAAAS,MAAA,CAAAzB,YAAA,CAAAiB,GAAA,CAAAF,MAAA,CAAAC,MAAA;UACAS,MAAA,CAAAP,YAAA,CAAAH,MAAA,CAAAC,MAAA;QACA;MACA;IACA;IAEA,kBACAE,YAAA,WAAAA,aAAAF,MAAA;MAAA,IAAAU,MAAA;MACA,KAAAV,MAAA,SAAAhB,YAAA,CAAAiB,GAAA,CAAAD,MAAA;QACA;MACA;MAEA,KAAAhB,YAAA,CAAA2B,GAAA,CAAAX,MAAA;MAEA,IAAAY,+BAAA,EAAAZ,MAAA,EAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAjC,IAAA;UACA;UACA6B,MAAA,CAAAK,IAAA,CAAAL,MAAA,CAAA3B,YAAA,EAAAiB,MAAA,EAAAc,GAAA,CAAAjC,IAAA;QACA;MACA,GAAAmC,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAC,IAAA,6BAAAC,MAAA,CAAApB,MAAA,mDAAAiB,KAAA;MACA,GAAAI,OAAA;QACAX,MAAA,CAAA1B,YAAA,CAAAsC,MAAA,CAAAtB,MAAA;MACA;IACA;IAEA,iBACAuB,kBAAA,WAAAA,mBAAAvB,MAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,QAAA,QAAA1C,YAAA,CAAAiB,MAAA;MACA,KAAAyB,QAAA;MAEAP,OAAA,CAAAQ,GAAA,UAAA1B,MAAA,EAAAyB,QAAA;;MAEA;MACA,IAAAE,YAAA;MAEA,IAAAC,YAAA;MACAC,MAAA,CAAAC,IAAA,CAAAL,QAAA,EAAA9B,OAAA,WAAAoC,GAAA;QACA,IAAAC,KAAA,GAAAP,QAAA,CAAAM,GAAA;;QAEA;QACA,IAAAJ,YAAA,CAAAM,QAAA,CAAAF,GAAA;UACA;QACA;;QAEA;QACA,IAAAC,KAAA,aAAAA,KAAA,KAAAE,SAAA,IAAAF,KAAA;UACA,IAAAG,KAAA,GAAAX,MAAA,CAAAY,aAAA,CAAAL,GAAA,EAAAN,QAAA,CAAAY,QAAA;UACAT,YAAA,CAAAO,KAAA,IAAAH,KAAA;QACA;MACA;MAEAd,OAAA,CAAAQ,GAAA,YAAAE,YAAA;MACA,OAAAA,YAAA;IACA;IAEA,aACAQ,aAAA,WAAAA,cAAAE,QAAA,EAAAD,QAAA;MACA,KAAAA,QAAA;QACA,OAAAC,QAAA;MACA;;MAEA;MACA,IAAAC,OAAA;MAEA,IAAAF,QAAA,CAAAG,UAAA;QACAD,OAAA,GAAAF,QAAA,CAAAG,UAAA;MACA,WAAAH,QAAA,CAAAI,UAAA,IAAAJ,QAAA,CAAAI,UAAA,CAAAD,UAAA;QACAD,OAAA,GAAAF,QAAA,CAAAI,UAAA,CAAAD,UAAA;MACA,WAAA/D,KAAA,CAAAiE,OAAA,CAAAL,QAAA;QACAE,OAAA,GAAAF,QAAA;MACA;MAEAnB,OAAA,CAAAQ,GAAA,YAAAY,QAAA,eAAAC,OAAA;;MAEA;MACA,IAAAI,MAAA,GAAAJ,OAAA,CAAAK,IAAA,WAAAC,CAAA;QACA,IAAAA,CAAA,CAAAC,OAAA,IAAAD,CAAA,CAAAC,OAAA,CAAAzE,IAAA,KAAAiE,QAAA;QACA,IAAAO,CAAA,CAAAE,UAAA,IAAAF,CAAA,CAAAE,UAAA,CAAAC,MAAA,KAAAV,QAAA;QACA,IAAAO,CAAA,CAAAI,UAAA,KAAAX,QAAA;QACA;MACA;MAEA,IAAAK,MAAA;QAAA,IAAAO,eAAA,EAAAC,kBAAA;QACA;QACA,IAAAhB,KAAA,KAAAe,eAAA,GAAAP,MAAA,CAAAG,OAAA,cAAAI,eAAA,uBAAAA,eAAA,CAAAf,KAAA,OAAAgB,kBAAA,GACAR,MAAA,CAAAI,UAAA,cAAAI,kBAAA,uBAAAA,kBAAA,CAAAhB,KAAA,KACAQ,MAAA,CAAAR,KAAA,IACAG,QAAA;QACApB,OAAA,CAAAQ,GAAA,UAAAY,QAAA,QAAAH,KAAA;QACA,OAAAA,KAAA;MACA;MAEA,OAAAG,QAAA;IACA;IAEA,aACAc,gBAAA,WAAAA,iBAAApB,KAAA;MACA,IAAAvD,KAAA,CAAAiE,OAAA,CAAAV,KAAA;QACA,OAAAA,KAAA,CAAAqB,IAAA;MACA;MACA,QAAAC,QAAA,CAAA5E,OAAA,EAAAsD,KAAA;QACA,OAAAuB,IAAA,CAAAC,SAAA,CAAAxB,KAAA;MACA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA;MACA;MACA,OAAAyB,MAAA,CAAAzB,KAAA;IACA;IAEA,aACA0B,YAAA,WAAAA,aAAA1B,KAAA;MACA,IAAA2B,QAAA,QAAAP,gBAAA,CAAApB,KAAA;MACA;MACA,OAAA2B,QAAA,CAAApE,MAAA;IACA;IAEA,cACAqE,aAAA,WAAAA,cAAA5B,KAAA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA;MACA;MACA,WAAAA,KAAA;QACA;MACA;MACA,IAAAvD,KAAA,CAAAiE,OAAA,CAAAV,KAAA;QACA;MACA;MACA;IACA;IAEA,kBACA6B,kBAAA,WAAAA,mBAAA9D,MAAA;MAAA,IAAA+D,MAAA;MACA;MACA,SAAA5E,gBAAA,CAAAK,MAAA;QACA,KAAAwE,oBAAA;MACA;;MAEA;MACA,IAAAC,MAAA,QAAA9E,gBAAA,CAAA0D,IAAA,WAAAoB,MAAA;QAAA,OACAA,MAAA,CAAAC,OAAA,KAAAlE,MAAA,CAAAmE,UAAA,IACAF,MAAA,CAAAG,QAAA,KAAApE,MAAA,CAAAqE,QAAA,IACAN,MAAA,CAAAO,eAAA,CAAAL,MAAA,EAAAjE,MAAA;MAAA,CACA;MAEA,OAAAiE,MAAA;IACA;IAEA,eACAK,eAAA,WAAAA,gBAAAL,MAAA,EAAAjE,MAAA;MACA,IAAAqE,QAAA,GAAArE,MAAA,CAAAqE,QAAA;MACA,IAAAE,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,IAAAC,QAAA,GAAAD,WAAA,CAAAN,MAAA,CAAAQ,QAAA;MACA,OAAAD,QAAA,CAAAE,IAAA,WAAAC,OAAA;QAAA,OAAAN,QAAA,CAAAnC,QAAA,CAAAyC,OAAA;MAAA;IACA;IAEA,kBACAX,oBAAA,WAAAA,qBAAA;MACA;QACA;QACA,IAAAY,KAAA,GAAAC,YAAA,CAAAC,OAAA;QACA,IAAAF,KAAA;UACA,IAAAX,MAAA,GAAAT,IAAA,CAAAuB,KAAA,CAAAH,KAAA;UACA,KAAAzF,gBAAA,GAAA8E,MAAA,CAAAe,SAAA;QACA;MACA,SAAA9D,KAAA;QACAC,OAAA,CAAAC,IAAA,mBAAAF,KAAA;QACA,KAAA/B,gBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}