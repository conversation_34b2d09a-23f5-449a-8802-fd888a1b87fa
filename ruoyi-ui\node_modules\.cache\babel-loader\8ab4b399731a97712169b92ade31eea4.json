{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FlowHistory\\index.vue", "mtime": 1752412939715}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9SdW9ZaS1mbG93YWJsZS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF90eXBlb2YyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9SdW9ZaS1mbG93YWJsZS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy90eXBlb2YuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuam9pbi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5qc29uLnN0cmluZ2lmeS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnNldC5kaWZmZXJlbmNlLnYyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuaW50ZXJzZWN0aW9uLnYyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuaXMtZGlzam9pbnQtZnJvbS52Mi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmlzLXN1YnNldC1vZi52Mi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmlzLXN1cGVyc2V0LW9mLnYyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuc3ltbWV0cmljLWRpZmZlcmVuY2UudjIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnNldC51bmlvbi52Mi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5yZXBsYWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maW5kLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5zb21lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIik7CnZhciBfZGVmaW5pdGlvbiA9IHJlcXVpcmUoIkAvYXBpL2Zsb3dhYmxlL2RlZmluaXRpb24iKTsKdmFyIF9GaWVsZFJlbmRlcmVyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL2NvbXBvbmVudHMvRmllbGRSZW5kZXJlciIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0MiA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnRmxvd0hpc3RvcnknLAogIGNvbXBvbmVudHM6IHsKICAgIEZpZWxkUmVuZGVyZXI6IF9GaWVsZFJlbmRlcmVyLmRlZmF1bHQKICB9LAogIHByb3BzOiB7CiAgICBmbG93UmVjb3JkTGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9LAogICAgZGVmYXVsdEV4cGFuZGVkOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IHRydWUKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhY3RpdmVIaXN0b3J5TmFtZXM6IFtdLAogICAgICBub2RlRm9ybURhdGE6IHt9LAogICAgICAvLyDlrZjlgqjmr4/kuKroioLngrnnmoTooajljZXmlbDmja4KICAgICAgbG9hZGluZ05vZGVzOiBuZXcgU2V0KCkgLy8g5q2j5Zyo5Yqg6L2955qE6IqC54K5CiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIGZsb3dSZWNvcmRMaXN0OiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgaWYgKG5ld1ZhbCAmJiBuZXdWYWwubGVuZ3RoID4gMCkgewogICAgICAgICAgaWYgKHRoaXMuZGVmYXVsdEV4cGFuZGVkKSB7CiAgICAgICAgICAgIC8vIOm7mOiupOWxleW8gOesrOS4gOS4quiKgueCuQogICAgICAgICAgICB0aGlzLmFjdGl2ZUhpc3RvcnlOYW1lcyA9IFsnaGlzdG9yeS0wJ107CiAgICAgICAgICB9CiAgICAgICAgICAvLyDliqDovb3miYDmnInoioLngrnnmoTooajljZXmlbDmja4KICAgICAgICAgIHRoaXMubG9hZEFsbE5vZGVGb3JtcygpOwogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9LAogICAgYWN0aXZlSGlzdG9yeU5hbWVzOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgICAvLyDlvZPlsZXlvIDoioLngrnml7bvvIzliqDovb3lr7nlupTnmoTooajljZXmlbDmja4KICAgICAgICBuZXdWYWwuZm9yRWFjaChmdW5jdGlvbiAobmFtZSkgewogICAgICAgICAgdmFyIGluZGV4ID0gcGFyc2VJbnQobmFtZS5yZXBsYWNlKCdoaXN0b3J5LScsICcnKSk7CiAgICAgICAgICB2YXIgcmVjb3JkID0gX3RoaXMuZmxvd1JlY29yZExpc3RbaW5kZXhdOwogICAgICAgICAgaWYgKHJlY29yZCAmJiByZWNvcmQudGFza0lkICYmICFfdGhpcy5ub2RlRm9ybURhdGFbcmVjb3JkLnRhc2tJZF0gJiYgIV90aGlzLmxvYWRpbmdOb2Rlcy5oYXMocmVjb3JkLnRhc2tJZCkpIHsKICAgICAgICAgICAgX3RoaXMubG9hZE5vZGVGb3JtKHJlY29yZC50YXNrSWQpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog6I635Y+W5Y6G5Y+y6IqC54K55Zu+5qCHICovZ2V0SGlzdG9yeUljb246IGZ1bmN0aW9uIGdldEhpc3RvcnlJY29uKHJlY29yZCkgewogICAgICBpZiAocmVjb3JkLmZpbmlzaFRpbWUpIHsKICAgICAgICByZXR1cm4gJ2VsLWljb24tY2hlY2snOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnZWwtaWNvbi10aW1lJzsKICAgICAgfQogICAgfSwKICAgIC8qKiDojrflj5bljoblj7LoioLngrnpopzoibIgKi9nZXRIaXN0b3J5Q29sb3I6IGZ1bmN0aW9uIGdldEhpc3RvcnlDb2xvcihyZWNvcmQpIHsKICAgICAgaWYgKHJlY29yZC5maW5pc2hUaW1lKSB7CiAgICAgICAgcmV0dXJuICcjNjdDMjNBJzsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJyNFNkEyM0MnOwogICAgICB9CiAgICB9LAogICAgLyoqIOiOt+WPlueKtuaAgeagh+etvuexu+WeiyAqL2dldFN0YXR1c1RhZ1R5cGU6IGZ1bmN0aW9uIGdldFN0YXR1c1RhZ1R5cGUocmVjb3JkKSB7CiAgICAgIGlmIChyZWNvcmQuZmluaXNoVGltZSkgewogICAgICAgIHJldHVybiAnc3VjY2Vzcyc7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICd3YXJuaW5nJzsKICAgICAgfQogICAgfSwKICAgIC8qKiDojrflj5bnirbmgIHmlofmnKwgKi9nZXRTdGF0dXNUZXh0OiBmdW5jdGlvbiBnZXRTdGF0dXNUZXh0KHJlY29yZCkgewogICAgICBpZiAocmVjb3JkLmZpbmlzaFRpbWUpIHsKICAgICAgICByZXR1cm4gJ+W3suWujOaIkCc7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICflpITnkIbkuK0nOwogICAgICB9CiAgICB9LAogICAgLyoqIOWKoOi9veaJgOacieiKgueCueeahOihqOWNleaVsOaNriAqL2xvYWRBbGxOb2RlRm9ybXM6IGZ1bmN0aW9uIGxvYWRBbGxOb2RlRm9ybXMoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLmZsb3dSZWNvcmRMaXN0LmZvckVhY2goZnVuY3Rpb24gKHJlY29yZCkgewogICAgICAgIGlmIChyZWNvcmQudGFza0lkICYmICFfdGhpczIubm9kZUZvcm1EYXRhW3JlY29yZC50YXNrSWRdICYmICFfdGhpczIubG9hZGluZ05vZGVzLmhhcyhyZWNvcmQudGFza0lkKSkgewogICAgICAgICAgX3RoaXMyLmxvYWROb2RlRm9ybShyZWNvcmQudGFza0lkKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliqDovb3ljZXkuKroioLngrnnmoTooajljZXmlbDmja4gKi9sb2FkTm9kZUZvcm06IGZ1bmN0aW9uIGxvYWROb2RlRm9ybSh0YXNrSWQpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIGlmICghdGFza0lkIHx8IHRoaXMubG9hZGluZ05vZGVzLmhhcyh0YXNrSWQpKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMubG9hZGluZ05vZGVzLmFkZCh0YXNrSWQpOwogICAgICAoMCwgX2RlZmluaXRpb24uZ2V0UHJvY2Vzc1ZhcmlhYmxlcykodGFza0lkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmRhdGEpIHsKICAgICAgICAgIC8vIOiuvue9ruihqOWNleaVsOaNrgogICAgICAgICAgX3RoaXMzLiRzZXQoX3RoaXMzLm5vZGVGb3JtRGF0YSwgdGFza0lkLCByZXMuZGF0YSk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLndhcm4oIlx1NTJBMFx1OEY3RFx1ODI4Mlx1NzBCOSAiLmNvbmNhdCh0YXNrSWQsICIgXHU3Njg0XHU4ODY4XHU1MzU1XHU2NTcwXHU2MzZFXHU1OTMxXHU4RDI1OiIpLCBlcnJvcik7CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMy5sb2FkaW5nTm9kZXMuZGVsZXRlKHRhc2tJZCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDojrflj5booajljZXmlbDmja7nlKjkuo7mmL7npLogKi9nZXRGb3JtRGF0YURpc3BsYXk6IGZ1bmN0aW9uIGdldEZvcm1EYXRhRGlzcGxheSh0YXNrSWQpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHZhciBub2RlRGF0YSA9IHRoaXMubm9kZUZvcm1EYXRhW3Rhc2tJZF07CiAgICAgIGlmICghbm9kZURhdGEpIHJldHVybiB7fTsKICAgICAgY29uc29sZS5sb2coJ+iKgueCueaVsOaNrjonLCB0YXNrSWQsIG5vZGVEYXRhKTsgLy8g6LCD6K+V5L+h5oGvCgogICAgICAvLyDns7vnu5/lrZfmrrXliJfooagKICAgICAgdmFyIHN5c3RlbUZpZWxkcyA9IFsnZm9ybUpzb24nLCAndGFza0lkJywgJ3Byb2NJbnNJZCcsICdkZXBsb3lJZCcsICdwcm9jRGVmSWQnLCAnaW5zdGFuY2VJZCddOwogICAgICB2YXIgZmlsdGVyZWREYXRhID0ge307CiAgICAgIE9iamVjdC5rZXlzKG5vZGVEYXRhKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICB2YXIgdmFsdWUgPSBub2RlRGF0YVtrZXldOwoKICAgICAgICAvLyDot7Pov4fns7vnu5/lrZfmrrUKICAgICAgICBpZiAoc3lzdGVtRmllbGRzLmluY2x1ZGVzKGtleSkpIHsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CgogICAgICAgIC8vIOWMheWQq+aJgOaciemdnuepuuWAvO+8jOWMheaLrOaVsOWtlzDjgIFmYWxzZeetieacieaEj+S5ieeahOWAvAogICAgICAgIGlmICh2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gdW5kZWZpbmVkICYmIHZhbHVlICE9PSAnJykgewogICAgICAgICAgdmFyIGxhYmVsID0gX3RoaXM0LmdldEZpZWxkTGFiZWwoa2V5LCBub2RlRGF0YS5mb3JtSnNvbik7CiAgICAgICAgICBmaWx0ZXJlZERhdGFbbGFiZWxdID0gdmFsdWU7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgY29uc29sZS5sb2coJ+i/h+a7pOWQjueahOaVsOaNrjonLCBmaWx0ZXJlZERhdGEpOyAvLyDosIPor5Xkv6Hmga8KICAgICAgcmV0dXJuIGZpbHRlcmVkRGF0YTsKICAgIH0sCiAgICAvKiog6I635Y+W5a2X5q615qCH562+ICovZ2V0RmllbGRMYWJlbDogZnVuY3Rpb24gZ2V0RmllbGRMYWJlbChmaWVsZEtleSwgZm9ybUpzb24pIHsKICAgICAgaWYgKCFmb3JtSnNvbikgewogICAgICAgIHJldHVybiBmaWVsZEtleTsKICAgICAgfQoKICAgICAgLy8g5bCd6K+V5aSa56eN5Y+v6IO955qE6KGo5Y2V57uT5p6ECiAgICAgIHZhciB3aWRnZXRzID0gW107CiAgICAgIGlmIChmb3JtSnNvbi53aWRnZXRMaXN0KSB7CiAgICAgICAgd2lkZ2V0cyA9IGZvcm1Kc29uLndpZGdldExpc3Q7CiAgICAgIH0gZWxzZSBpZiAoZm9ybUpzb24uZm9ybUNvbmZpZyAmJiBmb3JtSnNvbi5mb3JtQ29uZmlnLndpZGdldExpc3QpIHsKICAgICAgICB3aWRnZXRzID0gZm9ybUpzb24uZm9ybUNvbmZpZy53aWRnZXRMaXN0OwogICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoZm9ybUpzb24pKSB7CiAgICAgICAgd2lkZ2V0cyA9IGZvcm1Kc29uOwogICAgICB9CiAgICAgIGNvbnNvbGUubG9nKCfmn6Xmib7lrZfmrrXmoIfnrb46JywgZmllbGRLZXksICflnKh3aWRnZXRzOicsIHdpZGdldHMpOyAvLyDosIPor5Xkv6Hmga8KCiAgICAgIC8vIOWcqOihqOWNlee7hOS7tuS4reafpeaJvuWtl+auteagh+etvgogICAgICB2YXIgd2lkZ2V0ID0gd2lkZ2V0cy5maW5kKGZ1bmN0aW9uICh3KSB7CiAgICAgICAgaWYgKHcub3B0aW9ucyAmJiB3Lm9wdGlvbnMubmFtZSA9PT0gZmllbGRLZXkpIHJldHVybiB0cnVlOwogICAgICAgIGlmICh3Ll9fY29uZmlnX18gJiYgdy5fX2NvbmZpZ19fLmZvcm1JZCA9PT0gZmllbGRLZXkpIHJldHVybiB0cnVlOwogICAgICAgIGlmICh3Ll9fdk1vZGVsX18gPT09IGZpZWxkS2V5KSByZXR1cm4gdHJ1ZTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0pOwogICAgICBpZiAod2lkZ2V0KSB7CiAgICAgICAgdmFyIF93aWRnZXQkb3B0aW9ucywgX3dpZGdldCRfX2NvbmZpZ19fOwogICAgICAgIC8vIOWwneivleWkmuenjeWPr+iDveeahOagh+etvuWtl+autQogICAgICAgIHZhciBsYWJlbCA9ICgoX3dpZGdldCRvcHRpb25zID0gd2lkZ2V0Lm9wdGlvbnMpID09PSBudWxsIHx8IF93aWRnZXQkb3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3dpZGdldCRvcHRpb25zLmxhYmVsKSB8fCAoKF93aWRnZXQkX19jb25maWdfXyA9IHdpZGdldC5fX2NvbmZpZ19fKSA9PT0gbnVsbCB8fCBfd2lkZ2V0JF9fY29uZmlnX18gPT09IHZvaWQgMCA/IHZvaWQgMCA6IF93aWRnZXQkX19jb25maWdfXy5sYWJlbCkgfHwgd2lkZ2V0LmxhYmVsIHx8IGZpZWxkS2V5OwogICAgICAgIGNvbnNvbGUubG9nKCfmib7liLDmoIfnrb46JywgZmllbGRLZXksICctPicsIGxhYmVsKTsgLy8g6LCD6K+V5L+h5oGvCiAgICAgICAgcmV0dXJuIGxhYmVsOwogICAgICB9CiAgICAgIHJldHVybiBmaWVsZEtleTsKICAgIH0sCiAgICAvKiog5qC85byP5YyW5a2X5q615YC8ICovZm9ybWF0RmllbGRWYWx1ZTogZnVuY3Rpb24gZm9ybWF0RmllbGRWYWx1ZSh2YWx1ZSkgewogICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHsKICAgICAgICByZXR1cm4gdmFsdWUuam9pbignLCAnKTsKICAgICAgfQogICAgICBpZiAoKDAsIF90eXBlb2YyLmRlZmF1bHQpKHZhbHVlKSA9PT0gJ29iamVjdCcpIHsKICAgICAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkodmFsdWUpOwogICAgICB9CiAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdib29sZWFuJykgewogICAgICAgIHJldHVybiB2YWx1ZSA/ICfmmK8nIDogJ+WQpic7CiAgICAgIH0KICAgICAgcmV0dXJuIFN0cmluZyh2YWx1ZSk7CiAgICB9LAogICAgLyoqIOiOt+WPluWtl+autei3qOW6piAqL2dldEZpZWxkU3BhbjogZnVuY3Rpb24gZ2V0RmllbGRTcGFuKHZhbHVlKSB7CiAgICAgIHZhciB2YWx1ZVN0ciA9IHRoaXMuZm9ybWF0RmllbGRWYWx1ZSh2YWx1ZSk7CiAgICAgIC8vIOmVv+aWh+acrOWNoOeUqOS4pOWIlwogICAgICByZXR1cm4gdmFsdWVTdHIubGVuZ3RoID4gMjAgPyAyIDogMTsKICAgIH0sCiAgICAvKiog6I635Y+W5a2X5q615qC35byP57G7ICovZ2V0RmllbGRDbGFzczogZnVuY3Rpb24gZ2V0RmllbGRDbGFzcyh2YWx1ZSkgewogICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnYm9vbGVhbicpIHsKICAgICAgICByZXR1cm4gdmFsdWUgPyAnZmllbGQtYm9vbGVhbi10cnVlJyA6ICdmaWVsZC1ib29sZWFuLWZhbHNlJzsKICAgICAgfQogICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJykgewogICAgICAgIHJldHVybiAnZmllbGQtbnVtYmVyJzsKICAgICAgfQogICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHsKICAgICAgICByZXR1cm4gJ2ZpZWxkLWFycmF5JzsKICAgICAgfQogICAgICByZXR1cm4gJ2ZpZWxkLXRleHQnOwogICAgfSwKICAgIC8qKiDojrflj5boioLngrlWRm9ybemFjee9riAqL2dldE5vZGVWRm9ybUNvbmZpZzogZnVuY3Rpb24gZ2V0Tm9kZVZGb3JtQ29uZmlnKHJlY29yZCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgLy8g5LuO5pys5Zyw5a2Y5YKo5Yqg6L29VkZvcm3phY3nva4KICAgICAgaWYgKHRoaXMubm9kZVZGb3JtQ29uZmlncy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLmxvYWROb2RlVkZvcm1Db25maWdzKCk7CiAgICAgIH0KCiAgICAgIC8vIOagueaNruiKgueCueagh+ivhuaIluS7u+WKoeWQjeensOafpeaJvuWvueW6lOeahFZGb3Jt6YWN572uCiAgICAgIHZhciBjb25maWcgPSB0aGlzLm5vZGVWRm9ybUNvbmZpZ3MuZmluZChmdW5jdGlvbiAoY29uZmlnKSB7CiAgICAgICAgcmV0dXJuIGNvbmZpZy5ub2RlS2V5ID09PSByZWNvcmQudGFza0RlZktleSB8fCBjb25maWcubm9kZU5hbWUgPT09IHJlY29yZC50YXNrTmFtZSB8fCBfdGhpczUubWF0Y2hOb2RlQnlUeXBlKGNvbmZpZywgcmVjb3JkKTsKICAgICAgfSk7CiAgICAgIHJldHVybiBjb25maWc7CiAgICB9LAogICAgLyoqIOagueaNruexu+Wei+WMuemFjeiKgueCuSAqL21hdGNoTm9kZUJ5VHlwZTogZnVuY3Rpb24gbWF0Y2hOb2RlQnlUeXBlKGNvbmZpZywgcmVjb3JkKSB7CiAgICAgIHZhciB0YXNrTmFtZSA9IHJlY29yZC50YXNrTmFtZSB8fCAnJzsKICAgICAgdmFyIG5vZGVUeXBlTWFwID0gewogICAgICAgICducGlfYXBwbHknOiBbJ+eUs+ivtycsICdOUEnnlLPor7cnXSwKICAgICAgICAndGVjaF9yZXZpZXcnOiBbJ+aKgOacr+ivhOWuoScsICfmioDmnK/lrqHmoLgnXSwKICAgICAgICAncHJvY2Vzc19yZXZpZXcnOiBbJ+W3peiJuuivhOWuoScsICflt6XoibrlrqHmoLgnXSwKICAgICAgICAncXVhbGl0eV9yZXZpZXcnOiBbJ+i0qOmHj+ivhOWuoScsICfotKjph4/lrqHmoLgnXSwKICAgICAgICAnY29zdF9yZXZpZXcnOiBbJ+aIkOacrOivhOWuoScsICfmiJDmnKzlrqHmoLgnXSwKICAgICAgICAnZmluYWxfYXBwcm92YWwnOiBbJ+acgOe7iOWuoeaJuScsICfnu4jlrqEnXQogICAgICB9OwogICAgICB2YXIga2V5d29yZHMgPSBub2RlVHlwZU1hcFtjb25maWcubm9kZVR5cGVdIHx8IFtdOwogICAgICByZXR1cm4ga2V5d29yZHMuc29tZShmdW5jdGlvbiAoa2V5d29yZCkgewogICAgICAgIHJldHVybiB0YXNrTmFtZS5pbmNsdWRlcyhrZXl3b3JkKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWKoOi9veiKgueCuVZGb3Jt6YWN572uICovbG9hZE5vZGVWRm9ybUNvbmZpZ3M6IGZ1bmN0aW9uIGxvYWROb2RlVkZvcm1Db25maWdzKCkgewogICAgICB0cnkgewogICAgICAgIC8vIOWKoOi9vU5QSea1geeoi+eahFZGb3Jt6YWN572uCiAgICAgICAgdmFyIHNhdmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ25vZGVfdmZvcm1fY29uZmlnX25waV9wcm9jZXNzJyk7CiAgICAgICAgaWYgKHNhdmVkKSB7CiAgICAgICAgICB2YXIgY29uZmlnID0gSlNPTi5wYXJzZShzYXZlZCk7CiAgICAgICAgICB0aGlzLm5vZGVWRm9ybUNvbmZpZ3MgPSBjb25maWcubm9kZUZvcm1zIHx8IFtdOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLndhcm4oJ+WKoOi9veiKgueCuVZGb3Jt6YWN572u5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLm5vZGVWRm9ybUNvbmZpZ3MgPSBbXTsKICAgICAgfQogICAgfSwKICAgIC8qKiDliIfmjaLoioLngrnooajljZXmmL7npLrmqKHlvI8gKi90b2dnbGVOb2RlRm9ybU1vZGU6IGZ1bmN0aW9uIHRvZ2dsZU5vZGVGb3JtTW9kZSh0YXNrSWQpIHsKICAgICAgdmFyIGN1cnJlbnRNb2RlID0gdGhpcy5ub2RlRm9ybU1vZGVzW3Rhc2tJZF0gfHwgJ2Zvcm0nOwogICAgICB0aGlzLiRzZXQodGhpcy5ub2RlRm9ybU1vZGVzLCB0YXNrSWQsIGN1cnJlbnRNb2RlID09PSAnZm9ybScgPyAnZGF0YScgOiAnZm9ybScpOwogICAgfSwKICAgIC8qKiDojrflj5boioLngrnooajljZXlrZfmrrUgKi9nZXROb2RlRm9ybUZpZWxkczogZnVuY3Rpb24gZ2V0Tm9kZUZvcm1GaWVsZHMocmVjb3JkKSB7CiAgICAgIHZhciB2Zm9ybUNvbmZpZyA9IHRoaXMuZ2V0Tm9kZVZGb3JtQ29uZmlnKHJlY29yZCk7CiAgICAgIGlmICghdmZvcm1Db25maWcgfHwgIXZmb3JtQ29uZmlnLmZvcm1Kc29uIHx8ICF2Zm9ybUNvbmZpZy5mb3JtSnNvbi53aWRnZXRMaXN0KSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICAgIHJldHVybiB2Zm9ybUNvbmZpZy5mb3JtSnNvbi53aWRnZXRMaXN0Lm1hcChmdW5jdGlvbiAod2lkZ2V0KSB7CiAgICAgICAgdmFyIF93aWRnZXQkb3B0aW9uczIsIF93aWRnZXQkb3B0aW9uczMsIF93aWRnZXQkb3B0aW9uczQ7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIG5hbWU6ICgoX3dpZGdldCRvcHRpb25zMiA9IHdpZGdldC5vcHRpb25zKSA9PT0gbnVsbCB8fCBfd2lkZ2V0JG9wdGlvbnMyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfd2lkZ2V0JG9wdGlvbnMyLm5hbWUpIHx8IHdpZGdldC5uYW1lLAogICAgICAgICAgbGFiZWw6ICgoX3dpZGdldCRvcHRpb25zMyA9IHdpZGdldC5vcHRpb25zKSA9PT0gbnVsbCB8fCBfd2lkZ2V0JG9wdGlvbnMzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfd2lkZ2V0JG9wdGlvbnMzLmxhYmVsKSB8fCB3aWRnZXQubGFiZWwgfHwgKChfd2lkZ2V0JG9wdGlvbnM0ID0gd2lkZ2V0Lm9wdGlvbnMpID09PSBudWxsIHx8IF93aWRnZXQkb3B0aW9uczQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF93aWRnZXQkb3B0aW9uczQubmFtZSksCiAgICAgICAgICB0eXBlOiB3aWRnZXQudHlwZSwKICAgICAgICAgIG9wdGlvbnM6IHdpZGdldC5vcHRpb25zCiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOiOt+WPluWtl+auteWAvCAqL2dldEZpZWxkVmFsdWU6IGZ1bmN0aW9uIGdldEZpZWxkVmFsdWUodGFza0lkLCBmaWVsZE5hbWUpIHsKICAgICAgdmFyIG5vZGVEYXRhID0gdGhpcy5ub2RlRm9ybURhdGFbdGFza0lkXTsKICAgICAgaWYgKCFub2RlRGF0YSkgcmV0dXJuIG51bGw7CiAgICAgIHJldHVybiBub2RlRGF0YVtmaWVsZE5hbWVdOwogICAgfSwKICAgIC8qKiDojrflj5blrZfmrrXnu4Tku7YgKi9nZXRGaWVsZENvbXBvbmVudDogZnVuY3Rpb24gZ2V0RmllbGRDb21wb25lbnQoKSB7CiAgICAgIHJldHVybiAnRmllbGRSZW5kZXJlcic7CiAgICB9LAogICAgLyoqIOiOt+WPluiKgueCueebuOWFs+aVsOaNriAqL2dldE5vZGVSZWxhdGVkRGF0YTogZnVuY3Rpb24gZ2V0Tm9kZVJlbGF0ZWREYXRhKHJlY29yZCkgewogICAgICB2YXIgbm9kZURhdGEgPSB0aGlzLm5vZGVGb3JtRGF0YVtyZWNvcmQudGFza0lkXTsKICAgICAgaWYgKCFub2RlRGF0YSkgcmV0dXJuIHt9OwogICAgICB2YXIgdmZvcm1Db25maWcgPSB0aGlzLmdldE5vZGVWRm9ybUNvbmZpZyhyZWNvcmQpOwogICAgICBpZiAoIXZmb3JtQ29uZmlnIHx8ICF2Zm9ybUNvbmZpZy5mb3JtSnNvbiB8fCAhdmZvcm1Db25maWcuZm9ybUpzb24ud2lkZ2V0TGlzdCkgewogICAgICAgIC8vIOWmguaenOayoeacieihqOWNlemFjee9ru+8jOi/lOWbnuaJgOacieaVsOaNrgogICAgICAgIHJldHVybiB0aGlzLmdldEZvcm1EYXRhRGlzcGxheShyZWNvcmQudGFza0lkKTsKICAgICAgfQoKICAgICAgLy8g5Y+q6L+U5Zue5b2T5YmN6IqC54K56KGo5Y2V5Lit5a6a5LmJ55qE5a2X5q615pWw5o2uCiAgICAgIHZhciBub2RlRmllbGRzID0gdmZvcm1Db25maWcuZm9ybUpzb24ud2lkZ2V0TGlzdC5tYXAoZnVuY3Rpb24gKHcpIHsKICAgICAgICB2YXIgX3ckb3B0aW9uczsKICAgICAgICByZXR1cm4gKChfdyRvcHRpb25zID0gdy5vcHRpb25zKSA9PT0gbnVsbCB8fCBfdyRvcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdyRvcHRpb25zLm5hbWUpIHx8IHcubmFtZTsKICAgICAgfSk7CiAgICAgIHZhciBmaWx0ZXJlZERhdGEgPSB7fTsKICAgICAgbm9kZUZpZWxkcy5mb3JFYWNoKGZ1bmN0aW9uIChmaWVsZE5hbWUpIHsKICAgICAgICBpZiAobm9kZURhdGEuaGFzT3duUHJvcGVydHkoZmllbGROYW1lKSAmJiBub2RlRGF0YVtmaWVsZE5hbWVdICE9PSBudWxsICYmIG5vZGVEYXRhW2ZpZWxkTmFtZV0gIT09IHVuZGVmaW5lZCAmJiBub2RlRGF0YVtmaWVsZE5hbWVdICE9PSAnJykgewogICAgICAgICAgdmFyIF93aWRnZXQkb3B0aW9uczU7CiAgICAgICAgICB2YXIgd2lkZ2V0ID0gdmZvcm1Db25maWcuZm9ybUpzb24ud2lkZ2V0TGlzdC5maW5kKGZ1bmN0aW9uICh3KSB7CiAgICAgICAgICAgIHZhciBfdyRvcHRpb25zMjsKICAgICAgICAgICAgcmV0dXJuICgoKF93JG9wdGlvbnMyID0gdy5vcHRpb25zKSA9PT0gbnVsbCB8fCBfdyRvcHRpb25zMiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3ckb3B0aW9uczIubmFtZSkgfHwgdy5uYW1lKSA9PT0gZmllbGROYW1lOwogICAgICAgICAgfSk7CiAgICAgICAgICB2YXIgbGFiZWwgPSAod2lkZ2V0ID09PSBudWxsIHx8IHdpZGdldCA9PT0gdm9pZCAwIHx8IChfd2lkZ2V0JG9wdGlvbnM1ID0gd2lkZ2V0Lm9wdGlvbnMpID09PSBudWxsIHx8IF93aWRnZXQkb3B0aW9uczUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF93aWRnZXQkb3B0aW9uczUubGFiZWwpIHx8ICh3aWRnZXQgPT09IG51bGwgfHwgd2lkZ2V0ID09PSB2b2lkIDAgPyB2b2lkIDAgOiB3aWRnZXQubGFiZWwpIHx8IGZpZWxkTmFtZTsKICAgICAgICAgIGZpbHRlcmVkRGF0YVtsYWJlbF0gPSBub2RlRGF0YVtmaWVsZE5hbWVdOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiBmaWx0ZXJlZERhdGE7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_definition", "require", "_<PERSON><PERSON><PERSON><PERSON>", "_interopRequireDefault", "name", "components", "<PERSON><PERSON><PERSON><PERSON>", "props", "flowRecordList", "type", "Array", "default", "defaultExpanded", "Boolean", "data", "activeHistoryNames", "nodeFormData", "loadingNodes", "Set", "watch", "handler", "newVal", "length", "loadAllNodeForms", "immediate", "_this", "for<PERSON>ach", "index", "parseInt", "replace", "record", "taskId", "has", "loadNodeForm", "methods", "getHistoryIcon", "finishTime", "getHistoryColor", "getStatusTagType", "getStatusText", "_this2", "_this3", "add", "getProcessVariables", "then", "res", "$set", "catch", "error", "console", "warn", "concat", "finally", "delete", "getFormDataDisplay", "_this4", "nodeData", "log", "systemFields", "filteredData", "Object", "keys", "key", "value", "includes", "undefined", "label", "getFieldLabel", "formJson", "<PERSON><PERSON><PERSON>", "widgets", "widgetList", "formConfig", "isArray", "widget", "find", "w", "options", "__config__", "formId", "__vModel__", "_widget$options", "_widget$__config__", "formatFieldValue", "join", "_typeof2", "JSON", "stringify", "String", "getFieldSpan", "valueStr", "getFieldClass", "getNodeVFormConfig", "_this5", "nodeVFormConfigs", "loadNodeVFormConfigs", "config", "nodeKey", "taskDefKey", "nodeName", "taskName", "matchNodeByType", "nodeTypeMap", "keywords", "nodeType", "some", "keyword", "saved", "localStorage", "getItem", "parse", "nodeForms", "toggleNodeFormMode", "currentMode", "nodeFormModes", "getNodeFormFields", "vformConfig", "map", "_widget$options2", "_widget$options3", "_widget$options4", "getFieldValue", "fieldName", "getFieldComponent", "getNodeRelatedData", "nodeFields", "_w$options", "hasOwnProperty", "_widget$options5", "_w$options2"], "sources": ["src/components/FlowHistory/index.vue"], "sourcesContent": ["<template>\n  <div v-if=\"flowRecordList && flowRecordList.length > 0\" class=\"flow-history-container\">\n    <h4 class=\"history-title\">\n      <i class=\"el-icon-time\"></i> 流程历史记录\n    </h4>\n    <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\n      <el-collapse-item\n        v-for=\"(record, index) in flowRecordList\"\n        :key=\"`history-${index}-${record.taskId || record.id || index}`\"\n        :name=\"`history-${index}`\"\n      >\n        <template slot=\"title\">\n          <div class=\"history-title-content\">\n            <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\n            <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\n            <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\n            <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\n            <el-tag\n              :type=\"getStatusTagType(record)\"\n              size=\"mini\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(record) }}\n            </el-tag>\n          </div>\n        </template>\n\n        <div class=\"history-content\">\n          <!-- 节点表单数据显示 -->\n          <div v-if=\"record.taskId && nodeFormData[record.taskId]\" class=\"node-form-section\">\n            <h5 class=\"form-section-title\">\n              <i class=\"el-icon-document\"></i> {{ record.taskName }} - 数据记录\n            </h5>\n            <div class=\"form-data-container\">\n              <el-descriptions :column=\"2\" size=\"small\" border class=\"form-data-descriptions\">\n                <el-descriptions-item\n                  v-for=\"(value, key) in getFormDataDisplay(record.taskId)\"\n                  :key=\"key\"\n                  :label=\"key\"\n                  :span=\"getFieldSpan(value)\"\n                >\n                  <div class=\"form-field-value\" :class=\"getFieldClass(value)\">\n                    {{ formatFieldValue(value) }}\n                  </div>\n                </el-descriptions-item>\n              </el-descriptions>\n              <div v-if=\"Object.keys(getFormDataDisplay(record.taskId)).length === 0\" class=\"no-data\">\n                暂无表单数据\n              </div>\n            </div>\n          </div>\n\n          <!-- 办理信息 -->\n          <el-descriptions :column=\"2\" size=\"small\" border>\n            <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\n              <span>{{ record.assigneeName }}</span>\n              <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\n              {{ record.candidate }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\n              {{ record.createTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\n              {{ record.finishTime }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\n              {{ record.duration }}\n            </el-descriptions-item>\n            <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\n              <div class=\"comment-content\">\n                {{ record.comment.comment }}\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </el-collapse-item>\n    </el-collapse>\n  </div>\n</template>\n\n<script>\nimport { getProcessVariables } from '@/api/flowable/definition'\nimport FieldRenderer from '@/components/FieldRenderer'\n\nexport default {\n  name: 'FlowHistory',\n  components: {\n    FieldRenderer\n  },\n  props: {\n    flowRecordList: {\n      type: Array,\n      default: () => []\n    },\n    defaultExpanded: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      activeHistoryNames: [],\n      nodeFormData: {}, // 存储每个节点的表单数据\n      loadingNodes: new Set() // 正在加载的节点\n    }\n  },\n  watch: {\n    flowRecordList: {\n      handler(newVal) {\n        if (newVal && newVal.length > 0) {\n          if (this.defaultExpanded) {\n            // 默认展开第一个节点\n            this.activeHistoryNames = ['history-0'];\n          }\n          // 加载所有节点的表单数据\n          this.loadAllNodeForms();\n        }\n      },\n      immediate: true\n    },\n    activeHistoryNames: {\n      handler(newVal) {\n        // 当展开节点时，加载对应的表单数据\n        newVal.forEach(name => {\n          const index = parseInt(name.replace('history-', ''));\n          const record = this.flowRecordList[index];\n          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n            this.loadNodeForm(record.taskId);\n          }\n        });\n      }\n    }\n  },\n  methods: {\n    /** 获取历史节点图标 */\n    getHistoryIcon(record) {\n      if (record.finishTime) {\n        return 'el-icon-check';\n      } else {\n        return 'el-icon-time';\n      }\n    },\n\n    /** 获取历史节点颜色 */\n    getHistoryColor(record) {\n      if (record.finishTime) {\n        return '#67C23A';\n      } else {\n        return '#E6A23C';\n      }\n    },\n\n    /** 获取状态标签类型 */\n    getStatusTagType(record) {\n      if (record.finishTime) {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    /** 获取状态文本 */\n    getStatusText(record) {\n      if (record.finishTime) {\n        return '已完成';\n      } else {\n        return '处理中';\n      }\n    },\n\n    /** 加载所有节点的表单数据 */\n    loadAllNodeForms() {\n      this.flowRecordList.forEach(record => {\n        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {\n          this.loadNodeForm(record.taskId);\n        }\n      });\n    },\n\n    /** 加载单个节点的表单数据 */\n    loadNodeForm(taskId) {\n      if (!taskId || this.loadingNodes.has(taskId)) {\n        return;\n      }\n\n      this.loadingNodes.add(taskId);\n\n      getProcessVariables(taskId).then(res => {\n        if (res.data) {\n          // 设置表单数据\n          this.$set(this.nodeFormData, taskId, res.data);\n        }\n      }).catch(error => {\n        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);\n      }).finally(() => {\n        this.loadingNodes.delete(taskId);\n      });\n    },\n\n    /** 获取表单数据用于显示 */\n    getFormDataDisplay(taskId) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return {};\n\n      console.log('节点数据:', taskId, nodeData); // 调试信息\n\n      // 系统字段列表\n      const systemFields = ['formJson', 'taskId', 'procInsId', 'deployId', 'procDefId', 'instanceId'];\n\n      const filteredData = {};\n      Object.keys(nodeData).forEach(key => {\n        const value = nodeData[key];\n\n        // 跳过系统字段\n        if (systemFields.includes(key)) {\n          return;\n        }\n\n        // 包含所有非空值，包括数字0、false等有意义的值\n        if (value !== null && value !== undefined && value !== '') {\n          const label = this.getFieldLabel(key, nodeData.formJson);\n          filteredData[label] = value;\n        }\n      });\n\n      console.log('过滤后的数据:', filteredData); // 调试信息\n      return filteredData;\n    },\n\n    /** 获取字段标签 */\n    getFieldLabel(fieldKey, formJson) {\n      if (!formJson) {\n        return fieldKey;\n      }\n\n      // 尝试多种可能的表单结构\n      let widgets = [];\n\n      if (formJson.widgetList) {\n        widgets = formJson.widgetList;\n      } else if (formJson.formConfig && formJson.formConfig.widgetList) {\n        widgets = formJson.formConfig.widgetList;\n      } else if (Array.isArray(formJson)) {\n        widgets = formJson;\n      }\n\n      console.log('查找字段标签:', fieldKey, '在widgets:', widgets); // 调试信息\n\n      // 在表单组件中查找字段标签\n      const widget = widgets.find(w => {\n        if (w.options && w.options.name === fieldKey) return true;\n        if (w.__config__ && w.__config__.formId === fieldKey) return true;\n        if (w.__vModel__ === fieldKey) return true;\n        return false;\n      });\n\n      if (widget) {\n        // 尝试多种可能的标签字段\n        const label = widget.options?.label ||\n                     widget.__config__?.label ||\n                     widget.label ||\n                     fieldKey;\n        console.log('找到标签:', fieldKey, '->', label); // 调试信息\n        return label;\n      }\n\n      return fieldKey;\n    },\n\n    /** 格式化字段值 */\n    formatFieldValue(value) {\n      if (Array.isArray(value)) {\n        return value.join(', ');\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      if (typeof value === 'boolean') {\n        return value ? '是' : '否';\n      }\n      return String(value);\n    },\n\n    /** 获取字段跨度 */\n    getFieldSpan(value) {\n      const valueStr = this.formatFieldValue(value);\n      // 长文本占用两列\n      return valueStr.length > 20 ? 2 : 1;\n    },\n\n    /** 获取字段样式类 */\n    getFieldClass(value) {\n      if (typeof value === 'boolean') {\n        return value ? 'field-boolean-true' : 'field-boolean-false';\n      }\n      if (typeof value === 'number') {\n        return 'field-number';\n      }\n      if (Array.isArray(value)) {\n        return 'field-array';\n      }\n      return 'field-text';\n    },\n\n    /** 获取节点VForm配置 */\n    getNodeVFormConfig(record) {\n      // 从本地存储加载VForm配置\n      if (this.nodeVFormConfigs.length === 0) {\n        this.loadNodeVFormConfigs();\n      }\n\n      // 根据节点标识或任务名称查找对应的VForm配置\n      const config = this.nodeVFormConfigs.find(config =>\n        config.nodeKey === record.taskDefKey ||\n        config.nodeName === record.taskName ||\n        this.matchNodeByType(config, record)\n      );\n\n      return config;\n    },\n\n    /** 根据类型匹配节点 */\n    matchNodeByType(config, record) {\n      const taskName = record.taskName || '';\n      const nodeTypeMap = {\n        'npi_apply': ['申请', 'NPI申请'],\n        'tech_review': ['技术评审', '技术审核'],\n        'process_review': ['工艺评审', '工艺审核'],\n        'quality_review': ['质量评审', '质量审核'],\n        'cost_review': ['成本评审', '成本审核'],\n        'final_approval': ['最终审批', '终审']\n      };\n\n      const keywords = nodeTypeMap[config.nodeType] || [];\n      return keywords.some(keyword => taskName.includes(keyword));\n    },\n\n    /** 加载节点VForm配置 */\n    loadNodeVFormConfigs() {\n      try {\n        // 加载NPI流程的VForm配置\n        const saved = localStorage.getItem('node_vform_config_npi_process');\n        if (saved) {\n          const config = JSON.parse(saved);\n          this.nodeVFormConfigs = config.nodeForms || [];\n        }\n      } catch (error) {\n        console.warn('加载节点VForm配置失败:', error);\n        this.nodeVFormConfigs = [];\n      }\n    },\n\n    /** 切换节点表单显示模式 */\n    toggleNodeFormMode(taskId) {\n      const currentMode = this.nodeFormModes[taskId] || 'form';\n      this.$set(this.nodeFormModes, taskId, currentMode === 'form' ? 'data' : 'form');\n    },\n\n    /** 获取节点表单字段 */\n    getNodeFormFields(record) {\n      const vformConfig = this.getNodeVFormConfig(record);\n      if (!vformConfig || !vformConfig.formJson || !vformConfig.formJson.widgetList) {\n        return [];\n      }\n\n      return vformConfig.formJson.widgetList.map(widget => ({\n        name: widget.options?.name || widget.name,\n        label: widget.options?.label || widget.label || widget.options?.name,\n        type: widget.type,\n        options: widget.options\n      }));\n    },\n\n    /** 获取字段值 */\n    getFieldValue(taskId, fieldName) {\n      const nodeData = this.nodeFormData[taskId];\n      if (!nodeData) return null;\n      return nodeData[fieldName];\n    },\n\n    /** 获取字段组件 */\n    getFieldComponent() {\n      return 'FieldRenderer';\n    },\n\n    /** 获取节点相关数据 */\n    getNodeRelatedData(record) {\n      const nodeData = this.nodeFormData[record.taskId];\n      if (!nodeData) return {};\n\n      const vformConfig = this.getNodeVFormConfig(record);\n      if (!vformConfig || !vformConfig.formJson || !vformConfig.formJson.widgetList) {\n        // 如果没有表单配置，返回所有数据\n        return this.getFormDataDisplay(record.taskId);\n      }\n\n      // 只返回当前节点表单中定义的字段数据\n      const nodeFields = vformConfig.formJson.widgetList.map(w => w.options?.name || w.name);\n      const filteredData = {};\n\n      nodeFields.forEach(fieldName => {\n        if (nodeData.hasOwnProperty(fieldName) &&\n            nodeData[fieldName] !== null &&\n            nodeData[fieldName] !== undefined &&\n            nodeData[fieldName] !== '') {\n          const widget = vformConfig.formJson.widgetList.find(w =>\n            (w.options?.name || w.name) === fieldName\n          );\n          const label = widget?.options?.label || widget?.label || fieldName;\n          filteredData[label] = nodeData[fieldName];\n        }\n      });\n\n      return filteredData;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.flow-history-container {\n  margin-bottom: 20px;\n}\n\n.history-title {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n/* 历史节点样式 */\n.history-collapse {\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.history-collapse .el-collapse-item__header {\n  background-color: #F5F7FA;\n  border-bottom: 1px solid #EBEEF5;\n  padding: 0 20px;\n  height: 48px;\n  line-height: 48px;\n}\n\n.history-collapse .el-collapse-item__content {\n  padding: 20px;\n  background-color: #FAFAFA;\n}\n\n.history-title-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  font-size: 14px;\n}\n\n.history-title-content .node-name {\n  font-weight: 600;\n  margin-left: 8px;\n  margin-right: 15px;\n  color: #303133;\n}\n\n.history-title-content .assignee-name {\n  color: #606266;\n  margin-right: 15px;\n}\n\n.history-title-content .finish-time {\n  color: #909399;\n  font-size: 12px;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.history-content {\n  background-color: white;\n  border-radius: 4px;\n  padding: 16px;\n}\n\n.comment-content {\n  background-color: #F8F9FA;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 4px solid #409EFF;\n  font-style: italic;\n  color: #606266;\n}\n\n/* 节点表单样式 */\n.node-form-section {\n  margin-bottom: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.form-section-title {\n  background-color: #F5F7FA;\n  padding: 12px 16px;\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #606266;\n  border-bottom: 1px solid #E4E7ED;\n}\n\n.form-section-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n/* 表单显示容器样式 */\n.form-display-container {\n  background-color: white;\n}\n\n/* 节点表单视图样式 */\n.node-form-view {\n  padding: 16px;\n}\n\n.form-fields {\n  .form-field-item {\n    margin-bottom: 20px;\n    display: flex;\n    align-items: flex-start;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .field-label {\n    width: 120px;\n    min-width: 120px;\n    padding: 8px 12px 8px 0;\n    color: #606266;\n    font-weight: 500;\n    text-align: right;\n    line-height: 32px;\n  }\n\n  .field-content {\n    flex: 1;\n    padding: 4px 0;\n  }\n}\n\n.no-fields {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n  font-style: italic;\n}\n\n/* 节点数据视图样式 */\n.node-data-view {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n/* 通用数据容器样式 */\n.form-data-container {\n  padding: 16px;\n  background-color: #FAFAFA;\n}\n\n.form-data-descriptions {\n  background-color: white;\n}\n\n/* 表单字段值样式 */\n.form-field-value {\n  word-break: break-word;\n  line-height: 1.5;\n}\n\n.field-boolean-true {\n  color: #67C23A;\n  font-weight: 600;\n}\n\n.field-boolean-false {\n  color: #F56C6C;\n  font-weight: 600;\n}\n\n.field-number {\n  color: #E6A23C;\n  font-weight: 500;\n}\n\n.field-array {\n  color: #909399;\n  font-style: italic;\n}\n\n.field-text {\n  color: #606266;\n}\n\n/* 原始数据显示样式 */\n.raw-data-container {\n  background-color: #f8f8f8;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 12px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.raw-data-container pre {\n  margin: 0;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  color: #333;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.no-data {\n  text-align: center;\n  color: #909399;\n  padding: 20px;\n  font-style: italic;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA;EACA;EACAC,KAAA;IACAC,cAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,eAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;MACAC,YAAA;MAAA;MACAC,YAAA,MAAAC,GAAA;IACA;EACA;EACAC,KAAA;IACAX,cAAA;MACAY,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,MAAA;UACA,SAAAV,eAAA;YACA;YACA,KAAAG,kBAAA;UACA;UACA;UACA,KAAAQ,gBAAA;QACA;MACA;MACAC,SAAA;IACA;IACAT,kBAAA;MACAK,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAI,KAAA;QACA;QACAJ,MAAA,CAAAK,OAAA,WAAAtB,IAAA;UACA,IAAAuB,KAAA,GAAAC,QAAA,CAAAxB,IAAA,CAAAyB,OAAA;UACA,IAAAC,MAAA,GAAAL,KAAA,CAAAjB,cAAA,CAAAmB,KAAA;UACA,IAAAG,MAAA,IAAAA,MAAA,CAAAC,MAAA,KAAAN,KAAA,CAAAT,YAAA,CAAAc,MAAA,CAAAC,MAAA,MAAAN,KAAA,CAAAR,YAAA,CAAAe,GAAA,CAAAF,MAAA,CAAAC,MAAA;YACAN,KAAA,CAAAQ,YAAA,CAAAH,MAAA,CAAAC,MAAA;UACA;QACA;MACA;IACA;EACA;EACAG,OAAA;IACA,eACAC,cAAA,WAAAA,eAAAL,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAC,eAAA,WAAAA,gBAAAP,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAE,gBAAA,WAAAA,iBAAAR,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,aACAG,aAAA,WAAAA,cAAAT,MAAA;MACA,IAAAA,MAAA,CAAAM,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,kBACAb,gBAAA,WAAAA,iBAAA;MAAA,IAAAiB,MAAA;MACA,KAAAhC,cAAA,CAAAkB,OAAA,WAAAI,MAAA;QACA,IAAAA,MAAA,CAAAC,MAAA,KAAAS,MAAA,CAAAxB,YAAA,CAAAc,MAAA,CAAAC,MAAA,MAAAS,MAAA,CAAAvB,YAAA,CAAAe,GAAA,CAAAF,MAAA,CAAAC,MAAA;UACAS,MAAA,CAAAP,YAAA,CAAAH,MAAA,CAAAC,MAAA;QACA;MACA;IACA;IAEA,kBACAE,YAAA,WAAAA,aAAAF,MAAA;MAAA,IAAAU,MAAA;MACA,KAAAV,MAAA,SAAAd,YAAA,CAAAe,GAAA,CAAAD,MAAA;QACA;MACA;MAEA,KAAAd,YAAA,CAAAyB,GAAA,CAAAX,MAAA;MAEA,IAAAY,+BAAA,EAAAZ,MAAA,EAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA/B,IAAA;UACA;UACA2B,MAAA,CAAAK,IAAA,CAAAL,MAAA,CAAAzB,YAAA,EAAAe,MAAA,EAAAc,GAAA,CAAA/B,IAAA;QACA;MACA,GAAAiC,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAC,IAAA,6BAAAC,MAAA,CAAApB,MAAA,mDAAAiB,KAAA;MACA,GAAAI,OAAA;QACAX,MAAA,CAAAxB,YAAA,CAAAoC,MAAA,CAAAtB,MAAA;MACA;IACA;IAEA,iBACAuB,kBAAA,WAAAA,mBAAAvB,MAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,QAAA,QAAAxC,YAAA,CAAAe,MAAA;MACA,KAAAyB,QAAA;MAEAP,OAAA,CAAAQ,GAAA,UAAA1B,MAAA,EAAAyB,QAAA;;MAEA;MACA,IAAAE,YAAA;MAEA,IAAAC,YAAA;MACAC,MAAA,CAAAC,IAAA,CAAAL,QAAA,EAAA9B,OAAA,WAAAoC,GAAA;QACA,IAAAC,KAAA,GAAAP,QAAA,CAAAM,GAAA;;QAEA;QACA,IAAAJ,YAAA,CAAAM,QAAA,CAAAF,GAAA;UACA;QACA;;QAEA;QACA,IAAAC,KAAA,aAAAA,KAAA,KAAAE,SAAA,IAAAF,KAAA;UACA,IAAAG,KAAA,GAAAX,MAAA,CAAAY,aAAA,CAAAL,GAAA,EAAAN,QAAA,CAAAY,QAAA;UACAT,YAAA,CAAAO,KAAA,IAAAH,KAAA;QACA;MACA;MAEAd,OAAA,CAAAQ,GAAA,YAAAE,YAAA;MACA,OAAAA,YAAA;IACA;IAEA,aACAQ,aAAA,WAAAA,cAAAE,QAAA,EAAAD,QAAA;MACA,KAAAA,QAAA;QACA,OAAAC,QAAA;MACA;;MAEA;MACA,IAAAC,OAAA;MAEA,IAAAF,QAAA,CAAAG,UAAA;QACAD,OAAA,GAAAF,QAAA,CAAAG,UAAA;MACA,WAAAH,QAAA,CAAAI,UAAA,IAAAJ,QAAA,CAAAI,UAAA,CAAAD,UAAA;QACAD,OAAA,GAAAF,QAAA,CAAAI,UAAA,CAAAD,UAAA;MACA,WAAA7D,KAAA,CAAA+D,OAAA,CAAAL,QAAA;QACAE,OAAA,GAAAF,QAAA;MACA;MAEAnB,OAAA,CAAAQ,GAAA,YAAAY,QAAA,eAAAC,OAAA;;MAEA;MACA,IAAAI,MAAA,GAAAJ,OAAA,CAAAK,IAAA,WAAAC,CAAA;QACA,IAAAA,CAAA,CAAAC,OAAA,IAAAD,CAAA,CAAAC,OAAA,CAAAzE,IAAA,KAAAiE,QAAA;QACA,IAAAO,CAAA,CAAAE,UAAA,IAAAF,CAAA,CAAAE,UAAA,CAAAC,MAAA,KAAAV,QAAA;QACA,IAAAO,CAAA,CAAAI,UAAA,KAAAX,QAAA;QACA;MACA;MAEA,IAAAK,MAAA;QAAA,IAAAO,eAAA,EAAAC,kBAAA;QACA;QACA,IAAAhB,KAAA,KAAAe,eAAA,GAAAP,MAAA,CAAAG,OAAA,cAAAI,eAAA,uBAAAA,eAAA,CAAAf,KAAA,OAAAgB,kBAAA,GACAR,MAAA,CAAAI,UAAA,cAAAI,kBAAA,uBAAAA,kBAAA,CAAAhB,KAAA,KACAQ,MAAA,CAAAR,KAAA,IACAG,QAAA;QACApB,OAAA,CAAAQ,GAAA,UAAAY,QAAA,QAAAH,KAAA;QACA,OAAAA,KAAA;MACA;MAEA,OAAAG,QAAA;IACA;IAEA,aACAc,gBAAA,WAAAA,iBAAApB,KAAA;MACA,IAAArD,KAAA,CAAA+D,OAAA,CAAAV,KAAA;QACA,OAAAA,KAAA,CAAAqB,IAAA;MACA;MACA,QAAAC,QAAA,CAAA1E,OAAA,EAAAoD,KAAA;QACA,OAAAuB,IAAA,CAAAC,SAAA,CAAAxB,KAAA;MACA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA;MACA;MACA,OAAAyB,MAAA,CAAAzB,KAAA;IACA;IAEA,aACA0B,YAAA,WAAAA,aAAA1B,KAAA;MACA,IAAA2B,QAAA,QAAAP,gBAAA,CAAApB,KAAA;MACA;MACA,OAAA2B,QAAA,CAAApE,MAAA;IACA;IAEA,cACAqE,aAAA,WAAAA,cAAA5B,KAAA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA;MACA;MACA,WAAAA,KAAA;QACA;MACA;MACA,IAAArD,KAAA,CAAA+D,OAAA,CAAAV,KAAA;QACA;MACA;MACA;IACA;IAEA,kBACA6B,kBAAA,WAAAA,mBAAA9D,MAAA;MAAA,IAAA+D,MAAA;MACA;MACA,SAAAC,gBAAA,CAAAxE,MAAA;QACA,KAAAyE,oBAAA;MACA;;MAEA;MACA,IAAAC,MAAA,QAAAF,gBAAA,CAAAnB,IAAA,WAAAqB,MAAA;QAAA,OACAA,MAAA,CAAAC,OAAA,KAAAnE,MAAA,CAAAoE,UAAA,IACAF,MAAA,CAAAG,QAAA,KAAArE,MAAA,CAAAsE,QAAA,IACAP,MAAA,CAAAQ,eAAA,CAAAL,MAAA,EAAAlE,MAAA;MAAA,CACA;MAEA,OAAAkE,MAAA;IACA;IAEA,eACAK,eAAA,WAAAA,gBAAAL,MAAA,EAAAlE,MAAA;MACA,IAAAsE,QAAA,GAAAtE,MAAA,CAAAsE,QAAA;MACA,IAAAE,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,IAAAC,QAAA,GAAAD,WAAA,CAAAN,MAAA,CAAAQ,QAAA;MACA,OAAAD,QAAA,CAAAE,IAAA,WAAAC,OAAA;QAAA,OAAAN,QAAA,CAAApC,QAAA,CAAA0C,OAAA;MAAA;IACA;IAEA,kBACAX,oBAAA,WAAAA,qBAAA;MACA;QACA;QACA,IAAAY,KAAA,GAAAC,YAAA,CAAAC,OAAA;QACA,IAAAF,KAAA;UACA,IAAAX,MAAA,GAAAV,IAAA,CAAAwB,KAAA,CAAAH,KAAA;UACA,KAAAb,gBAAA,GAAAE,MAAA,CAAAe,SAAA;QACA;MACA,SAAA/D,KAAA;QACAC,OAAA,CAAAC,IAAA,mBAAAF,KAAA;QACA,KAAA8C,gBAAA;MACA;IACA;IAEA,iBACAkB,kBAAA,WAAAA,mBAAAjF,MAAA;MACA,IAAAkF,WAAA,QAAAC,aAAA,CAAAnF,MAAA;MACA,KAAAe,IAAA,MAAAoE,aAAA,EAAAnF,MAAA,EAAAkF,WAAA;IACA;IAEA,eACAE,iBAAA,WAAAA,kBAAArF,MAAA;MACA,IAAAsF,WAAA,QAAAxB,kBAAA,CAAA9D,MAAA;MACA,KAAAsF,WAAA,KAAAA,WAAA,CAAAhD,QAAA,KAAAgD,WAAA,CAAAhD,QAAA,CAAAG,UAAA;QACA;MACA;MAEA,OAAA6C,WAAA,CAAAhD,QAAA,CAAAG,UAAA,CAAA8C,GAAA,WAAA3C,MAAA;QAAA,IAAA4C,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;QAAA;UACApH,IAAA,IAAAkH,gBAAA,GAAA5C,MAAA,CAAAG,OAAA,cAAAyC,gBAAA,uBAAAA,gBAAA,CAAAlH,IAAA,KAAAsE,MAAA,CAAAtE,IAAA;UACA8D,KAAA,IAAAqD,gBAAA,GAAA7C,MAAA,CAAAG,OAAA,cAAA0C,gBAAA,uBAAAA,gBAAA,CAAArD,KAAA,KAAAQ,MAAA,CAAAR,KAAA,MAAAsD,gBAAA,GAAA9C,MAAA,CAAAG,OAAA,cAAA2C,gBAAA,uBAAAA,gBAAA,CAAApH,IAAA;UACAK,IAAA,EAAAiE,MAAA,CAAAjE,IAAA;UACAoE,OAAA,EAAAH,MAAA,CAAAG;QACA;MAAA;IACA;IAEA,YACA4C,aAAA,WAAAA,cAAA1F,MAAA,EAAA2F,SAAA;MACA,IAAAlE,QAAA,QAAAxC,YAAA,CAAAe,MAAA;MACA,KAAAyB,QAAA;MACA,OAAAA,QAAA,CAAAkE,SAAA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAA;MACA;IACA;IAEA,eACAC,kBAAA,WAAAA,mBAAA9F,MAAA;MACA,IAAA0B,QAAA,QAAAxC,YAAA,CAAAc,MAAA,CAAAC,MAAA;MACA,KAAAyB,QAAA;MAEA,IAAA4D,WAAA,QAAAxB,kBAAA,CAAA9D,MAAA;MACA,KAAAsF,WAAA,KAAAA,WAAA,CAAAhD,QAAA,KAAAgD,WAAA,CAAAhD,QAAA,CAAAG,UAAA;QACA;QACA,YAAAjB,kBAAA,CAAAxB,MAAA,CAAAC,MAAA;MACA;;MAEA;MACA,IAAA8F,UAAA,GAAAT,WAAA,CAAAhD,QAAA,CAAAG,UAAA,CAAA8C,GAAA,WAAAzC,CAAA;QAAA,IAAAkD,UAAA;QAAA,SAAAA,UAAA,GAAAlD,CAAA,CAAAC,OAAA,cAAAiD,UAAA,uBAAAA,UAAA,CAAA1H,IAAA,KAAAwE,CAAA,CAAAxE,IAAA;MAAA;MACA,IAAAuD,YAAA;MAEAkE,UAAA,CAAAnG,OAAA,WAAAgG,SAAA;QACA,IAAAlE,QAAA,CAAAuE,cAAA,CAAAL,SAAA,KACAlE,QAAA,CAAAkE,SAAA,cACAlE,QAAA,CAAAkE,SAAA,MAAAzD,SAAA,IACAT,QAAA,CAAAkE,SAAA;UAAA,IAAAM,gBAAA;UACA,IAAAtD,MAAA,GAAA0C,WAAA,CAAAhD,QAAA,CAAAG,UAAA,CAAAI,IAAA,WAAAC,CAAA;YAAA,IAAAqD,WAAA;YAAA,OACA,GAAAA,WAAA,GAAArD,CAAA,CAAAC,OAAA,cAAAoD,WAAA,uBAAAA,WAAA,CAAA7H,IAAA,KAAAwE,CAAA,CAAAxE,IAAA,MAAAsH,SAAA;UAAA,CACA;UACA,IAAAxD,KAAA,IAAAQ,MAAA,aAAAA,MAAA,gBAAAsD,gBAAA,GAAAtD,MAAA,CAAAG,OAAA,cAAAmD,gBAAA,uBAAAA,gBAAA,CAAA9D,KAAA,MAAAQ,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAAAR,KAAA,KAAAwD,SAAA;UACA/D,YAAA,CAAAO,KAAA,IAAAV,QAAA,CAAAkE,SAAA;QACA;MACA;MAEA,OAAA/D,YAAA;IACA;EACA;AACA", "ignoreList": []}]}