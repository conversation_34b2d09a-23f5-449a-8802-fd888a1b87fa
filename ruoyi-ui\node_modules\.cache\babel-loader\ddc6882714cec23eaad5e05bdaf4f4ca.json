{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeVFormManager\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeVFormManager\\index.vue", "mtime": 1752411008662}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "value", "type", "Array", "default", "process<PERSON>ey", "String", "data", "nodeForms", "activeNodes", "showDesigner", "showPreview", "showAllPreview", "previewActiveTab", "currentEditNode", "designerConfig", "languageMenu", "externalLink", "formTemplates", "eventCollapse", "widgetCollapse", "clearDesigner<PERSON><PERSON><PERSON>", "previewFormButton", "importJsonButton", "exportJsonButton", "exportCodeButton", "generateSFCButton", "watch", "handler", "newVal", "loadFormPreviews", "immediate", "deep", "$emit", "methods", "addNodeForm", "newNodeForm", "id", "concat", "Date", "now", "nodeName", "length", "nodeType", "nodeKey", "formJson", "<PERSON><PERSON><PERSON>", "push", "removeNodeForm", "index", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "then", "splice", "$message", "success", "catch", "designForm", "nodeForm", "_this2", "$nextTick", "$refs", "vfDesigner", "set<PERSON><PERSON><PERSON><PERSON>", "widgetList", "formConfig", "modelName", "refName", "rulesName", "labelWidth", "labelPosition", "size", "labelAlign", "cssCode", "customClass", "functions", "layoutType", "onFormJsonChange", "_this3", "find", "n", "saveFormDesign", "_this4", "getFormJson", "previewForm", "_this5", "previewFormRef", "previewAllForms", "_this6", "formsWithJson", "filter", "warning", "for<PERSON>ach", "ref", "_this7", "disableForm", "getNodeTypeTag", "tagMap", "npi_apply", "tech_review", "process_review", "quality_review", "cost_review", "final_approval", "getNodeTypeText", "textMap", "saveConfig", "config", "createTime", "toISOString", "version", "localStorage", "setItem", "JSON", "stringify", "exportConfig", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "click", "revokeObjectURL", "importConfig", "fileInput", "handleFileImport", "event", "_this8", "file", "target", "files", "reader", "FileReader", "onload", "e", "parse", "result", "isArray", "error", "readAsText", "getFormByNodeKey", "form", "getFormByNodeType"], "sources": ["src/components/NodeVFormManager/index.vue"], "sourcesContent": ["<template>\n  <div class=\"node-vform-manager\">\n    <div class=\"manager-header\">\n      <h3>\n        <i class=\"el-icon-s-order\"></i>\n        NPI流程节点表单配置\n      </h3>\n      <el-button type=\"primary\" @click=\"addNodeForm\">\n        <i class=\"el-icon-plus\"></i>\n        添加节点表单\n      </el-button>\n    </div>\n\n    <div class=\"node-forms-list\">\n      <el-collapse v-model=\"activeNodes\" accordion>\n        <el-collapse-item \n          v-for=\"(nodeForm, index) in nodeForms\" \n          :key=\"nodeForm.id\"\n          :name=\"nodeForm.id\"\n        >\n          <template slot=\"title\">\n            <div class=\"node-title\">\n              <i class=\"el-icon-document\"></i>\n              <span class=\"node-name\">{{ nodeForm.nodeName }}</span>\n              <el-tag :type=\"getNodeTypeTag(nodeForm.nodeType)\" size=\"mini\">\n                {{ getNodeTypeText(nodeForm.nodeType) }}\n              </el-tag>\n              <span class=\"form-status\">{{ nodeForm.formJson ? '已配置' : '未配置' }}</span>\n            </div>\n          </template>\n\n          <div class=\"node-form-content\">\n            <div class=\"node-config\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"节点名称\">\n                    <el-input v-model=\"nodeForm.nodeName\" placeholder=\"请输入节点名称\" />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"节点类型\">\n                    <el-select v-model=\"nodeForm.nodeType\" style=\"width: 100%\">\n                      <el-option label=\"NPI申请\" value=\"npi_apply\" />\n                      <el-option label=\"技术评审\" value=\"tech_review\" />\n                      <el-option label=\"工艺评审\" value=\"process_review\" />\n                      <el-option label=\"质量评审\" value=\"quality_review\" />\n                      <el-option label=\"成本评审\" value=\"cost_review\" />\n                      <el-option label=\"最终审批\" value=\"final_approval\" />\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"节点标识\">\n                    <el-input v-model=\"nodeForm.nodeKey\" placeholder=\"流程图中的节点ID\" />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"操作\">\n                    <el-button type=\"primary\" size=\"small\" @click=\"designForm(nodeForm)\">\n                      设计表单\n                    </el-button>\n                    <el-button type=\"danger\" size=\"small\" @click=\"removeNodeForm(index)\">\n                      删除\n                    </el-button>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </div>\n\n            <!-- 表单预览 -->\n            <div v-if=\"nodeForm.formJson\" class=\"form-preview\">\n              <h5>表单预览</h5>\n              <div class=\"preview-container\">\n                <v-form-render \n                  :ref=\"`preview_${nodeForm.id}`\"\n                  :key=\"`preview_${nodeForm.id}_${nodeForm.previewKey || 0}`\"\n                />\n              </div>\n            </div>\n            <div v-else class=\"no-form\">\n              <i class=\"el-icon-document-add\"></i>\n              <p>暂未配置表单，点击\"设计表单\"开始配置</p>\n            </div>\n          </div>\n        </el-collapse-item>\n      </el-collapse>\n\n      <div v-if=\"nodeForms.length === 0\" class=\"empty-state\">\n        <i class=\"el-icon-document-add\"></i>\n        <p>暂无节点表单，点击\"添加节点表单\"开始创建</p>\n      </div>\n    </div>\n\n    <div class=\"manager-footer\">\n      <el-button @click=\"previewAllForms\">预览所有表单</el-button>\n      <el-button type=\"primary\" @click=\"saveConfig\">保存配置</el-button>\n      <el-button @click=\"exportConfig\">导出配置</el-button>\n      <el-button @click=\"importConfig\">导入配置</el-button>\n    </div>\n\n    <!-- VForm设计器对话框 -->\n    <el-dialog \n      :title=\"`设计表单 - ${currentEditNode.nodeName}`\"\n      :visible.sync=\"showDesigner\" \n      width=\"90%\" \n      top=\"5vh\"\n      :close-on-click-modal=\"false\"\n    >\n      <div class=\"vform-designer-container\">\n        <v-form-designer \n          ref=\"vfDesigner\" \n          :designer-config=\"designerConfig\"\n          @form-json-change=\"onFormJsonChange\"\n        >\n          <template #customToolButtons>\n            <el-button type=\"primary\" @click=\"saveFormDesign\">保存表单</el-button>\n            <el-button @click=\"previewForm\">预览表单</el-button>\n          </template>\n        </v-form-designer>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"showPreview\" width=\"60%\">\n      <div class=\"form-preview-dialog\">\n        <v-form-render ref=\"previewFormRef\" />\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"showPreview = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 所有表单预览 -->\n    <el-dialog title=\"所有表单预览\" :visible.sync=\"showAllPreview\" width=\"80%\">\n      <el-tabs v-model=\"previewActiveTab\" type=\"card\">\n        <el-tab-pane \n          v-for=\"nodeForm in nodeForms.filter(n => n.formJson)\" \n          :key=\"nodeForm.id\"\n          :label=\"nodeForm.nodeName\"\n          :name=\"nodeForm.id\"\n        >\n          <v-form-render \n            :ref=\"`allPreview_${nodeForm.id}`\"\n            :key=\"`allPreview_${nodeForm.id}_${nodeForm.previewKey || 0}`\"\n          />\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n\n    <!-- 导入文件 -->\n    <input \n      ref=\"fileInput\" \n      type=\"file\" \n      accept=\".json\" \n      style=\"display: none\" \n      @change=\"handleFileImport\"\n    />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'NodeVFormManager',\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    },\n    processKey: {\n      type: String,\n      default: 'npi_process'\n    }\n  },\n  data() {\n    return {\n      nodeForms: [],\n      activeNodes: '',\n      showDesigner: false,\n      showPreview: false,\n      showAllPreview: false,\n      previewActiveTab: '',\n      currentEditNode: {},\n      designerConfig: {\n        languageMenu: false,\n        externalLink: false,\n        formTemplates: true,\n        eventCollapse: false,\n        widgetCollapse: false,\n        clearDesignerButton: true,\n        previewFormButton: false,\n        importJsonButton: true,\n        exportJsonButton: true,\n        exportCodeButton: false,\n        generateSFCButton: false\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        this.nodeForms = newVal || [];\n        this.loadFormPreviews();\n      },\n      immediate: true,\n      deep: true\n    },\n    nodeForms: {\n      handler(newVal) {\n        this.$emit('input', newVal);\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 添加节点表单 */\n    addNodeForm() {\n      const newNodeForm = {\n        id: `node_${Date.now()}`,\n        nodeName: `NPI节点${this.nodeForms.length + 1}`,\n        nodeType: 'npi_apply',\n        nodeKey: '',\n        formJson: null,\n        previewKey: 0\n      };\n      \n      this.nodeForms.push(newNodeForm);\n      this.activeNodes = newNodeForm.id;\n    },\n\n    /** 删除节点表单 */\n    removeNodeForm(index) {\n      this.$confirm('确定要删除这个节点表单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.nodeForms.splice(index, 1);\n        this.$message.success('删除成功');\n      }).catch(() => {});\n    },\n\n    /** 设计表单 */\n    designForm(nodeForm) {\n      this.currentEditNode = nodeForm;\n      this.showDesigner = true;\n      \n      this.$nextTick(() => {\n        if (nodeForm.formJson) {\n          this.$refs.vfDesigner.setFormJson(nodeForm.formJson);\n        } else {\n          // 设置默认的空表单\n          this.$refs.vfDesigner.setFormJson({\n            widgetList: [],\n            formConfig: {\n              modelName: 'formData',\n              refName: 'vForm',\n              rulesName: 'rules',\n              labelWidth: 80,\n              labelPosition: 'left',\n              size: '',\n              labelAlign: 'label-left-align',\n              cssCode: '',\n              customClass: '',\n              functions: '',\n              layoutType: 'PC'\n            }\n          });\n        }\n      });\n    },\n\n    /** 表单JSON变化 */\n    onFormJsonChange(formJson) {\n      // 实时保存表单设计\n      if (this.currentEditNode.id) {\n        const nodeForm = this.nodeForms.find(n => n.id === this.currentEditNode.id);\n        if (nodeForm) {\n          nodeForm.formJson = formJson;\n          nodeForm.previewKey = Date.now();\n        }\n      }\n    },\n\n    /** 保存表单设计 */\n    saveFormDesign() {\n      const formJson = this.$refs.vfDesigner.getFormJson();\n      const nodeForm = this.nodeForms.find(n => n.id === this.currentEditNode.id);\n      if (nodeForm) {\n        nodeForm.formJson = formJson;\n        nodeForm.previewKey = Date.now();\n        this.$message.success('表单保存成功');\n        this.loadFormPreviews();\n      }\n    },\n\n    /** 预览表单 */\n    previewForm() {\n      const formJson = this.$refs.vfDesigner.getFormJson();\n      this.showPreview = true;\n      this.$nextTick(() => {\n        this.$refs.previewFormRef.setFormJson(formJson);\n      });\n    },\n\n    /** 预览所有表单 */\n    previewAllForms() {\n      const formsWithJson = this.nodeForms.filter(n => n.formJson);\n      if (formsWithJson.length === 0) {\n        this.$message.warning('暂无已配置的表单');\n        return;\n      }\n      \n      this.previewActiveTab = formsWithJson[0].id;\n      this.showAllPreview = true;\n      \n      this.$nextTick(() => {\n        formsWithJson.forEach(nodeForm => {\n          const ref = this.$refs[`allPreview_${nodeForm.id}`];\n          if (ref && ref[0]) {\n            ref[0].setFormJson(nodeForm.formJson);\n          }\n        });\n      });\n    },\n\n    /** 加载表单预览 */\n    loadFormPreviews() {\n      this.$nextTick(() => {\n        this.nodeForms.forEach(nodeForm => {\n          if (nodeForm.formJson) {\n            const ref = this.$refs[`preview_${nodeForm.id}`];\n            if (ref && ref[0]) {\n              ref[0].setFormJson(nodeForm.formJson);\n              ref[0].disableForm();\n            }\n          }\n        });\n      });\n    },\n\n    /** 获取节点类型标签 */\n    getNodeTypeTag(type) {\n      const tagMap = {\n        npi_apply: 'primary',\n        tech_review: 'success',\n        process_review: 'warning',\n        quality_review: 'danger',\n        cost_review: 'info',\n        final_approval: 'primary'\n      };\n      return tagMap[type] || 'primary';\n    },\n\n    /** 获取节点类型文本 */\n    getNodeTypeText(type) {\n      const textMap = {\n        npi_apply: 'NPI申请',\n        tech_review: '技术评审',\n        process_review: '工艺评审',\n        quality_review: '质量评审',\n        cost_review: '成本评审',\n        final_approval: '最终审批'\n      };\n      return textMap[type] || type;\n    },\n\n    /** 保存配置 */\n    saveConfig() {\n      const config = {\n        processKey: this.processKey,\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      // 保存到本地存储\n      localStorage.setItem(`node_vform_config_${this.processKey}`, JSON.stringify(config));\n      \n      this.$message.success('配置保存成功');\n      this.$emit('save', config);\n    },\n\n    /** 导出配置 */\n    exportConfig() {\n      const config = {\n        processKey: this.processKey,\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      const blob = new Blob([JSON.stringify(config, null, 2)], { \n        type: 'application/json' \n      });\n      \n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `${this.processKey}_node_forms_${Date.now()}.json`;\n      a.click();\n      URL.revokeObjectURL(url);\n      \n      this.$message.success('导出成功');\n    },\n\n    /** 导入配置 */\n    importConfig() {\n      this.$refs.fileInput.click();\n    },\n\n    /** 处理文件导入 */\n    handleFileImport(event) {\n      const file = event.target.files[0];\n      if (!file) return;\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const config = JSON.parse(e.target.result);\n          if (config.nodeForms && Array.isArray(config.nodeForms)) {\n            this.nodeForms = config.nodeForms;\n            this.$message.success('导入成功');\n            this.loadFormPreviews();\n          } else {\n            this.$message.error('文件格式不正确');\n          }\n        } catch (error) {\n          this.$message.error('文件解析失败');\n        }\n      };\n      reader.readAsText(file);\n      \n      event.target.value = '';\n    },\n\n    /** 根据节点标识获取表单配置 */\n    getFormByNodeKey(nodeKey) {\n      return this.nodeForms.find(form => form.nodeKey === nodeKey);\n    },\n\n    /** 根据节点类型获取表单配置 */\n    getFormByNodeType(nodeType) {\n      return this.nodeForms.find(form => form.nodeType === nodeType);\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.node-vform-manager {\n  background-color: white;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.manager-header {\n  background-color: #F5F7FA;\n  padding: 16px 20px;\n  border-bottom: 1px solid #EBEEF5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h3 {\n    margin: 0;\n    color: #303133;\n    font-size: 16px;\n\n    i {\n      margin-right: 8px;\n      color: #409EFF;\n    }\n  }\n}\n\n.node-forms-list {\n  padding: 20px;\n}\n\n.node-title {\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  i {\n    margin-right: 8px;\n    color: #409EFF;\n  }\n\n  .node-name {\n    font-weight: 600;\n    margin-right: 12px;\n  }\n\n  .form-status {\n    margin-left: auto;\n    color: #909399;\n    font-size: 12px;\n  }\n}\n\n.node-form-content {\n  padding: 16px 0;\n}\n\n.node-config {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #FAFAFA;\n  border-radius: 4px;\n}\n\n.form-preview {\n  margin-top: 16px;\n  \n  h5 {\n    margin: 0 0 12px 0;\n    color: #606266;\n  }\n}\n\n.preview-container {\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  padding: 16px;\n  background-color: white;\n}\n\n.no-form {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n  border: 1px dashed #E4E7ED;\n  border-radius: 4px;\n\n  i {\n    font-size: 48px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.manager-footer {\n  padding: 16px 20px;\n  border-top: 1px solid #EBEEF5;\n  background-color: #FAFAFA;\n  text-align: right;\n\n  .el-button {\n    margin-left: 8px;\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n\n  i {\n    font-size: 64px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.vform-designer-container {\n  height: 70vh;\n  overflow: hidden;\n}\n\n.form-preview-dialog {\n  min-height: 300px;\n  padding: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiKA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,UAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,WAAA;MACAC,YAAA;MACAC,WAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,cAAA;QACAC,YAAA;QACAC,YAAA;QACAC,aAAA;QACAC,aAAA;QACAC,cAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,iBAAA;MACA;IACA;EACA;EACAC,KAAA;IACA1B,KAAA;MACA2B,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAArB,SAAA,GAAAqB,MAAA;QACA,KAAAC,gBAAA;MACA;MACAC,SAAA;MACAC,IAAA;IACA;IACAxB,SAAA;MACAoB,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAAI,KAAA,UAAAJ,MAAA;MACA;MACAG,IAAA;IACA;EACA;EACAE,OAAA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,IAAAC,WAAA;QACAC,EAAA,UAAAC,MAAA,CAAAC,IAAA,CAAAC,GAAA;QACAC,QAAA,oBAAAH,MAAA,MAAA9B,SAAA,CAAAkC,MAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;MACA;MAEA,KAAAtC,SAAA,CAAAuC,IAAA,CAAAX,WAAA;MACA,KAAA3B,WAAA,GAAA2B,WAAA,CAAAC,EAAA;IACA;IAEA,aACAW,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAnD,IAAA;MACA,GAAAoD,IAAA;QACAJ,KAAA,CAAA1C,SAAA,CAAA+C,MAAA,CAAAN,KAAA;QACAC,KAAA,CAAAM,QAAA,CAAAC,OAAA;MACA,GAAAC,KAAA;IACA;IAEA,WACAC,UAAA,WAAAA,WAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,KAAA/C,eAAA,GAAA8C,QAAA;MACA,KAAAlD,YAAA;MAEA,KAAAoD,SAAA;QACA,IAAAF,QAAA,CAAAf,QAAA;UACAgB,MAAA,CAAAE,KAAA,CAAAC,UAAA,CAAAC,WAAA,CAAAL,QAAA,CAAAf,QAAA;QACA;UACA;UACAgB,MAAA,CAAAE,KAAA,CAAAC,UAAA,CAAAC,WAAA;YACAC,UAAA;YACAC,UAAA;cACAC,SAAA;cACAC,OAAA;cACAC,SAAA;cACAC,UAAA;cACAC,aAAA;cACAC,IAAA;cACAC,UAAA;cACAC,OAAA;cACAC,WAAA;cACAC,SAAA;cACAC,UAAA;YACA;UACA;QACA;MACA;IACA;IAEA,eACAC,gBAAA,WAAAA,iBAAAlC,QAAA;MAAA,IAAAmC,MAAA;MACA;MACA,SAAAlE,eAAA,CAAAuB,EAAA;QACA,IAAAuB,QAAA,QAAApD,SAAA,CAAAyE,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA7C,EAAA,KAAA2C,MAAA,CAAAlE,eAAA,CAAAuB,EAAA;QAAA;QACA,IAAAuB,QAAA;UACAA,QAAA,CAAAf,QAAA,GAAAA,QAAA;UACAe,QAAA,CAAAd,UAAA,GAAAP,IAAA,CAAAC,GAAA;QACA;MACA;IACA;IAEA,aACA2C,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,IAAAvC,QAAA,QAAAkB,KAAA,CAAAC,UAAA,CAAAqB,WAAA;MACA,IAAAzB,QAAA,QAAApD,SAAA,CAAAyE,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA7C,EAAA,KAAA+C,MAAA,CAAAtE,eAAA,CAAAuB,EAAA;MAAA;MACA,IAAAuB,QAAA;QACAA,QAAA,CAAAf,QAAA,GAAAA,QAAA;QACAe,QAAA,CAAAd,UAAA,GAAAP,IAAA,CAAAC,GAAA;QACA,KAAAgB,QAAA,CAAAC,OAAA;QACA,KAAA3B,gBAAA;MACA;IACA;IAEA,WACAwD,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAA1C,QAAA,QAAAkB,KAAA,CAAAC,UAAA,CAAAqB,WAAA;MACA,KAAA1E,WAAA;MACA,KAAAmD,SAAA;QACAyB,MAAA,CAAAxB,KAAA,CAAAyB,cAAA,CAAAvB,WAAA,CAAApB,QAAA;MACA;IACA;IAEA,aACA4C,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,aAAA,QAAAnF,SAAA,CAAAoF,MAAA,WAAAV,CAAA;QAAA,OAAAA,CAAA,CAAArC,QAAA;MAAA;MACA,IAAA8C,aAAA,CAAAjD,MAAA;QACA,KAAAc,QAAA,CAAAqC,OAAA;QACA;MACA;MAEA,KAAAhF,gBAAA,GAAA8E,aAAA,IAAAtD,EAAA;MACA,KAAAzB,cAAA;MAEA,KAAAkD,SAAA;QACA6B,aAAA,CAAAG,OAAA,WAAAlC,QAAA;UACA,IAAAmC,GAAA,GAAAL,MAAA,CAAA3B,KAAA,eAAAzB,MAAA,CAAAsB,QAAA,CAAAvB,EAAA;UACA,IAAA0D,GAAA,IAAAA,GAAA;YACAA,GAAA,IAAA9B,WAAA,CAAAL,QAAA,CAAAf,QAAA;UACA;QACA;MACA;IACA;IAEA,aACAf,gBAAA,WAAAA,iBAAA;MAAA,IAAAkE,MAAA;MACA,KAAAlC,SAAA;QACAkC,MAAA,CAAAxF,SAAA,CAAAsF,OAAA,WAAAlC,QAAA;UACA,IAAAA,QAAA,CAAAf,QAAA;YACA,IAAAkD,GAAA,GAAAC,MAAA,CAAAjC,KAAA,YAAAzB,MAAA,CAAAsB,QAAA,CAAAvB,EAAA;YACA,IAAA0D,GAAA,IAAAA,GAAA;cACAA,GAAA,IAAA9B,WAAA,CAAAL,QAAA,CAAAf,QAAA;cACAkD,GAAA,IAAAE,WAAA;YACA;UACA;QACA;MACA;IACA;IAEA,eACAC,cAAA,WAAAA,eAAAhG,IAAA;MACA,IAAAiG,MAAA;QACAC,SAAA;QACAC,WAAA;QACAC,cAAA;QACAC,cAAA;QACAC,WAAA;QACAC,cAAA;MACA;MACA,OAAAN,MAAA,CAAAjG,IAAA;IACA;IAEA,eACAwG,eAAA,WAAAA,gBAAAxG,IAAA;MACA,IAAAyG,OAAA;QACAP,SAAA;QACAC,WAAA;QACAC,cAAA;QACAC,cAAA;QACAC,WAAA;QACAC,cAAA;MACA;MACA,OAAAE,OAAA,CAAAzG,IAAA,KAAAA,IAAA;IACA;IAEA,WACA0G,UAAA,WAAAA,WAAA;MACA,IAAAC,MAAA;QACAxG,UAAA,OAAAA,UAAA;QACAG,SAAA,OAAAA,SAAA;QACAsG,UAAA,MAAAvE,IAAA,GAAAwE,WAAA;QACAC,OAAA;MACA;;MAEA;MACAC,YAAA,CAAAC,OAAA,sBAAA5E,MAAA,MAAAjC,UAAA,GAAA8G,IAAA,CAAAC,SAAA,CAAAP,MAAA;MAEA,KAAArD,QAAA,CAAAC,OAAA;MACA,KAAAxB,KAAA,SAAA4E,MAAA;IACA;IAEA,WACAQ,YAAA,WAAAA,aAAA;MACA,IAAAR,MAAA;QACAxG,UAAA,OAAAA,UAAA;QACAG,SAAA,OAAAA,SAAA;QACAsG,UAAA,MAAAvE,IAAA,GAAAwE,WAAA;QACAC,OAAA;MACA;MAEA,IAAAM,IAAA,OAAAC,IAAA,EAAAJ,IAAA,CAAAC,SAAA,CAAAP,MAAA;QACA3G,IAAA;MACA;MAEA,IAAAsH,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,IAAA;MACA,IAAAK,CAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,CAAA,CAAAG,IAAA,GAAAN,GAAA;MACAG,CAAA,CAAAI,QAAA,MAAAzF,MAAA,MAAAjC,UAAA,kBAAAiC,MAAA,CAAAC,IAAA,CAAAC,GAAA;MACAmF,CAAA,CAAAK,KAAA;MACAP,GAAA,CAAAQ,eAAA,CAAAT,GAAA;MAEA,KAAAhE,QAAA,CAAAC,OAAA;IACA;IAEA,WACAyE,YAAA,WAAAA,aAAA;MACA,KAAAnE,KAAA,CAAAoE,SAAA,CAAAH,KAAA;IACA;IAEA,aACAI,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,KAAA,CAAAG,MAAA,CAAAC,KAAA;MACA,KAAAF,IAAA;MAEA,IAAAG,MAAA,OAAAC,UAAA;MACAD,MAAA,CAAAE,MAAA,aAAAC,CAAA;QACA;UACA,IAAAhC,MAAA,GAAAM,IAAA,CAAA2B,KAAA,CAAAD,CAAA,CAAAL,MAAA,CAAAO,MAAA;UACA,IAAAlC,MAAA,CAAArG,SAAA,IAAAL,KAAA,CAAA6I,OAAA,CAAAnC,MAAA,CAAArG,SAAA;YACA8H,MAAA,CAAA9H,SAAA,GAAAqG,MAAA,CAAArG,SAAA;YACA8H,MAAA,CAAA9E,QAAA,CAAAC,OAAA;YACA6E,MAAA,CAAAxG,gBAAA;UACA;YACAwG,MAAA,CAAA9E,QAAA,CAAAyF,KAAA;UACA;QACA,SAAAA,KAAA;UACAX,MAAA,CAAA9E,QAAA,CAAAyF,KAAA;QACA;MACA;MACAP,MAAA,CAAAQ,UAAA,CAAAX,IAAA;MAEAF,KAAA,CAAAG,MAAA,CAAAvI,KAAA;IACA;IAEA,mBACAkJ,gBAAA,WAAAA,iBAAAvG,OAAA;MACA,YAAApC,SAAA,CAAAyE,IAAA,WAAAmE,IAAA;QAAA,OAAAA,IAAA,CAAAxG,OAAA,KAAAA,OAAA;MAAA;IACA;IAEA,mBACAyG,iBAAA,WAAAA,kBAAA1G,QAAA;MACA,YAAAnC,SAAA,CAAAyE,IAAA,WAAAmE,IAAA;QAAA,OAAAA,IAAA,CAAAzG,QAAA,KAAAA,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}