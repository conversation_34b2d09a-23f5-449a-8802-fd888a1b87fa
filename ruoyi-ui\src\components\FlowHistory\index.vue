<template>
  <div v-if="flowRecordList && flowRecordList.length > 0" class="flow-history-container">
    <h4 class="history-title">
      <i class="el-icon-time"></i> 流程历史记录
    </h4>
    <el-collapse v-model="activeHistoryNames" class="history-collapse">
      <el-collapse-item
        v-for="(record, index) in flowRecordList"
        :key="`history-${index}-${record.taskId || record.id || index}`"
        :name="`history-${index}`"
      >
        <template slot="title">
          <div class="history-title-content">
            <i :class="getHistoryIcon(record)" :style="{ color: getHistoryColor(record) }"></i>
            <span class="node-name">{{ record.taskName || '未知节点' }}</span>
            <span class="assignee-name">{{ record.assigneeName || '未分配' }}</span>
            <span class="finish-time">{{ record.finishTime || '处理中' }}</span>
            <el-tag
              :type="getStatusTagType(record)"
              size="mini"
              class="status-tag"
            >
              {{ getStatusText(record) }}
            </el-tag>
          </div>
        </template>

        <div class="history-content">
          <!-- 节点VForm表单 -->
          <div v-if="getNodeVFormConfig(record)" class="node-form-section">
            <h5 class="form-section-title">
              <i class="el-icon-document"></i> {{ record.taskName }} - 专属表单
            </h5>
            <div class="vform-container">
              <v-form-render
                :ref="`nodeVForm_${record.taskId}`"
                :key="`vform_${record.taskId}_${vformKeys[record.taskId] || 0}`"
              />
            </div>
          </div>

          <!-- 通用数据视图（当没有专属表单时） -->
          <div v-else-if="record.taskId && nodeFormData[record.taskId]" class="node-form-section">
            <h5 class="form-section-title">
              <i class="el-icon-document"></i> {{ record.taskName }} - 数据记录
            </h5>
            <div class="form-data-container">
              <el-descriptions :column="2" size="small" border class="form-data-descriptions">
                <el-descriptions-item
                  v-for="(value, key) in getFormDataDisplay(record.taskId)"
                  :key="key"
                  :label="key"
                  :span="getFieldSpan(value)"
                >
                  <div class="form-field-value" :class="getFieldClass(value)">
                    {{ formatFieldValue(value) }}
                  </div>
                </el-descriptions-item>
              </el-descriptions>
              <div v-if="Object.keys(getFormDataDisplay(record.taskId)).length === 0" class="no-data">
                暂无表单数据
              </div>
            </div>
          </div>

          <!-- 办理信息 -->
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item v-if="record.assigneeName" label="办理人">
              <span>{{ record.assigneeName }}</span>
              <el-tag v-if="record.deptName" type="info" size="mini" style="margin-left: 8px;">{{ record.deptName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item v-if="record.candidate" label="候选办理">
              {{ record.candidate }}
            </el-descriptions-item>
            <el-descriptions-item v-if="record.createTime" label="接收时间">
              {{ record.createTime }}
            </el-descriptions-item>
            <el-descriptions-item v-if="record.finishTime" label="处理时间">
              {{ record.finishTime }}
            </el-descriptions-item>
            <el-descriptions-item v-if="record.duration" label="处理耗时">
              {{ record.duration }}
            </el-descriptions-item>
            <el-descriptions-item v-if="record.comment && record.comment.comment" label="处理意见" :span="2">
              <div class="comment-content">
                {{ record.comment.comment }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { getProcessVariables } from '@/api/flowable/definition'

export default {
  name: 'FlowHistory',
  props: {
    flowRecordList: {
      type: Array,
      default: () => []
    },
    defaultExpanded: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      activeHistoryNames: [],
      nodeFormData: {}, // 存储每个节点的表单数据
      loadingNodes: new Set(), // 正在加载的节点
      nodeVFormConfigs: [], // 节点VForm配置
      vformKeys: {} // VForm组件的key，用于强制重新渲染
    }
  },
  watch: {
    flowRecordList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          if (this.defaultExpanded) {
            // 默认展开第一个节点
            this.activeHistoryNames = ['history-0'];
          }
          // 加载所有节点的表单数据
          this.loadAllNodeForms();
        }
      },
      immediate: true
    },
    activeHistoryNames: {
      handler(newVal) {
        // 当展开节点时，加载对应的表单数据
        newVal.forEach(name => {
          const index = parseInt(name.replace('history-', ''));
          const record = this.flowRecordList[index];
          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {
            this.loadNodeForm(record.taskId);
          }
        });
      }
    }
  },
  methods: {
    /** 获取历史节点图标 */
    getHistoryIcon(record) {
      if (record.finishTime) {
        return 'el-icon-check';
      } else {
        return 'el-icon-time';
      }
    },

    /** 获取历史节点颜色 */
    getHistoryColor(record) {
      if (record.finishTime) {
        return '#67C23A';
      } else {
        return '#E6A23C';
      }
    },

    /** 获取状态标签类型 */
    getStatusTagType(record) {
      if (record.finishTime) {
        return 'success';
      } else {
        return 'warning';
      }
    },

    /** 获取状态文本 */
    getStatusText(record) {
      if (record.finishTime) {
        return '已完成';
      } else {
        return '处理中';
      }
    },

    /** 加载所有节点的表单数据 */
    loadAllNodeForms() {
      this.flowRecordList.forEach(record => {
        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {
          this.loadNodeForm(record.taskId);
        }
      });
    },

    /** 加载单个节点的表单数据 */
    loadNodeForm(taskId) {
      if (!taskId || this.loadingNodes.has(taskId)) {
        return;
      }

      this.loadingNodes.add(taskId);

      getProcessVariables(taskId).then(res => {
        if (res.data) {
          // 设置表单数据
          this.$set(this.nodeFormData, taskId, res.data);

          // 如果有对应的VForm配置，则设置VForm
          const record = this.flowRecordList.find(r => r.taskId === taskId);
          if (record) {
            const vformConfig = this.getNodeVFormConfig(record);
            if (vformConfig && vformConfig.formJson) {
              this.$set(this.vformKeys, taskId, Date.now());

              this.$nextTick(() => {
                const vformRef = this.$refs[`nodeVForm_${taskId}`];
                if (vformRef && vformRef[0]) {
                  // 设置表单JSON
                  vformRef[0].setFormJson(vformConfig.formJson);
                  this.$nextTick(() => {
                    // 设置表单数据
                    vformRef[0].setFormData(res.data);
                    this.$nextTick(() => {
                      // 禁用表单
                      vformRef[0].disableForm();
                    });
                  });
                }
              });
            }
          }
        }
      }).catch(error => {
        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);
      }).finally(() => {
        this.loadingNodes.delete(taskId);
      });
    },

    /** 获取表单数据用于显示 */
    getFormDataDisplay(taskId) {
      const nodeData = this.nodeFormData[taskId];
      if (!nodeData) return {};

      console.log('节点数据:', taskId, nodeData); // 调试信息

      // 系统字段列表
      const systemFields = ['formJson', 'taskId', 'procInsId', 'deployId', 'procDefId', 'instanceId'];

      const filteredData = {};
      Object.keys(nodeData).forEach(key => {
        const value = nodeData[key];

        // 跳过系统字段
        if (systemFields.includes(key)) {
          return;
        }

        // 包含所有非空值，包括数字0、false等有意义的值
        if (value !== null && value !== undefined && value !== '') {
          const label = this.getFieldLabel(key, nodeData.formJson);
          filteredData[label] = value;
        }
      });

      console.log('过滤后的数据:', filteredData); // 调试信息
      return filteredData;
    },

    /** 获取字段标签 */
    getFieldLabel(fieldKey, formJson) {
      if (!formJson) {
        return fieldKey;
      }

      // 尝试多种可能的表单结构
      let widgets = [];

      if (formJson.widgetList) {
        widgets = formJson.widgetList;
      } else if (formJson.formConfig && formJson.formConfig.widgetList) {
        widgets = formJson.formConfig.widgetList;
      } else if (Array.isArray(formJson)) {
        widgets = formJson;
      }

      console.log('查找字段标签:', fieldKey, '在widgets:', widgets); // 调试信息

      // 在表单组件中查找字段标签
      const widget = widgets.find(w => {
        if (w.options && w.options.name === fieldKey) return true;
        if (w.__config__ && w.__config__.formId === fieldKey) return true;
        if (w.__vModel__ === fieldKey) return true;
        return false;
      });

      if (widget) {
        // 尝试多种可能的标签字段
        const label = widget.options?.label ||
                     widget.__config__?.label ||
                     widget.label ||
                     fieldKey;
        console.log('找到标签:', fieldKey, '->', label); // 调试信息
        return label;
      }

      return fieldKey;
    },

    /** 格式化字段值 */
    formatFieldValue(value) {
      if (Array.isArray(value)) {
        return value.join(', ');
      }
      if (typeof value === 'object') {
        return JSON.stringify(value);
      }
      if (typeof value === 'boolean') {
        return value ? '是' : '否';
      }
      return String(value);
    },

    /** 获取字段跨度 */
    getFieldSpan(value) {
      const valueStr = this.formatFieldValue(value);
      // 长文本占用两列
      return valueStr.length > 20 ? 2 : 1;
    },

    /** 获取字段样式类 */
    getFieldClass(value) {
      if (typeof value === 'boolean') {
        return value ? 'field-boolean-true' : 'field-boolean-false';
      }
      if (typeof value === 'number') {
        return 'field-number';
      }
      if (Array.isArray(value)) {
        return 'field-array';
      }
      return 'field-text';
    },

    /** 获取节点VForm配置 */
    getNodeVFormConfig(record) {
      // 从本地存储加载VForm配置
      if (this.nodeVFormConfigs.length === 0) {
        this.loadNodeVFormConfigs();
      }

      // 根据节点标识或任务名称查找对应的VForm配置
      const config = this.nodeVFormConfigs.find(config =>
        config.nodeKey === record.taskDefKey ||
        config.nodeName === record.taskName ||
        this.matchNodeByType(config, record)
      );

      return config;
    },

    /** 根据类型匹配节点 */
    matchNodeByType(config, record) {
      const taskName = record.taskName || '';
      const nodeTypeMap = {
        'npi_apply': ['申请', 'NPI申请'],
        'tech_review': ['技术评审', '技术审核'],
        'process_review': ['工艺评审', '工艺审核'],
        'quality_review': ['质量评审', '质量审核'],
        'cost_review': ['成本评审', '成本审核'],
        'final_approval': ['最终审批', '终审']
      };

      const keywords = nodeTypeMap[config.nodeType] || [];
      return keywords.some(keyword => taskName.includes(keyword));
    },

    /** 加载节点VForm配置 */
    loadNodeVFormConfigs() {
      try {
        // 加载NPI流程的VForm配置
        const saved = localStorage.getItem('node_vform_config_npi_process');
        if (saved) {
          const config = JSON.parse(saved);
          this.nodeVFormConfigs = config.nodeForms || [];
        }
      } catch (error) {
        console.warn('加载节点VForm配置失败:', error);
        this.nodeVFormConfigs = [];
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-history-container {
  margin-bottom: 20px;
}

.history-title {
  margin-bottom: 15px;
  color: #606266;
  font-size: 16px;
  font-weight: 600;
}

/* 历史节点样式 */
.history-collapse {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
}

.history-collapse .el-collapse-item__header {
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
  padding: 0 20px;
  height: 48px;
  line-height: 48px;
}

.history-collapse .el-collapse-item__content {
  padding: 20px;
  background-color: #FAFAFA;
}

.history-title-content {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 14px;
}

.history-title-content .node-name {
  font-weight: 600;
  margin-left: 8px;
  margin-right: 15px;
  color: #303133;
}

.history-title-content .assignee-name {
  color: #606266;
  margin-right: 15px;
}

.history-title-content .finish-time {
  color: #909399;
  font-size: 12px;
  margin-left: auto;
  margin-right: 10px;
}

.status-tag {
  margin-left: 10px;
}

.history-content {
  background-color: white;
  border-radius: 4px;
  padding: 16px;
}

.comment-content {
  background-color: #F8F9FA;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
  font-style: italic;
  color: #606266;
}

/* 节点表单样式 */
.node-form-section {
  margin-bottom: 20px;
  border: 1px solid #E4E7ED;
  border-radius: 4px;
  overflow: hidden;
}

.form-section-title {
  background-color: #F5F7FA;
  padding: 12px 16px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  border-bottom: 1px solid #E4E7ED;
}

.form-section-title i {
  margin-right: 8px;
  color: #409EFF;
}

/* VForm容器样式 */
.vform-container {
  padding: 16px;
  background-color: white;
}

/* 通用数据容器样式 */
.form-data-container {
  padding: 16px;
  background-color: #FAFAFA;
}

.form-data-descriptions {
  background-color: white;
}

/* 表单字段值样式 */
.form-field-value {
  word-break: break-word;
  line-height: 1.5;
}

.field-boolean-true {
  color: #67C23A;
  font-weight: 600;
}

.field-boolean-false {
  color: #F56C6C;
  font-weight: 600;
}

.field-number {
  color: #E6A23C;
  font-weight: 500;
}

.field-array {
  color: #909399;
  font-style: italic;
}

.field-text {
  color: #606266;
}

/* 原始数据显示样式 */
.raw-data-container {
  background-color: #f8f8f8;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.raw-data-container pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px;
  font-style: italic;
}
</style>
