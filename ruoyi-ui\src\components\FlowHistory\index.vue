<template>
  <div v-if="flowRecordList && flowRecordList.length > 0" class="flow-history-container">
    <h4 class="history-title">
      <i class="el-icon-time"></i> 流程历史记录
    </h4>
    <el-collapse v-model="activeHistoryNames" class="history-collapse">
      <el-collapse-item
        v-for="(record, index) in flowRecordList"
        :key="`history-${index}-${record.taskId || record.id || index}`"
        :name="`history-${index}`"
      >
        <template slot="title">
          <div class="history-title-content">
            <i :class="getHistoryIcon(record)" :style="{ color: getHistoryColor(record) }"></i>
            <span class="node-name">{{ record.taskName || '未知节点' }}</span>
            <span class="assignee-name">{{ record.assigneeName || '未分配' }}</span>
            <span class="finish-time">{{ record.finishTime || '处理中' }}</span>
            <el-tag
              :type="getStatusTagType(record)"
              size="mini"
              class="status-tag"
            >
              {{ getStatusText(record) }}
            </el-tag>
          </div>
        </template>

        <div class="history-content">
          <!-- 节点表单 -->
          <div v-if="record.taskId && nodeFormData[record.taskId]" class="node-form-section">
            <h5 class="form-section-title">
              <i class="el-icon-document"></i> 节点表单
            </h5>
            <div class="form-container">
              <v-form-render
                :ref="`nodeForm_${record.taskId}`"
                :key="`form_${record.taskId}_${formKeys[record.taskId] || 0}`"
              />
            </div>
          </div>

          <!-- 办理信息 -->
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item v-if="record.assigneeName" label="办理人">
              <span>{{ record.assigneeName }}</span>
              <el-tag v-if="record.deptName" type="info" size="mini" style="margin-left: 8px;">{{ record.deptName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item v-if="record.candidate" label="候选办理">
              {{ record.candidate }}
            </el-descriptions-item>
            <el-descriptions-item v-if="record.createTime" label="接收时间">
              {{ record.createTime }}
            </el-descriptions-item>
            <el-descriptions-item v-if="record.finishTime" label="处理时间">
              {{ record.finishTime }}
            </el-descriptions-item>
            <el-descriptions-item v-if="record.duration" label="处理耗时">
              {{ record.duration }}
            </el-descriptions-item>
            <el-descriptions-item v-if="record.comment && record.comment.comment" label="处理意见" :span="2">
              <div class="comment-content">
                {{ record.comment.comment }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { getProcessVariables } from '@/api/flowable/definition'

export default {
  name: 'FlowHistory',
  props: {
    flowRecordList: {
      type: Array,
      default: () => []
    },
    defaultExpanded: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      activeHistoryNames: [],
      nodeFormData: {}, // 存储每个节点的表单数据
      formKeys: {}, // 存储每个表单的key，用于强制重新渲染
      loadingNodes: new Set() // 正在加载的节点
    }
  },
  watch: {
    flowRecordList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          if (this.defaultExpanded) {
            // 默认展开第一个节点
            this.activeHistoryNames = ['history-0'];
          }
          // 加载所有节点的表单数据
          this.loadAllNodeForms();
        }
      },
      immediate: true
    },
    activeHistoryNames: {
      handler(newVal) {
        // 当展开节点时，加载对应的表单数据
        newVal.forEach(name => {
          const index = parseInt(name.replace('history-', ''));
          const record = this.flowRecordList[index];
          if (record && record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {
            this.loadNodeForm(record.taskId);
          }
        });
      }
    }
  },
  methods: {
    /** 获取历史节点图标 */
    getHistoryIcon(record) {
      if (record.finishTime) {
        return 'el-icon-check';
      } else {
        return 'el-icon-time';
      }
    },

    /** 获取历史节点颜色 */
    getHistoryColor(record) {
      if (record.finishTime) {
        return '#67C23A';
      } else {
        return '#E6A23C';
      }
    },

    /** 获取状态标签类型 */
    getStatusTagType(record) {
      if (record.finishTime) {
        return 'success';
      } else {
        return 'warning';
      }
    },

    /** 获取状态文本 */
    getStatusText(record) {
      if (record.finishTime) {
        return '已完成';
      } else {
        return '处理中';
      }
    },

    /** 加载所有节点的表单数据 */
    loadAllNodeForms() {
      this.flowRecordList.forEach(record => {
        if (record.taskId && !this.nodeFormData[record.taskId] && !this.loadingNodes.has(record.taskId)) {
          this.loadNodeForm(record.taskId);
        }
      });
    },

    /** 加载单个节点的表单数据 */
    loadNodeForm(taskId) {
      if (!taskId || this.loadingNodes.has(taskId)) {
        return;
      }

      this.loadingNodes.add(taskId);

      getProcessVariables(taskId).then(res => {
        if (res.data && res.data.formJson) {
          // 设置表单数据
          this.$set(this.nodeFormData, taskId, res.data);
          this.$set(this.formKeys, taskId, Date.now());

          this.$nextTick(() => {
            const formRef = this.$refs[`nodeForm_${taskId}`];
            if (formRef && formRef[0]) {
              // 设置表单JSON
              formRef[0].setFormJson(res.data.formJson);
              this.$nextTick(() => {
                // 设置表单数据
                formRef[0].setFormData(res.data);
                this.$nextTick(() => {
                  // 禁用表单
                  formRef[0].disableForm();
                });
              });
            }
          });
        }
      }).catch(error => {
        console.warn(`加载节点 ${taskId} 的表单数据失败:`, error);
      }).finally(() => {
        this.loadingNodes.delete(taskId);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-history-container {
  margin-bottom: 20px;
}

.history-title {
  margin-bottom: 15px;
  color: #606266;
  font-size: 16px;
  font-weight: 600;
}

/* 历史节点样式 */
.history-collapse {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
}

.history-collapse .el-collapse-item__header {
  background-color: #F5F7FA;
  border-bottom: 1px solid #EBEEF5;
  padding: 0 20px;
  height: 48px;
  line-height: 48px;
}

.history-collapse .el-collapse-item__content {
  padding: 20px;
  background-color: #FAFAFA;
}

.history-title-content {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 14px;
}

.history-title-content .node-name {
  font-weight: 600;
  margin-left: 8px;
  margin-right: 15px;
  color: #303133;
}

.history-title-content .assignee-name {
  color: #606266;
  margin-right: 15px;
}

.history-title-content .finish-time {
  color: #909399;
  font-size: 12px;
  margin-left: auto;
  margin-right: 10px;
}

.status-tag {
  margin-left: 10px;
}

.history-content {
  background-color: white;
  border-radius: 4px;
  padding: 16px;
}

.comment-content {
  background-color: #F8F9FA;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
  font-style: italic;
  color: #606266;
}

/* 节点表单样式 */
.node-form-section {
  margin-bottom: 20px;
  border: 1px solid #E4E7ED;
  border-radius: 4px;
  overflow: hidden;
}

.form-section-title {
  background-color: #F5F7FA;
  padding: 12px 16px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  border-bottom: 1px solid #E4E7ED;
}

.form-section-title i {
  margin-right: 8px;
  color: #409EFF;
}

.form-container {
  padding: 16px;
  background-color: #FAFAFA;
}
</style>
