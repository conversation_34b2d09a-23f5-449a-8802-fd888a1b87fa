{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\index.vue?vue&type=style&index=0&id=b06e787c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\index.vue", "mtime": 1752386577717}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZm9ybS1kZXNpZ25lciB7CiAgaGVpZ2h0OiAxMDB2aDsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgYmFja2dyb3VuZDogI2Y1ZjVmNTsKCiAgLmRlc2lnbmVyLWhlYWRlciB7CiAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgcGFkZGluZzogMTBweCAyMHB4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpOwogIH0KCiAgLmRlc2lnbmVyLWJvZHkgewogICAgZmxleDogMTsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA4MHB4KTsKICAgIG92ZXJmbG93OiBoaWRkZW47CgogICAgLmNvbXBvbmVudHMtcGFuZWwgewogICAgICB3aWR0aDogMjYwcHg7CiAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgICAgIG92ZXJmbG93LXk6IGF1dG87CgogICAgICAucGFuZWwtdGl0bGUgewogICAgICAgIHBhZGRpbmc6IDE1cHg7CiAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgIGNvbG9yOiAjMzAzMTMzOwogICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwogICAgICAgIGJhY2tncm91bmQ6ICNmYWZhZmE7CiAgICAgIH0KCiAgICAgIC5jb21wb25lbnQtZ3JvdXBzIHsKICAgICAgICAuY29tcG9uZW50LWdyb3VwIHsKICAgICAgICAgIC5ncm91cC10aXRsZSB7CiAgICAgICAgICAgIHBhZGRpbmc6IDEwcHggMTVweDsKICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgICBmb250LXdlaWdodDogNjAwOwogICAgICAgICAgICBjb2xvcjogIzYwNjI2NjsKICAgICAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgICAgICAgICB9CgogICAgICAgICAgLmNvbXBvbmVudC1saXN0IHsKICAgICAgICAgICAgcGFkZGluZzogMTBweDsKCiAgICAgICAgICAgIC5jb21wb25lbnQtaXRlbSB7CiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAgICAgIHBhZGRpbmc6IDhweCAxMnB4OwogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDsKICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICAgICAgICAgIGN1cnNvcjogZ3JhYjsKICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKCiAgICAgICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICM0MDlFRkY7CiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjBmOWZmOwogICAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOwogICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjIpOwogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgJjphY3RpdmUgewogICAgICAgICAgICAgICAgY3Vyc29yOiBncmFiYmluZzsKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIGkgewogICAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgICAgICAgICAgICBjb2xvcjogIzQwOUVGRjsKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIHNwYW4gewogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICAgICAgICAgICAgY29sb3I6ICM2MDYyNjY7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9CgogICAgLmRlc2lnbi1wYW5lbCB7CiAgICAgIGZsZXg6IDE7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICAgIG1hcmdpbjogMCAxcHg7CgogICAgICAucGFuZWwtdGl0bGUgewogICAgICAgIHBhZGRpbmc6IDE1cHg7CiAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgIGNvbG9yOiAjMzAzMTMzOwogICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwogICAgICAgIGJhY2tncm91bmQ6ICNmYWZhZmE7CiAgICAgIH0KCiAgICAgIC5kZXNpZ24tY2FudmFzIHsKICAgICAgICBmbGV4OiAxOwogICAgICAgIHBhZGRpbmc6IDIwcHg7CiAgICAgICAgb3ZlcmZsb3cteTogYXV0bzsKICAgICAgICBtaW4taGVpZ2h0OiA0MDBweDsKICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CgogICAgICAgIC5lbXB0eS1jYW52YXMgewogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgICBoZWlnaHQ6IDMwMHB4OwogICAgICAgICAgYm9yZGVyOiAycHggZGFzaGVkICNjMGM0Y2M7CiAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7CiAgICAgICAgICBjb2xvcjogIzkwOTM5OTsKICAgICAgICAgIGJhY2tncm91bmQ6ICNmYWZiZmM7CgogICAgICAgICAgaSB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogNDhweDsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsKICAgICAgICAgICAgY29sb3I6ICNjMGM0Y2M7CiAgICAgICAgICB9CgogICAgICAgICAgcCB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICAgICAgbWFyZ2luOiAwOwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLmdob3N0IHsKICAgICAgICAgIG9wYWNpdHk6IDAuNTsKICAgICAgICAgIGJhY2tncm91bmQ6ICM0MDlFRkY7CiAgICAgICAgfQoKICAgICAgICAuY2hvc2VuIHsKICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICM0MDlFRkYgIWltcG9ydGFudDsKICAgICAgICB9CiAgICAgIH0KICAgIH0KCiAgICAucHJvcGVydGllcy1wYW5lbCB7CiAgICAgIHdpZHRoOiAzMjBweDsKICAgICAgYmFja2dyb3VuZDogI2ZmZjsKICAgICAgYm9yZGVyLWxlZnQ6IDFweCBzb2xpZCAjZTRlN2VkOwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwoKICAgICAgLnBhbmVsLXRpdGxlIHsKICAgICAgICBwYWRkaW5nOiAxNXB4OwogICAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U0ZTdlZDsKICAgICAgICBiYWNrZ3JvdW5kOiAjZmFmYWZhOwogICAgICB9CgogICAgICAucHJvcGVydGllcy1jb250ZW50IHsKICAgICAgICBmbGV4OiAxOwogICAgICAgIG92ZXJmbG93LXk6IGF1dG87CgogICAgICAgIC5uby1zZWxlY3Rpb24gewogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgICBoZWlnaHQ6IDIwMHB4OwogICAgICAgICAgY29sb3I6ICM5MDkzOTk7CgogICAgICAgICAgaSB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogNDhweDsKICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsKICAgICAgICAgICAgY29sb3I6ICNjMGM0Y2M7CiAgICAgICAgICB9CgogICAgICAgICAgcCB7CiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICAgICAgbWFyZ2luOiAwOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQoKLy8g5YWo5bGA5qC35byPCjpkZWVwKC5lbC10YWJzLS1ib3JkZXItY2FyZCkgewogIGJvcmRlcjogbm9uZTsKICBib3gtc2hhZG93OiBub25lOwoKICAuZWwtdGFic19faGVhZGVyIHsKICAgIGJhY2tncm91bmQ6ICNmYWZhZmE7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U0ZTdlZDsKICAgIG1hcmdpbjogMDsKICB9CgogIC5lbC10YWJzX19jb250ZW50IHsKICAgIHBhZGRpbmc6IDE1cHg7CiAgfQp9Cgo6ZGVlcCguZWwtZm9ybS1pdGVtKSB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQoKOmRlZXAoLmVsLWlucHV0KSwgOmRlZXAoLmVsLXNlbGVjdCksIDpkZWVwKC5lbC1kYXRlLWVkaXRvcikgewogIHdpZHRoOiAxMDAlOwp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqVA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FormDesigner", "sourcesContent": ["<template>\n  <div class=\"form-designer\">\n    <div class=\"designer-header\">\n      <el-form :model=\"formConfig\" :inline=\"true\" size=\"small\">\n        <el-form-item label=\"表单名称\">\n          <el-input v-model=\"formConfig.formName\" placeholder=\"请输入表单名称\" style=\"width: 200px\" />\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"formConfig.remark\" placeholder=\"请输入备注\" style=\"width: 200px\" />\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"handleSave\">保存表单</el-button>\n          <el-button @click=\"handleCancel\">取消</el-button>\n          <el-button type=\"success\" @click=\"handlePreview\">预览</el-button>\n          <el-button type=\"info\" @click=\"handleExportJson\">导出JSON</el-button>\n          <el-button type=\"warning\" @click=\"handleImportJson\">导入JSON</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <div class=\"designer-body\">\n      <!-- 左侧组件面板 -->\n      <div class=\"components-panel\">\n        <div class=\"panel-title\">组件库</div>\n        <div class=\"component-groups\">\n          <!-- 基础组件 -->\n          <div class=\"component-group\">\n            <div class=\"group-title\">基础组件</div>\n            <div class=\"component-list\">\n              <div \n                v-for=\"component in basicComponents\" \n                :key=\"component.type\"\n                class=\"component-item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, component)\"\n              >\n                <i :class=\"component.icon\"></i>\n                <span>{{ component.label }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 高级组件 -->\n          <div class=\"component-group\">\n            <div class=\"group-title\">高级组件</div>\n            <div class=\"component-list\">\n              <div \n                v-for=\"component in advancedComponents\" \n                :key=\"component.type\"\n                class=\"component-item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, component)\"\n              >\n                <i :class=\"component.icon\"></i>\n                <span>{{ component.label }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 布局组件 -->\n          <div class=\"component-group\">\n            <div class=\"group-title\">布局组件</div>\n            <div class=\"component-list\">\n              <div \n                v-for=\"component in layoutComponents\" \n                :key=\"component.type\"\n                class=\"component-item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, component)\"\n              >\n                <i :class=\"component.icon\"></i>\n                <span>{{ component.label }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 中间设计区域 -->\n      <div class=\"design-panel\">\n        <div class=\"panel-title\">表单设计区域</div>\n        <div \n          class=\"design-canvas\"\n          @drop=\"handleDrop\"\n          @dragover=\"handleDragOver\"\n          @click=\"clearSelection\"\n        >\n          <div v-if=\"formItems.length === 0\" class=\"empty-canvas\">\n            <i class=\"el-icon-plus\"></i>\n            <p>从左侧拖拽组件到此处开始设计表单</p>\n          </div>\n          \n          <draggable \n            v-model=\"formItems\" \n            group=\"form-items\"\n            :animation=\"200\"\n            ghost-class=\"ghost\"\n            chosen-class=\"chosen\"\n            @end=\"handleSortEnd\"\n          >\n            <form-item-wrapper\n              v-for=\"(item, index) in formItems\"\n              :key=\"item.id\"\n              :item=\"item\"\n              :index=\"index\"\n              :selected=\"selectedItem && selectedItem.id === item.id\"\n              @select=\"handleSelectItem\"\n              @delete=\"handleDeleteItem\"\n              @clone=\"handleCloneItem\"\n            />\n          </draggable>\n        </div>\n      </div>\n\n      <!-- 右侧属性面板 -->\n      <div class=\"properties-panel\">\n        <div class=\"panel-title\">属性配置</div>\n        <div class=\"properties-content\">\n          <form-properties\n            v-if=\"selectedItem\"\n            :item=\"selectedItem\"\n            @update=\"handleUpdateProperties\"\n          />\n          <div v-else class=\"no-selection\">\n            <i class=\"el-icon-info\"></i>\n            <p>请选择一个组件进行配置</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewVisible\" width=\"80%\" append-to-body>\n      <form-preview :form-items=\"formItems\" />\n    </el-dialog>\n\n    <!-- JSON导入对话框 -->\n    <el-dialog title=\"导入JSON\" :visible.sync=\"importVisible\" width=\"60%\" append-to-body>\n      <el-input\n        v-model=\"importJson\"\n        type=\"textarea\"\n        :rows=\"10\"\n        placeholder=\"请粘贴表单JSON配置\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"importVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleImportConfirm\">确定导入</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport draggable from 'vuedraggable'\nimport FormItemWrapper from './components/FormItemWrapper.vue'\nimport FormProperties from './components/FormProperties.vue'\nimport FormPreview from './components/FormPreview.vue'\nimport { generateId } from './utils/index.js'\nimport { basicComponents, advancedComponents, layoutComponents } from './config/components.js'\n\nexport default {\n  name: 'FormDesigner',\n  components: {\n    draggable,\n    FormItemWrapper,\n    FormProperties,\n    FormPreview\n  },\n  props: {\n    formData: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      formConfig: {\n        formName: '',\n        remark: ''\n      },\n      formItems: [],\n      selectedItem: null,\n      previewVisible: false,\n      importVisible: false,\n      importJson: '',\n      basicComponents,\n      advancedComponents,\n      layoutComponents\n    }\n  },\n  created() {\n    this.initFormData()\n  },\n  methods: {\n    initFormData() {\n      if (this.formData.formId) {\n        this.formConfig.formName = this.formData.formName || ''\n        this.formConfig.remark = this.formData.remark || ''\n        \n        // 解析表单内容\n        if (this.formData.formContent) {\n          try {\n            const content = JSON.parse(this.formData.formContent)\n            this.formItems = content.formItems || []\n          } catch (e) {\n            console.warn('表单内容解析失败:', e)\n          }\n        }\n      }\n    },\n\n    handleDragStart(event, component) {\n      event.dataTransfer.setData('component', JSON.stringify(component))\n    },\n\n    handleDragOver(event) {\n      event.preventDefault()\n    },\n\n    handleDrop(event) {\n      event.preventDefault()\n      const componentData = event.dataTransfer.getData('component')\n      if (componentData) {\n        const component = JSON.parse(componentData)\n        this.addFormItem(component)\n      }\n    },\n\n    addFormItem(component) {\n      const newItem = {\n        id: generateId(),\n        type: component.type,\n        label: component.label,\n        icon: component.icon,\n        ...component.defaultProps\n      }\n      this.formItems.push(newItem)\n      this.selectedItem = newItem\n    },\n\n    handleSelectItem(item) {\n      this.selectedItem = item\n    },\n\n    handleDeleteItem(index) {\n      this.formItems.splice(index, 1)\n      if (this.selectedItem && this.selectedItem.id === this.formItems[index]?.id) {\n        this.selectedItem = null\n      }\n    },\n\n    handleCloneItem(item) {\n      const clonedItem = {\n        ...JSON.parse(JSON.stringify(item)),\n        id: generateId()\n      }\n      const index = this.formItems.findIndex(i => i.id === item.id)\n      this.formItems.splice(index + 1, 0, clonedItem)\n    },\n\n    handleUpdateProperties(properties) {\n      if (this.selectedItem) {\n        Object.assign(this.selectedItem, properties)\n      }\n    },\n\n    handleSortEnd() {\n      // 拖拽排序完成后的处理\n    },\n\n    clearSelection() {\n      this.selectedItem = null\n    },\n\n    handleSave() {\n      if (!this.formConfig.formName) {\n        this.$message.error('请输入表单名称')\n        return\n      }\n\n      const formData = {\n        ...this.formData,\n        formName: this.formConfig.formName,\n        remark: this.formConfig.remark,\n        formContent: JSON.stringify({\n          formItems: this.formItems,\n          config: this.formConfig\n        })\n      }\n\n      this.$emit('save', formData)\n    },\n\n    handleCancel() {\n      this.$emit('cancel')\n    },\n\n    handlePreview() {\n      this.previewVisible = true\n    },\n\n    handleExportJson() {\n      const exportData = {\n        formItems: this.formItems,\n        config: this.formConfig\n      }\n      \n      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })\n      const url = URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = `${this.formConfig.formName || 'form'}.json`\n      a.click()\n      URL.revokeObjectURL(url)\n    },\n\n    handleImportJson() {\n      this.importVisible = true\n      this.importJson = ''\n    },\n\n    handleImportConfirm() {\n      try {\n        const data = JSON.parse(this.importJson)\n        if (data.formItems) {\n          this.formItems = data.formItems\n        }\n        if (data.config) {\n          this.formConfig = { ...this.formConfig, ...data.config }\n        }\n        this.importVisible = false\n        this.$message.success('导入成功')\n      } catch (e) {\n        this.$message.error('JSON格式错误，请检查后重试')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-designer {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n\n  .designer-header {\n    background: #fff;\n    padding: 10px 20px;\n    border-bottom: 1px solid #e4e7ed;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  }\n\n  .designer-body {\n    flex: 1;\n    display: flex;\n    height: calc(100vh - 80px);\n    overflow: hidden;\n\n    .components-panel {\n      width: 260px;\n      background: #fff;\n      border-right: 1px solid #e4e7ed;\n      overflow-y: auto;\n\n      .panel-title {\n        padding: 15px;\n        font-size: 16px;\n        font-weight: bold;\n        color: #303133;\n        border-bottom: 1px solid #e4e7ed;\n        background: #fafafa;\n      }\n\n      .component-groups {\n        .component-group {\n          .group-title {\n            padding: 10px 15px;\n            font-size: 14px;\n            font-weight: 600;\n            color: #606266;\n            background: #f8f9fa;\n            border-bottom: 1px solid #e4e7ed;\n          }\n\n          .component-list {\n            padding: 10px;\n\n            .component-item {\n              display: flex;\n              align-items: center;\n              padding: 8px 12px;\n              margin-bottom: 8px;\n              background: #fff;\n              border: 1px solid #e4e7ed;\n              border-radius: 4px;\n              cursor: grab;\n              transition: all 0.3s;\n\n              &:hover {\n                border-color: #409EFF;\n                background: #f0f9ff;\n                transform: translateY(-1px);\n                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n              }\n\n              &:active {\n                cursor: grabbing;\n              }\n\n              i {\n                margin-right: 8px;\n                font-size: 16px;\n                color: #409EFF;\n              }\n\n              span {\n                font-size: 13px;\n                color: #606266;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .design-panel {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      background: #fff;\n      margin: 0 1px;\n\n      .panel-title {\n        padding: 15px;\n        font-size: 16px;\n        font-weight: bold;\n        color: #303133;\n        border-bottom: 1px solid #e4e7ed;\n        background: #fafafa;\n      }\n\n      .design-canvas {\n        flex: 1;\n        padding: 20px;\n        overflow-y: auto;\n        min-height: 400px;\n        position: relative;\n\n        .empty-canvas {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          height: 300px;\n          border: 2px dashed #c0c4cc;\n          border-radius: 8px;\n          color: #909399;\n          background: #fafbfc;\n\n          i {\n            font-size: 48px;\n            margin-bottom: 16px;\n            color: #c0c4cc;\n          }\n\n          p {\n            font-size: 14px;\n            margin: 0;\n          }\n        }\n\n        .ghost {\n          opacity: 0.5;\n          background: #409EFF;\n        }\n\n        .chosen {\n          border: 2px solid #409EFF !important;\n        }\n      }\n    }\n\n    .properties-panel {\n      width: 320px;\n      background: #fff;\n      border-left: 1px solid #e4e7ed;\n      display: flex;\n      flex-direction: column;\n\n      .panel-title {\n        padding: 15px;\n        font-size: 16px;\n        font-weight: bold;\n        color: #303133;\n        border-bottom: 1px solid #e4e7ed;\n        background: #fafafa;\n      }\n\n      .properties-content {\n        flex: 1;\n        overflow-y: auto;\n\n        .no-selection {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          height: 200px;\n          color: #909399;\n\n          i {\n            font-size: 48px;\n            margin-bottom: 16px;\n            color: #c0c4cc;\n          }\n\n          p {\n            font-size: 14px;\n            margin: 0;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 全局样式\n:deep(.el-tabs--border-card) {\n  border: none;\n  box-shadow: none;\n\n  .el-tabs__header {\n    background: #fafafa;\n    border-bottom: 1px solid #e4e7ed;\n    margin: 0;\n  }\n\n  .el-tabs__content {\n    padding: 15px;\n  }\n}\n\n:deep(.el-form-item) {\n  margin-bottom: 15px;\n}\n\n:deep(.el-input), :deep(.el-select), :deep(.el-date-editor) {\n  width: 100%;\n}\n</style>\n"]}]}