{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\npi\\formConfig\\index.vue?vue&type=template&id=358f0d4e&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\npi\\formConfig\\index.vue", "mtime": 1752411081456}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1752199741460}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}