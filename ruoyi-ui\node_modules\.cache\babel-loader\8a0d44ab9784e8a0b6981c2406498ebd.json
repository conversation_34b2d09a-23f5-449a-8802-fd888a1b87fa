{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\finished\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\finished\\detail\\index.vue", "mtime": 1752407394746}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_finished", "require", "_definition", "_viewer", "_interopRequireDefault", "_FlowHistory", "name", "components", "BpmnViewer", "FlowHistory", "props", "data", "flowData", "activeName", "queryParams", "deptId", "undefined", "loading", "flowRecordList", "taskForm", "multiple", "comment", "procInsId", "instanceId", "deployId", "taskId", "procDefId", "vars", "formKey", "Date", "now", "created", "$route", "query", "processVariables", "getFlowRecordList", "methods", "handleClick", "tab", "_this", "flowXmlAndNode", "then", "res", "_this2", "that", "params", "flowRecord", "flowList", "catch", "goBack", "_this3", "getProcessVariables", "$nextTick", "$refs", "vFormRef", "set<PERSON><PERSON><PERSON><PERSON>", "formJson", "setFormData", "disableForm", "obj", "path", "t", "$tab", "closeOpenPage"], "sources": ["src/views/flowable/task/finished/detail/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\" >\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">已办任务</span>\r\n        <el-button style=\"float: right;\" size=\"mini\" type=\"danger\" @click=\"goBack\">关闭</el-button>\r\n      </div>\r\n      <el-tabs  tab-position=\"top\" v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <!--表单信息-->\r\n        <el-tab-pane label=\"表单信息\" name=\"1\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <v-form-render ref=\"vFormRef\" :key=\"formKey\"/>\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <!--流程流转记录-->\r\n        <el-tab-pane label=\"流转记录\" name=\"2\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <flow-history :flow-record-list=\"flowRecordList\" />\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <el-tab-pane label=\"流程图\" name=\"3\">\r\n          <Bpmn-viewer :flowData=\"flowData\" :procInsId=\"taskForm.procInsId\"/>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {flowRecord} from \"@/api/flowable/finished\";\r\nimport {getProcessVariables, flowXmlAndNode} from \"@/api/flowable/definition\";\r\nimport BpmnViewer from '@/components/Process/viewer';\r\nimport FlowHistory from '@/components/FlowHistory';\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Record\",\r\n  components: {\r\n    BpmnViewer,\r\n    FlowHistory,\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      // 模型xml数据\r\n      flowData: {},\r\n      activeName: '1',\r\n      // 查询参数\r\n      queryParams: {\r\n        deptId: undefined\r\n      },\r\n      // 遮罩层\r\n      loading: true,\r\n      flowRecordList: [], // 流程流转数据\r\n      taskForm:{\r\n        multiple: false,\r\n        comment:\"\", // 意见内容\r\n        procInsId: \"\", // 流程实例编号\r\n        instanceId: \"\", // 流程实例编号\r\n        deployId: \"\",  // 流程定义编号\r\n        taskId: \"\" ,// 流程任务编号\r\n        procDefId: \"\",  // 流程编号\r\n        vars: \"\",\r\n      },\r\n      formKey: Date.now() // VForm组件的唯一key\r\n    };\r\n  },\r\n  created() {\r\n    this.taskForm.deployId = this.$route.query && this.$route.query.deployId;\r\n    this.taskForm.taskId  = this.$route.query && this.$route.query.taskId;\r\n    this.taskForm.procInsId = this.$route.query && this.$route.query.procInsId;\r\n    // 流程任务重获取变量表单\r\n    if (this.taskForm.taskId){\r\n      this.processVariables( this.taskForm.taskId)\r\n    }\r\n    this.getFlowRecordList( this.taskForm.procInsId, this.taskForm.deployId);\r\n  },\r\n  methods: {\r\n    handleClick(tab) {\r\n      if (tab.name === '3') {\r\n        flowXmlAndNode({procInsId: this.taskForm.procInsId, deployId: this.taskForm.deployId}).then(res => {\r\n          this.flowData = res.data;\r\n        })\r\n      }\r\n    },\r\n    /** 流程流转记录 */\r\n    getFlowRecordList(procInsId, deployId) {\r\n      const that = this\r\n      const params = {procInsId: procInsId, deployId: deployId}\r\n      flowRecord(params).then(res => {\r\n        that.flowRecordList = res.data.flowList;\r\n      }).catch(() => {\r\n        this.goBack();\r\n      })\r\n    },\r\n    /** 获取流程变量内容 */\r\n    processVariables(taskId) {\r\n      if (taskId) {\r\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\r\n        getProcessVariables(taskId).then(res => {\r\n          // 更新formKey以强制重新渲染VForm组件，避免key冲突\r\n          this.formKey = Date.now();\r\n\r\n          this.$nextTick(() => {\r\n            // 回显表单\r\n            this.$refs.vFormRef.setFormJson(res.data.formJson);\r\n            this.$nextTick(() => {\r\n              // 加载表单填写的数据\r\n              this.$refs.vFormRef.setFormData(res.data);\r\n              this.$nextTick(() => {\r\n                // 表单禁用\r\n                this.$refs.vFormRef.disableForm();\r\n              })\r\n            })\r\n          })\r\n        });\r\n      }\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      const obj = { path: \"/task/finished\", query: { t: Date.now()} };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.test-form {\r\n  margin: 15px auto;\r\n  width: 800px;\r\n  padding: 15px;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n.clearfix:after {\r\n  clear: both\r\n}\r\n\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.my-label {\r\n  background: #E1F3D8;\r\n}\r\n\r\n.current-form-card {\r\n  border: 2px solid #409EFF;\r\n}\r\n\r\n.current-form-header {\r\n  color: #409EFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.current-form-header i {\r\n  margin-right: 8px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AA8BA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,YAAA,GAAAD,sBAAA,CAAAH,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,eAAA;IACAC,WAAA,EAAAA;EACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,QAAA;MACAC,UAAA;MACA;MACAC,WAAA;QACAC,MAAA,EAAAC;MACA;MACA;MACAC,OAAA;MACAC,cAAA;MAAA;MACAC,QAAA;QACAC,QAAA;QACAC,OAAA;QAAA;QACAC,SAAA;QAAA;QACAC,UAAA;QAAA;QACAC,QAAA;QAAA;QACAC,MAAA;QAAA;QACAC,SAAA;QAAA;QACAC,IAAA;MACA;MACAC,OAAA,EAAAC,IAAA,CAAAC,GAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAZ,QAAA,CAAAK,QAAA,QAAAQ,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAT,QAAA;IACA,KAAAL,QAAA,CAAAM,MAAA,QAAAO,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAR,MAAA;IACA,KAAAN,QAAA,CAAAG,SAAA,QAAAU,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAX,SAAA;IACA;IACA,SAAAH,QAAA,CAAAM,MAAA;MACA,KAAAS,gBAAA,MAAAf,QAAA,CAAAM,MAAA;IACA;IACA,KAAAU,iBAAA,MAAAhB,QAAA,CAAAG,SAAA,OAAAH,QAAA,CAAAK,QAAA;EACA;EACAY,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,GAAA,CAAAhC,IAAA;QACA,IAAAkC,0BAAA;UAAAlB,SAAA,OAAAH,QAAA,CAAAG,SAAA;UAAAE,QAAA,OAAAL,QAAA,CAAAK;QAAA,GAAAiB,IAAA,WAAAC,GAAA;UACAH,KAAA,CAAA3B,QAAA,GAAA8B,GAAA,CAAA/B,IAAA;QACA;MACA;IACA;IACA,aACAwB,iBAAA,WAAAA,kBAAAb,SAAA,EAAAE,QAAA;MAAA,IAAAmB,MAAA;MACA,IAAAC,IAAA;MACA,IAAAC,MAAA;QAAAvB,SAAA,EAAAA,SAAA;QAAAE,QAAA,EAAAA;MAAA;MACA,IAAAsB,oBAAA,EAAAD,MAAA,EAAAJ,IAAA,WAAAC,GAAA;QACAE,IAAA,CAAA1B,cAAA,GAAAwB,GAAA,CAAA/B,IAAA,CAAAoC,QAAA;MACA,GAAAC,KAAA;QACAL,MAAA,CAAAM,MAAA;MACA;IACA;IACA,eACAf,gBAAA,WAAAA,iBAAAT,MAAA;MAAA,IAAAyB,MAAA;MACA,IAAAzB,MAAA;QACA;QACA,IAAA0B,+BAAA,EAAA1B,MAAA,EAAAgB,IAAA,WAAAC,GAAA;UACA;UACAQ,MAAA,CAAAtB,OAAA,GAAAC,IAAA,CAAAC,GAAA;UAEAoB,MAAA,CAAAE,SAAA;YACA;YACAF,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAC,WAAA,CAAAb,GAAA,CAAA/B,IAAA,CAAA6C,QAAA;YACAN,MAAA,CAAAE,SAAA;cACA;cACAF,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAG,WAAA,CAAAf,GAAA,CAAA/B,IAAA;cACAuC,MAAA,CAAAE,SAAA;gBACA;gBACAF,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAI,WAAA;cACA;YACA;UACA;QACA;MACA;IACA;IACA,WACAT,MAAA,WAAAA,OAAA;MACA;MACA,IAAAU,GAAA;QAAAC,IAAA;QAAA3B,KAAA;UAAA4B,CAAA,EAAAhC,IAAA,CAAAC,GAAA;QAAA;MAAA;MACA,KAAAgC,IAAA,CAAAC,aAAA,CAAAJ,GAAA;IACA;EACA;AACA", "ignoreList": []}]}