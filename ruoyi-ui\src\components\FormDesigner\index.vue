<template>
  <div class="form-designer">
    <div class="designer-header">
      <el-form :model="formConfig" :inline="true" size="small">
        <el-form-item label="表单名称">
          <el-input v-model="formConfig.formName" placeholder="请输入表单名称" style="width: 200px" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="formConfig.remark" placeholder="请输入备注" style="width: 200px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSave">保存表单</el-button>
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="success" @click="handlePreview">预览</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="designer-body">
      <!-- 左侧组件面板 -->
      <div class="components-panel">
        <div class="panel-title">组件库</div>
        <div class="component-groups">
          <!-- 基础组件 -->
          <div class="component-group">
            <div class="group-title">基础组件</div>
            <div class="component-list">
              <div 
                v-for="component in basicComponents" 
                :key="component.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart($event, component)"
              >
                <i :class="component.icon"></i>
                <span>{{ component.label }}</span>
              </div>
            </div>
          </div>

          <!-- 高级组件 -->
          <div class="component-group">
            <div class="group-title">高级组件</div>
            <div class="component-list">
              <div 
                v-for="component in advancedComponents" 
                :key="component.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart($event, component)"
              >
                <i :class="component.icon"></i>
                <span>{{ component.label }}</span>
              </div>
            </div>
          </div>

          <!-- 布局组件 -->
          <div class="component-group">
            <div class="group-title">布局组件</div>
            <div class="component-list">
              <div 
                v-for="component in layoutComponents" 
                :key="component.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart($event, component)"
              >
                <i :class="component.icon"></i>
                <span>{{ component.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间设计区域 -->
      <div class="design-panel">
        <div class="panel-title">表单设计区域</div>
        <div 
          class="design-canvas"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @click="clearSelection"
        >
          <div v-if="formItems.length === 0" class="empty-canvas">
            <i class="el-icon-plus"></i>
            <p>从左侧拖拽组件到此处开始设计表单</p>
          </div>
          
          <draggable 
            v-model="formItems" 
            group="form-items"
            :animation="200"
            ghost-class="ghost"
            chosen-class="chosen"
            @end="handleSortEnd"
          >
            <form-item-wrapper
              v-for="(item, index) in formItems"
              :key="item.id"
              :item="item"
              :index="index"
              :selected="selectedItem && selectedItem.id === item.id"
              @select="handleSelectItem"
              @delete="handleDeleteItem"
              @clone="handleCloneItem"
            />
          </draggable>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel">
        <div class="panel-title">属性配置</div>
        <div class="properties-content">
          <form-properties
            v-if="selectedItem"
            :item="selectedItem"
            @update="handleUpdateProperties"
          />
          <div v-else class="no-selection">
            <i class="el-icon-info"></i>
            <p>请选择一个组件进行配置</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog title="表单预览" :visible.sync="previewVisible" width="80%" append-to-body>
      <form-preview :form-items="formItems" />
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import FormItemWrapper from './components/FormItemWrapper.vue'
import FormProperties from './components/FormProperties.vue'
import FormPreview from './components/FormPreview.vue'
import { generateId } from './utils/index.js'
import { basicComponents, advancedComponents, layoutComponents } from './config/components.js'

export default {
  name: 'FormDesigner',
  components: {
    draggable,
    FormItemWrapper,
    FormProperties,
    FormPreview
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formConfig: {
        formName: '',
        remark: ''
      },
      formItems: [],
      selectedItem: null,
      previewVisible: false,
      basicComponents,
      advancedComponents,
      layoutComponents
    }
  },
  created() {
    this.initFormData()
  },
  methods: {
    initFormData() {
      if (this.formData.formId) {
        this.formConfig.formName = this.formData.formName || ''
        this.formConfig.remark = this.formData.remark || ''
        
        // 解析表单内容
        if (this.formData.formContent) {
          try {
            const content = JSON.parse(this.formData.formContent)
            this.formItems = content.formItems || []
          } catch (e) {
            console.warn('表单内容解析失败:', e)
          }
        }
      }
    },

    handleDragStart(event, component) {
      event.dataTransfer.setData('component', JSON.stringify(component))
    },

    handleDragOver(event) {
      event.preventDefault()
    },

    handleDrop(event) {
      event.preventDefault()
      const componentData = event.dataTransfer.getData('component')
      if (componentData) {
        const component = JSON.parse(componentData)
        this.addFormItem(component)
      }
    },

    addFormItem(component) {
      const newItem = {
        id: generateId(),
        type: component.type,
        label: component.label,
        icon: component.icon,
        ...component.defaultProps
      }
      this.formItems.push(newItem)
      this.selectedItem = newItem
    },

    handleSelectItem(item) {
      this.selectedItem = item
    },

    handleDeleteItem(index) {
      this.formItems.splice(index, 1)
      if (this.selectedItem && this.selectedItem.id === this.formItems[index]?.id) {
        this.selectedItem = null
      }
    },

    handleCloneItem(item) {
      const clonedItem = {
        ...JSON.parse(JSON.stringify(item)),
        id: generateId()
      }
      const index = this.formItems.findIndex(i => i.id === item.id)
      this.formItems.splice(index + 1, 0, clonedItem)
    },

    handleUpdateProperties(properties) {
      if (this.selectedItem) {
        Object.assign(this.selectedItem, properties)
      }
    },

    handleSortEnd() {
      // 拖拽排序完成后的处理
    },

    clearSelection() {
      this.selectedItem = null
    },

    handleSave() {
      if (!this.formConfig.formName) {
        this.$message.error('请输入表单名称')
        return
      }

      const formData = {
        ...this.formData,
        formName: this.formConfig.formName,
        remark: this.formConfig.remark,
        formContent: JSON.stringify({
          formItems: this.formItems,
          config: this.formConfig
        })
      }

      this.$emit('save', formData)
    },

    handleCancel() {
      this.$emit('cancel')
    },

    handlePreview() {
      this.previewVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.form-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .designer-header {
    background: #fff;
    padding: 10px 20px;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .designer-body {
    flex: 1;
    display: flex;
    height: calc(100vh - 80px);
    overflow: hidden;

    .components-panel {
      width: 260px;
      background: #fff;
      border-right: 1px solid #e4e7ed;
      overflow-y: auto;

      .panel-title {
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        background: #fafafa;
      }

      .component-groups {
        .component-group {
          .group-title {
            padding: 10px 15px;
            font-size: 14px;
            font-weight: 600;
            color: #606266;
            background: #f8f9fa;
            border-bottom: 1px solid #e4e7ed;
          }

          .component-list {
            padding: 10px;

            .component-item {
              display: flex;
              align-items: center;
              padding: 8px 12px;
              margin-bottom: 8px;
              background: #fff;
              border: 1px solid #e4e7ed;
              border-radius: 4px;
              cursor: grab;
              transition: all 0.3s;

              &:hover {
                border-color: #409EFF;
                background: #f0f9ff;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
              }

              &:active {
                cursor: grabbing;
              }

              i {
                margin-right: 8px;
                font-size: 16px;
                color: #409EFF;
              }

              span {
                font-size: 13px;
                color: #606266;
              }
            }
          }
        }
      }
    }

    .design-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: #fff;
      margin: 0 1px;

      .panel-title {
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        background: #fafafa;
      }

      .design-canvas {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        min-height: 400px;
        position: relative;

        .empty-canvas {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 300px;
          border: 2px dashed #c0c4cc;
          border-radius: 8px;
          color: #909399;
          background: #fafbfc;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #c0c4cc;
          }

          p {
            font-size: 14px;
            margin: 0;
          }
        }

        .ghost {
          opacity: 0.5;
          background: #409EFF;
        }

        .chosen {
          border: 2px solid #409EFF !important;
        }
      }
    }

    .properties-panel {
      width: 320px;
      background: #fff;
      border-left: 1px solid #e4e7ed;
      display: flex;
      flex-direction: column;

      .panel-title {
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        background: #fafafa;
      }

      .properties-content {
        flex: 1;
        overflow-y: auto;

        .no-selection {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 200px;
          color: #909399;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #c0c4cc;
          }

          p {
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }
  }
}
</style>
