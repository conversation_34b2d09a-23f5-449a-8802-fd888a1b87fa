{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeVFormManager\\index.vue?vue&type=style&index=0&id=20aafb88&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeVFormManager\\index.vue", "mtime": 1752411008662}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoubm9kZS12Zm9ybS1tYW5hZ2VyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLm1hbmFnZXItaGVhZGVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjVGN0ZBOwogIHBhZGRpbmc6IDE2cHggMjBweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI0VCRUVGNTsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwoKICBoMyB7CiAgICBtYXJnaW46IDA7CiAgICBjb2xvcjogIzMwMzEzMzsKICAgIGZvbnQtc2l6ZTogMTZweDsKCiAgICBpIHsKICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgICAgIGNvbG9yOiAjNDA5RUZGOwogICAgfQogIH0KfQoKLm5vZGUtZm9ybXMtbGlzdCB7CiAgcGFkZGluZzogMjBweDsKfQoKLm5vZGUtdGl0bGUgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICB3aWR0aDogMTAwJTsKCiAgaSB7CiAgICBtYXJnaW4tcmlnaHQ6IDhweDsKICAgIGNvbG9yOiAjNDA5RUZGOwogIH0KCiAgLm5vZGUtbmFtZSB7CiAgICBmb250LXdlaWdodDogNjAwOwogICAgbWFyZ2luLXJpZ2h0OiAxMnB4OwogIH0KCiAgLmZvcm0tc3RhdHVzIHsKICAgIG1hcmdpbi1sZWZ0OiBhdXRvOwogICAgY29sb3I6ICM5MDkzOTk7CiAgICBmb250LXNpemU6IDEycHg7CiAgfQp9Cgoubm9kZS1mb3JtLWNvbnRlbnQgewogIHBhZGRpbmc6IDE2cHggMDsKfQoKLm5vZGUtY29uZmlnIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4OwogIHBhZGRpbmc6IDE2cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI0ZBRkFGQTsKICBib3JkZXItcmFkaXVzOiA0cHg7Cn0KCi5mb3JtLXByZXZpZXcgewogIG1hcmdpbi10b3A6IDE2cHg7CiAgCiAgaDUgewogICAgbWFyZ2luOiAwIDAgMTJweCAwOwogICAgY29sb3I6ICM2MDYyNjY7CiAgfQp9CgoucHJldmlldy1jb250YWluZXIgewogIGJvcmRlcjogMXB4IHNvbGlkICNFNEU3RUQ7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIHBhZGRpbmc6IDE2cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7Cn0KCi5uby1mb3JtIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogNDBweCAyMHB4OwogIGNvbG9yOiAjOTA5Mzk5OwogIGJvcmRlcjogMXB4IGRhc2hlZCAjRTRFN0VEOwogIGJvcmRlci1yYWRpdXM6IDRweDsKCiAgaSB7CiAgICBmb250LXNpemU6IDQ4cHg7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgZGlzcGxheTogYmxvY2s7CiAgfQoKICBwIHsKICAgIG1hcmdpbjogMDsKICAgIGZvbnQtc2l6ZTogMTRweDsKICB9Cn0KCi5tYW5hZ2VyLWZvb3RlciB7CiAgcGFkZGluZzogMTZweCAyMHB4OwogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjRUJFRUY1OwogIGJhY2tncm91bmQtY29sb3I6ICNGQUZBRkE7CiAgdGV4dC1hbGlnbjogcmlnaHQ7CgogIC5lbC1idXR0b24gewogICAgbWFyZ2luLWxlZnQ6IDhweDsKICB9Cn0KCi5lbXB0eS1zdGF0ZSB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDYwcHggMjBweDsKICBjb2xvcjogIzkwOTM5OTsKCiAgaSB7CiAgICBmb250LXNpemU6IDY0cHg7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgZGlzcGxheTogYmxvY2s7CiAgfQoKICBwIHsKICAgIG1hcmdpbjogMDsKICAgIGZvbnQtc2l6ZTogMTRweDsKICB9Cn0KCi52Zm9ybS1kZXNpZ25lci1jb250YWluZXIgewogIGhlaWdodDogNzB2aDsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgouZm9ybS1wcmV2aWV3LWRpYWxvZyB7CiAgbWluLWhlaWdodDogMzAwcHg7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXI6IDFweCBzb2xpZCAjRTRFN0VEOwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAicA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/NodeVFormManager", "sourcesContent": ["<template>\n  <div class=\"node-vform-manager\">\n    <div class=\"manager-header\">\n      <h3>\n        <i class=\"el-icon-s-order\"></i>\n        NPI流程节点表单配置\n      </h3>\n      <el-button type=\"primary\" @click=\"addNodeForm\">\n        <i class=\"el-icon-plus\"></i>\n        添加节点表单\n      </el-button>\n    </div>\n\n    <div class=\"node-forms-list\">\n      <el-collapse v-model=\"activeNodes\" accordion>\n        <el-collapse-item \n          v-for=\"(nodeForm, index) in nodeForms\" \n          :key=\"nodeForm.id\"\n          :name=\"nodeForm.id\"\n        >\n          <template slot=\"title\">\n            <div class=\"node-title\">\n              <i class=\"el-icon-document\"></i>\n              <span class=\"node-name\">{{ nodeForm.nodeName }}</span>\n              <el-tag :type=\"getNodeTypeTag(nodeForm.nodeType)\" size=\"mini\">\n                {{ getNodeTypeText(nodeForm.nodeType) }}\n              </el-tag>\n              <span class=\"form-status\">{{ nodeForm.formJson ? '已配置' : '未配置' }}</span>\n            </div>\n          </template>\n\n          <div class=\"node-form-content\">\n            <div class=\"node-config\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"节点名称\">\n                    <el-input v-model=\"nodeForm.nodeName\" placeholder=\"请输入节点名称\" />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"节点类型\">\n                    <el-select v-model=\"nodeForm.nodeType\" style=\"width: 100%\">\n                      <el-option label=\"NPI申请\" value=\"npi_apply\" />\n                      <el-option label=\"技术评审\" value=\"tech_review\" />\n                      <el-option label=\"工艺评审\" value=\"process_review\" />\n                      <el-option label=\"质量评审\" value=\"quality_review\" />\n                      <el-option label=\"成本评审\" value=\"cost_review\" />\n                      <el-option label=\"最终审批\" value=\"final_approval\" />\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"节点标识\">\n                    <el-input v-model=\"nodeForm.nodeKey\" placeholder=\"流程图中的节点ID\" />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-form-item label=\"操作\">\n                    <el-button type=\"primary\" size=\"small\" @click=\"designForm(nodeForm)\">\n                      设计表单\n                    </el-button>\n                    <el-button type=\"danger\" size=\"small\" @click=\"removeNodeForm(index)\">\n                      删除\n                    </el-button>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </div>\n\n            <!-- 表单预览 -->\n            <div v-if=\"nodeForm.formJson\" class=\"form-preview\">\n              <h5>表单预览</h5>\n              <div class=\"preview-container\">\n                <v-form-render \n                  :ref=\"`preview_${nodeForm.id}`\"\n                  :key=\"`preview_${nodeForm.id}_${nodeForm.previewKey || 0}`\"\n                />\n              </div>\n            </div>\n            <div v-else class=\"no-form\">\n              <i class=\"el-icon-document-add\"></i>\n              <p>暂未配置表单，点击\"设计表单\"开始配置</p>\n            </div>\n          </div>\n        </el-collapse-item>\n      </el-collapse>\n\n      <div v-if=\"nodeForms.length === 0\" class=\"empty-state\">\n        <i class=\"el-icon-document-add\"></i>\n        <p>暂无节点表单，点击\"添加节点表单\"开始创建</p>\n      </div>\n    </div>\n\n    <div class=\"manager-footer\">\n      <el-button @click=\"previewAllForms\">预览所有表单</el-button>\n      <el-button type=\"primary\" @click=\"saveConfig\">保存配置</el-button>\n      <el-button @click=\"exportConfig\">导出配置</el-button>\n      <el-button @click=\"importConfig\">导入配置</el-button>\n    </div>\n\n    <!-- VForm设计器对话框 -->\n    <el-dialog \n      :title=\"`设计表单 - ${currentEditNode.nodeName}`\"\n      :visible.sync=\"showDesigner\" \n      width=\"90%\" \n      top=\"5vh\"\n      :close-on-click-modal=\"false\"\n    >\n      <div class=\"vform-designer-container\">\n        <v-form-designer \n          ref=\"vfDesigner\" \n          :designer-config=\"designerConfig\"\n          @form-json-change=\"onFormJsonChange\"\n        >\n          <template #customToolButtons>\n            <el-button type=\"primary\" @click=\"saveFormDesign\">保存表单</el-button>\n            <el-button @click=\"previewForm\">预览表单</el-button>\n          </template>\n        </v-form-designer>\n      </div>\n    </el-dialog>\n\n    <!-- 表单预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"showPreview\" width=\"60%\">\n      <div class=\"form-preview-dialog\">\n        <v-form-render ref=\"previewFormRef\" />\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"showPreview = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 所有表单预览 -->\n    <el-dialog title=\"所有表单预览\" :visible.sync=\"showAllPreview\" width=\"80%\">\n      <el-tabs v-model=\"previewActiveTab\" type=\"card\">\n        <el-tab-pane \n          v-for=\"nodeForm in nodeForms.filter(n => n.formJson)\" \n          :key=\"nodeForm.id\"\n          :label=\"nodeForm.nodeName\"\n          :name=\"nodeForm.id\"\n        >\n          <v-form-render \n            :ref=\"`allPreview_${nodeForm.id}`\"\n            :key=\"`allPreview_${nodeForm.id}_${nodeForm.previewKey || 0}`\"\n          />\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n\n    <!-- 导入文件 -->\n    <input \n      ref=\"fileInput\" \n      type=\"file\" \n      accept=\".json\" \n      style=\"display: none\" \n      @change=\"handleFileImport\"\n    />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'NodeVFormManager',\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    },\n    processKey: {\n      type: String,\n      default: 'npi_process'\n    }\n  },\n  data() {\n    return {\n      nodeForms: [],\n      activeNodes: '',\n      showDesigner: false,\n      showPreview: false,\n      showAllPreview: false,\n      previewActiveTab: '',\n      currentEditNode: {},\n      designerConfig: {\n        languageMenu: false,\n        externalLink: false,\n        formTemplates: true,\n        eventCollapse: false,\n        widgetCollapse: false,\n        clearDesignerButton: true,\n        previewFormButton: false,\n        importJsonButton: true,\n        exportJsonButton: true,\n        exportCodeButton: false,\n        generateSFCButton: false\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        this.nodeForms = newVal || [];\n        this.loadFormPreviews();\n      },\n      immediate: true,\n      deep: true\n    },\n    nodeForms: {\n      handler(newVal) {\n        this.$emit('input', newVal);\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 添加节点表单 */\n    addNodeForm() {\n      const newNodeForm = {\n        id: `node_${Date.now()}`,\n        nodeName: `NPI节点${this.nodeForms.length + 1}`,\n        nodeType: 'npi_apply',\n        nodeKey: '',\n        formJson: null,\n        previewKey: 0\n      };\n      \n      this.nodeForms.push(newNodeForm);\n      this.activeNodes = newNodeForm.id;\n    },\n\n    /** 删除节点表单 */\n    removeNodeForm(index) {\n      this.$confirm('确定要删除这个节点表单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.nodeForms.splice(index, 1);\n        this.$message.success('删除成功');\n      }).catch(() => {});\n    },\n\n    /** 设计表单 */\n    designForm(nodeForm) {\n      this.currentEditNode = nodeForm;\n      this.showDesigner = true;\n      \n      this.$nextTick(() => {\n        if (nodeForm.formJson) {\n          this.$refs.vfDesigner.setFormJson(nodeForm.formJson);\n        } else {\n          // 设置默认的空表单\n          this.$refs.vfDesigner.setFormJson({\n            widgetList: [],\n            formConfig: {\n              modelName: 'formData',\n              refName: 'vForm',\n              rulesName: 'rules',\n              labelWidth: 80,\n              labelPosition: 'left',\n              size: '',\n              labelAlign: 'label-left-align',\n              cssCode: '',\n              customClass: '',\n              functions: '',\n              layoutType: 'PC'\n            }\n          });\n        }\n      });\n    },\n\n    /** 表单JSON变化 */\n    onFormJsonChange(formJson) {\n      // 实时保存表单设计\n      if (this.currentEditNode.id) {\n        const nodeForm = this.nodeForms.find(n => n.id === this.currentEditNode.id);\n        if (nodeForm) {\n          nodeForm.formJson = formJson;\n          nodeForm.previewKey = Date.now();\n        }\n      }\n    },\n\n    /** 保存表单设计 */\n    saveFormDesign() {\n      const formJson = this.$refs.vfDesigner.getFormJson();\n      const nodeForm = this.nodeForms.find(n => n.id === this.currentEditNode.id);\n      if (nodeForm) {\n        nodeForm.formJson = formJson;\n        nodeForm.previewKey = Date.now();\n        this.$message.success('表单保存成功');\n        this.loadFormPreviews();\n      }\n    },\n\n    /** 预览表单 */\n    previewForm() {\n      const formJson = this.$refs.vfDesigner.getFormJson();\n      this.showPreview = true;\n      this.$nextTick(() => {\n        this.$refs.previewFormRef.setFormJson(formJson);\n      });\n    },\n\n    /** 预览所有表单 */\n    previewAllForms() {\n      const formsWithJson = this.nodeForms.filter(n => n.formJson);\n      if (formsWithJson.length === 0) {\n        this.$message.warning('暂无已配置的表单');\n        return;\n      }\n      \n      this.previewActiveTab = formsWithJson[0].id;\n      this.showAllPreview = true;\n      \n      this.$nextTick(() => {\n        formsWithJson.forEach(nodeForm => {\n          const ref = this.$refs[`allPreview_${nodeForm.id}`];\n          if (ref && ref[0]) {\n            ref[0].setFormJson(nodeForm.formJson);\n          }\n        });\n      });\n    },\n\n    /** 加载表单预览 */\n    loadFormPreviews() {\n      this.$nextTick(() => {\n        this.nodeForms.forEach(nodeForm => {\n          if (nodeForm.formJson) {\n            const ref = this.$refs[`preview_${nodeForm.id}`];\n            if (ref && ref[0]) {\n              ref[0].setFormJson(nodeForm.formJson);\n              ref[0].disableForm();\n            }\n          }\n        });\n      });\n    },\n\n    /** 获取节点类型标签 */\n    getNodeTypeTag(type) {\n      const tagMap = {\n        npi_apply: 'primary',\n        tech_review: 'success',\n        process_review: 'warning',\n        quality_review: 'danger',\n        cost_review: 'info',\n        final_approval: 'primary'\n      };\n      return tagMap[type] || 'primary';\n    },\n\n    /** 获取节点类型文本 */\n    getNodeTypeText(type) {\n      const textMap = {\n        npi_apply: 'NPI申请',\n        tech_review: '技术评审',\n        process_review: '工艺评审',\n        quality_review: '质量评审',\n        cost_review: '成本评审',\n        final_approval: '最终审批'\n      };\n      return textMap[type] || type;\n    },\n\n    /** 保存配置 */\n    saveConfig() {\n      const config = {\n        processKey: this.processKey,\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      // 保存到本地存储\n      localStorage.setItem(`node_vform_config_${this.processKey}`, JSON.stringify(config));\n      \n      this.$message.success('配置保存成功');\n      this.$emit('save', config);\n    },\n\n    /** 导出配置 */\n    exportConfig() {\n      const config = {\n        processKey: this.processKey,\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      const blob = new Blob([JSON.stringify(config, null, 2)], { \n        type: 'application/json' \n      });\n      \n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `${this.processKey}_node_forms_${Date.now()}.json`;\n      a.click();\n      URL.revokeObjectURL(url);\n      \n      this.$message.success('导出成功');\n    },\n\n    /** 导入配置 */\n    importConfig() {\n      this.$refs.fileInput.click();\n    },\n\n    /** 处理文件导入 */\n    handleFileImport(event) {\n      const file = event.target.files[0];\n      if (!file) return;\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const config = JSON.parse(e.target.result);\n          if (config.nodeForms && Array.isArray(config.nodeForms)) {\n            this.nodeForms = config.nodeForms;\n            this.$message.success('导入成功');\n            this.loadFormPreviews();\n          } else {\n            this.$message.error('文件格式不正确');\n          }\n        } catch (error) {\n          this.$message.error('文件解析失败');\n        }\n      };\n      reader.readAsText(file);\n      \n      event.target.value = '';\n    },\n\n    /** 根据节点标识获取表单配置 */\n    getFormByNodeKey(nodeKey) {\n      return this.nodeForms.find(form => form.nodeKey === nodeKey);\n    },\n\n    /** 根据节点类型获取表单配置 */\n    getFormByNodeType(nodeType) {\n      return this.nodeForms.find(form => form.nodeType === nodeType);\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.node-vform-manager {\n  background-color: white;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.manager-header {\n  background-color: #F5F7FA;\n  padding: 16px 20px;\n  border-bottom: 1px solid #EBEEF5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h3 {\n    margin: 0;\n    color: #303133;\n    font-size: 16px;\n\n    i {\n      margin-right: 8px;\n      color: #409EFF;\n    }\n  }\n}\n\n.node-forms-list {\n  padding: 20px;\n}\n\n.node-title {\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  i {\n    margin-right: 8px;\n    color: #409EFF;\n  }\n\n  .node-name {\n    font-weight: 600;\n    margin-right: 12px;\n  }\n\n  .form-status {\n    margin-left: auto;\n    color: #909399;\n    font-size: 12px;\n  }\n}\n\n.node-form-content {\n  padding: 16px 0;\n}\n\n.node-config {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #FAFAFA;\n  border-radius: 4px;\n}\n\n.form-preview {\n  margin-top: 16px;\n  \n  h5 {\n    margin: 0 0 12px 0;\n    color: #606266;\n  }\n}\n\n.preview-container {\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n  padding: 16px;\n  background-color: white;\n}\n\n.no-form {\n  text-align: center;\n  padding: 40px 20px;\n  color: #909399;\n  border: 1px dashed #E4E7ED;\n  border-radius: 4px;\n\n  i {\n    font-size: 48px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.manager-footer {\n  padding: 16px 20px;\n  border-top: 1px solid #EBEEF5;\n  background-color: #FAFAFA;\n  text-align: right;\n\n  .el-button {\n    margin-left: 8px;\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n\n  i {\n    font-size: 64px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.vform-designer-container {\n  height: 70vh;\n  overflow: hidden;\n}\n\n.form-preview-dialog {\n  min-height: 300px;\n  padding: 20px;\n  border: 1px solid #E4E7ED;\n  border-radius: 4px;\n}\n</style>\n"]}]}