{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FieldRenderer\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FieldRenderer\\index.vue", "mtime": 1752412497993}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "value", "type", "String", "Number", "Boolean", "Array", "Object", "default", "field", "required", "disabled", "methods", "formatValue", "undefined", "isArray", "join", "_typeof2", "JSON", "stringify", "getFieldOption", "optionName", "options"], "sources": ["src/components/FieldRenderer/index.vue"], "sourcesContent": ["<template>\n  <div class=\"field-renderer\">\n    <!-- 文本输入 -->\n    <el-input\n      v-if=\"field.type === 'input'\"\n      :value=\"value\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      readonly\n    />\n\n    <!-- 多行文本 -->\n    <el-input\n      v-else-if=\"field.type === 'textarea'\"\n      :value=\"value\"\n      type=\"textarea\"\n      :rows=\"getFieldOption('rows') || 3\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      readonly\n    />\n\n    <!-- 数字输入 -->\n    <el-input-number\n      v-else-if=\"field.type === 'number'\"\n      :value=\"value\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      style=\"width: 100%\"\n      :controls=\"false\"\n    />\n\n    <!-- 选择器 -->\n    <el-select\n      v-else-if=\"field.type === 'select'\"\n      :value=\"value\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      style=\"width: 100%\"\n    >\n      <el-option\n        v-for=\"option in getFieldOption('optionItems') || []\"\n        :key=\"option.value\"\n        :label=\"option.label\"\n        :value=\"option.value\"\n      />\n    </el-select>\n\n    <!-- 日期选择 -->\n    <el-date-picker\n      v-else-if=\"field.type === 'date'\"\n      :value=\"value\"\n      type=\"date\"\n      :placeholder=\"getFieldOption('placeholder')\"\n      :disabled=\"disabled\"\n      style=\"width: 100%\"\n    />\n\n    <!-- 开关 -->\n    <el-switch\n      v-else-if=\"field.type === 'switch'\"\n      :value=\"value\"\n      :disabled=\"disabled\"\n    />\n\n    <!-- 单选框组 -->\n    <el-radio-group\n      v-else-if=\"field.type === 'radio'\"\n      :value=\"value\"\n      :disabled=\"disabled\"\n    >\n      <el-radio\n        v-for=\"option in getFieldOption('optionItems') || []\"\n        :key=\"option.value\"\n        :label=\"option.value\"\n      >\n        {{ option.label }}\n      </el-radio>\n    </el-radio-group>\n\n    <!-- 复选框组 -->\n    <el-checkbox-group\n      v-else-if=\"field.type === 'checkbox'\"\n      :value=\"value || []\"\n      :disabled=\"disabled\"\n    >\n      <el-checkbox\n        v-for=\"option in getFieldOption('optionItems') || []\"\n        :key=\"option.value\"\n        :label=\"option.value\"\n      >\n        {{ option.label }}\n      </el-checkbox>\n    </el-checkbox-group>\n    \n    <!-- 默认文本显示 -->\n    <span v-else class=\"default-text\">{{ formatValue(value) }}</span>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FieldRenderer',\n  props: {\n    value: {\n      type: [String, Number, Boolean, Array, Object],\n      default: null\n    },\n    field: {\n      type: Object,\n      required: true\n    },\n    disabled: {\n      type: Boolean,\n      default: true\n    }\n  },\n  methods: {\n    formatValue(value) {\n      if (value === null || value === undefined) {\n        return '';\n      }\n      if (Array.isArray(value)) {\n        return value.join(', ');\n      }\n      if (typeof value === 'object') {\n        return JSON.stringify(value);\n      }\n      if (typeof value === 'boolean') {\n        return value ? '是' : '否';\n      }\n      return String(value);\n    },\n\n    getFieldOption(optionName) {\n      return this.field.options && this.field.options[optionName] ? this.field.options[optionName] : null;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.field-renderer {\n  width: 100%;\n  \n  .default-text {\n    color: #606266;\n    font-size: 14px;\n    line-height: 32px;\n  }\n  \n  // 禁用状态下的样式调整\n  :deep(.el-input.is-disabled .el-input__inner) {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #606266;\n  }\n  \n  :deep(.el-textarea.is-disabled .el-textarea__inner) {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #606266;\n  }\n  \n  :deep(.el-select.is-disabled .el-input__inner) {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #606266;\n  }\n  \n  :deep(.el-date-editor.is-disabled .el-input__inner) {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #606266;\n  }\n  \n  :deep(.el-switch.is-disabled) {\n    opacity: 0.6;\n  }\n  \n  :deep(.el-radio.is-disabled) {\n    color: #c0c4cc;\n  }\n  \n  :deep(.el-checkbox.is-disabled) {\n    color: #c0c4cc;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAqGA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,OAAA,EAAAC,KAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAP,IAAA,EAAAK,MAAA;MACAG,QAAA;IACA;IACAC,QAAA;MACAT,IAAA,EAAAG,OAAA;MACAG,OAAA;IACA;EACA;EACAI,OAAA;IACAC,WAAA,WAAAA,YAAAZ,KAAA;MACA,IAAAA,KAAA,aAAAA,KAAA,KAAAa,SAAA;QACA;MACA;MACA,IAAAR,KAAA,CAAAS,OAAA,CAAAd,KAAA;QACA,OAAAA,KAAA,CAAAe,IAAA;MACA;MACA,QAAAC,QAAA,CAAAT,OAAA,EAAAP,KAAA;QACA,OAAAiB,IAAA,CAAAC,SAAA,CAAAlB,KAAA;MACA;MACA,WAAAA,KAAA;QACA,OAAAA,KAAA;MACA;MACA,OAAAE,MAAA,CAAAF,KAAA;IACA;IAEAmB,cAAA,WAAAA,eAAAC,UAAA;MACA,YAAAZ,KAAA,CAAAa,OAAA,SAAAb,KAAA,CAAAa,OAAA,CAAAD,UAAA,SAAAZ,KAAA,CAAAa,OAAA,CAAAD,UAAA;IACA;EACA;AACA", "ignoreList": []}]}