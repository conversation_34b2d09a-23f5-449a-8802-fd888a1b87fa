{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\myProcess\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\myProcess\\detail\\index.vue", "mtime": 1752406578957}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/task/myProcess/detail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\" >\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">已发任务</span>\r\n        <el-button style=\"float: right;\" size=\"mini\" type=\"danger\" @click=\"goBack\">关闭</el-button>\r\n      </div>\r\n      <el-tabs  tab-position=\"top\" v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <!--表单信息-->\r\n        <el-tab-pane label=\"表单信息\" name=\"1\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <flow-history :flow-record-list=\"flowRecordList\" />\r\n\r\n            <!-- 当前表单 -->\r\n            <el-card class=\"current-form-card\" shadow=\"hover\">\r\n              <div slot=\"header\" class=\"current-form-header\">\r\n                <i class=\"el-icon-edit-outline\"></i>\r\n                <span>表单信息</span>\r\n              </div>\r\n              <v-form-render ref=\"vFormRef\" :key=\"formKey\"/>\r\n            </el-card>\r\n         </el-col>\r\n        </el-tab-pane>\r\n        <!--流程流转记录-->\r\n        <el-tab-pane label=\"流转记录\" name=\"2\">\r\n          <el-col :span=\"16\" :offset=\"4\" >\r\n            <div class=\"block\">\r\n              <el-timeline>\r\n                <el-timeline-item\r\n                  v-for=\"(item,index ) in flowRecordList\"\r\n                  :key=\"index\"\r\n                  :icon=\"setIcon(item.finishTime)\"\r\n                  :color=\"setColor(item.finishTime)\"\r\n                >\r\n                  <p style=\"font-weight: 700\">{{item.taskName}}</p>\r\n                  <el-card :body-style=\"{ padding: '10px' }\">\r\n                    <el-descriptions class=\"margin-top\" :column=\"1\" size=\"small\" border>\r\n                      <el-descriptions-item v-if=\"item.assigneeName\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-user\"></i>办理人</template>\r\n                        {{item.assigneeName}}\r\n                        <el-tag type=\"info\" size=\"mini\">{{item.deptName}}</el-tag>\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.candidate\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-user\"></i>候选办理</template>\r\n                        {{item.candidate}}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-date\"></i>接收时间</template>\r\n                        {{item.createTime}}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.finishTime\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-date\"></i>处理时间</template>\r\n                        {{item.finishTime}}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.duration\"  label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-time\"></i>耗时</template>\r\n                        {{item.duration}}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.comment\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-tickets\"></i>处理意见</template>\r\n                        {{item.comment.comment}}\r\n                      </el-descriptions-item>\r\n                    </el-descriptions>\r\n                  </el-card>\r\n                </el-timeline-item>\r\n              </el-timeline>\r\n            </div>\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <!--流程图-->\r\n        <el-tab-pane label=\"流程图\" name=\"3\">\r\n          <bpmn-viewer :flowData=\"flowData\" :procInsId=\"taskForm.procInsId\"/>\r\n        </el-tab-pane>\r\n    </el-tabs>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {flowRecord} from \"@/api/flowable/finished\";\r\nimport {getProcessVariables, flowXmlAndNode} from \"@/api/flowable/definition\";\r\nimport BpmnViewer from '@/components/Process/viewer';\r\nimport FlowHistory from '@/components/FlowHistory';\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Record\",\r\n  components: {\r\n    BpmnViewer,\r\n    FlowHistory\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      // 模型xml数据\r\n      flowData: {},\r\n      activeName: '1',\r\n      // 查询参数\r\n      queryParams: {},\r\n      // 遮罩层\r\n      loading: true,\r\n      flowRecordList: [], // 流程流转数据\r\n      taskForm:{\r\n        multiple: false,\r\n        comment:\"\", // 意见内容\r\n        procInsId: \"\", // 流程实例编号\r\n        instanceId: \"\", // 流程实例编号\r\n        deployId: \"\",  // 流程定义编号\r\n        taskId: \"\" ,// 流程任务编号\r\n        procDefId: \"\",  // 流程编号\r\n      },\r\n      formKey: Date.now() // VForm组件的唯一key\r\n    };\r\n  },\r\n  created() {\r\n    this.taskForm.deployId = this.$route.query && this.$route.query.deployId;\r\n    this.taskForm.taskId  = this.$route.query && this.$route.query.taskId;\r\n    this.taskForm.procInsId = this.$route.query && this.$route.query.procInsId;\r\n    // 流程任务重获取变量表单\r\n    this.processVariables( this.taskForm.taskId)\r\n    this.getFlowRecordList(this.taskForm.procInsId, this.taskForm.deployId);\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      if (tab.name === '3'){\r\n        flowXmlAndNode({procInsId:this.taskForm.procInsId,deployId:this.taskForm.deployId}).then(res => {\r\n          this.flowData = res.data;\r\n        })\r\n      }\r\n    },\r\n    setIcon(val) {\r\n      if (val) {\r\n        return \"el-icon-check\";\r\n      } else {\r\n        return \"el-icon-time\";\r\n      }\r\n    },\r\n    setColor(val) {\r\n      if (val) {\r\n        return \"#2bc418\";\r\n      } else {\r\n        return \"#b3bdbb\";\r\n      }\r\n    },\r\n    /** 流程流转记录 */\r\n    getFlowRecordList(procInsId, deployId) {\r\n      const that = this\r\n      const params = {procInsId: procInsId, deployId: deployId}\r\n      flowRecord(params).then(res => {\r\n        that.flowRecordList = res.data.flowList;\r\n      }).catch(res => {\r\n        this.goBack();\r\n      })\r\n    },\r\n    /** 获取流程变量内容 */\r\n    processVariables(taskId) {\r\n      if (taskId) {\r\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\r\n        getProcessVariables(taskId).then(res => {\r\n          // 更新formKey以强制重新渲染VForm组件，避免key冲突\r\n          this.formKey = Date.now();\r\n\r\n          this.$nextTick(() => {\r\n            // 回显表单\r\n            this.$refs.vFormRef.setFormJson(res.data.formJson);\r\n            this.$nextTick(() => {\r\n              // 加载表单填写的数据\r\n              this.$refs.vFormRef.setFormData(res.data);\r\n              this.$nextTick(() => {\r\n                // 表单禁用\r\n                this.$refs.vFormRef.disableForm();\r\n              })\r\n            })\r\n          })\r\n        });\r\n      }\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      const obj = { path: \"/task/process\", query: { t: Date.now()} };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.test-form {\r\n  margin: 15px auto;\r\n  width: 800px;\r\n  padding: 15px;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n.clearfix:after {\r\n  clear: both\r\n}\r\n\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.my-label {\r\n  background: #E1F3D8;\r\n}\r\n\r\n.current-form-card {\r\n  border: 2px solid #409EFF;\r\n}\r\n\r\n.current-form-header {\r\n  color: #409EFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.current-form-header i {\r\n  margin-right: 8px;\r\n}\r\n</style>\r\n"]}]}