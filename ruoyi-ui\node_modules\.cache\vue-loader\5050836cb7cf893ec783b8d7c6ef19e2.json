{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue?vue&type=style&index=0&id=9469f620&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue", "mtime": 1752396969884}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnRlc3QtZm9ybSB7DQogIG1hcmdpbjogMTVweCBhdXRvOw0KICB3aWR0aDogODAwcHg7DQogIHBhZGRpbmc6IDE1cHg7DQp9DQoNCi5jbGVhcmZpeDpiZWZvcmUsDQouY2xlYXJmaXg6YWZ0ZXIgew0KICBkaXNwbGF5OiB0YWJsZTsNCiAgY29udGVudDogIiI7DQp9DQoNCi5jbGVhcmZpeDphZnRlciB7DQogIGNsZWFyOiBib3RoDQp9DQoNCi5ib3gtY2FyZCB7DQogIHdpZHRoOiAxMDAlOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouZWwtdGFnICsgLmVsLXRhZyB7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KfQ0KDQoubXktbGFiZWwgew0KICBiYWNrZ3JvdW5kOiAjRTFGM0Q4Ow0KfQ0KDQovKiDljoblj7LoioLngrnmoLflvI8gKi8NCi5oaXN0b3J5LWNvbGxhcHNlIHsNCiAgYm9yZGVyOiAxcHggc29saWQgI0VCRUVGNTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouaGlzdG9yeS1jb2xsYXBzZSAuZWwtY29sbGFwc2UtaXRlbV9faGVhZGVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI0Y1RjdGQTsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNFQkVFRjU7DQogIHBhZGRpbmc6IDAgMjBweDsNCiAgaGVpZ2h0OiA0OHB4Ow0KICBsaW5lLWhlaWdodDogNDhweDsNCn0NCg0KLmhpc3RvcnktY29sbGFwc2UgLmVsLWNvbGxhcHNlLWl0ZW1fX2NvbnRlbnQgew0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjRkFGQUZBOw0KfQ0KDQouaGlzdG9yeS10aXRsZSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHdpZHRoOiAxMDAlOw0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5oaXN0b3J5LXRpdGxlIC5ub2RlLW5hbWUgew0KICBmb250LXdlaWdodDogNjAwOw0KICBtYXJnaW4tbGVmdDogOHB4Ow0KICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQouaGlzdG9yeS10aXRsZSAuYXNzaWduZWUtbmFtZSB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQp9DQoNCi5oaXN0b3J5LXRpdGxlIC5maW5pc2gtdGltZSB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXNpemU6IDEycHg7DQogIG1hcmdpbi1sZWZ0OiBhdXRvOw0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQoNCi5oaXN0b3J5LWNvbnRlbnQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KfQ0KDQouY29tbWVudC1jb250ZW50IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI0Y4RjlGQTsNCiAgcGFkZGluZzogMTJweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM0MDlFRkY7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCiAgY29sb3I6ICM2MDYyNjY7DQp9DQoNCi5mb3JtLWRhdGEtc2VjdGlvbiB7DQogIG1hcmdpbi10b3A6IDE2cHg7DQp9DQoNCi5jdXJyZW50LWZvcm0tY2FyZCB7DQogIGJvcmRlcjogMnB4IHNvbGlkICM0MDlFRkY7DQp9DQoNCi5jdXJyZW50LWZvcm0taGVhZGVyIHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5jdXJyZW50LWZvcm0taGVhZGVyIGkgew0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0iBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/flowable/task/todo/detail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">待办任务</span>\r\n        <el-tag style=\"margin-left:10px\">发起人:{{ startUser }}</el-tag>\r\n        <el-tag>任务节点:{{ taskName }}</el-tag>\r\n        <el-button style=\"float: right;\" size=\"mini\" type=\"danger\" @click=\"goBack\">关闭</el-button>\r\n      </div>\r\n      <el-tabs tab-position=\"top\" v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <!--表单信息-->\r\n        <el-tab-pane label=\"表单信息\" name=\"1\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <div v-if=\"flowRecordList && flowRecordList.length > 0\" style=\"margin-bottom: 20px;\">\r\n              <h4 style=\"margin-bottom: 15px; color: #606266;\">\r\n                <i class=\"el-icon-time\"></i> 流程历史记录\r\n              </h4>\r\n              <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\r\n                <el-collapse-item\r\n                  v-for=\"(record, index) in flowRecordList\"\r\n                  :key=\"`history-${index}-${record.taskId || record.id || index}`\"\r\n                  :name=\"`history-${index}`\"\r\n                >\r\n                  <template slot=\"title\">\r\n                    <div class=\"history-title\">\r\n                      <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\r\n                      <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\r\n                      <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\r\n                      <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\r\n                      <el-tag\r\n                        :type=\"getStatusTagType(record)\"\r\n                        size=\"mini\"\r\n                        style=\"margin-left: 10px;\"\r\n                      >\r\n                        {{ getStatusText(record) }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </template>\r\n\r\n                  <div class=\"history-content\">\r\n                    <el-descriptions :column=\"2\" size=\"small\" border>\r\n                      <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\r\n                        <span>{{ record.assigneeName }}</span>\r\n                        <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\r\n                        {{ record.candidate }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\r\n                        {{ record.createTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\r\n                        {{ record.finishTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\r\n                        {{ record.duration }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\r\n                        <div class=\"comment-content\">\r\n                          {{ record.comment.comment }}\r\n                        </div>\r\n                      </el-descriptions-item>\r\n                    </el-descriptions>\r\n                  </div>\r\n                </el-collapse-item>\r\n              </el-collapse>\r\n            </div>\r\n\r\n            <!-- 当前表单 -->\r\n            <el-card class=\"current-form-card\" shadow=\"hover\">\r\n              <div slot=\"header\" class=\"current-form-header\">\r\n                <i class=\"el-icon-edit-outline\"></i>\r\n                <span>当前待处理表单</span>\r\n              </div>\r\n              <v-form-render ref=\"vFormRef\"/>\r\n            </el-card>\r\n\r\n            <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;margin-top: 20px;\">\r\n              <el-button type=\"primary\" @click=\"handleComplete\">审 批</el-button>\r\n            </div>\r\n          </el-col>\r\n        </el-tab-pane>\r\n\r\n        <!--流程流转记录-->\r\n        <el-tab-pane label=\"流转记录\" name=\"2\">\r\n          <!--flowRecordList-->\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <div class=\"block\">\r\n              <el-timeline>\r\n                <el-timeline-item\r\n                  v-for=\"(item,index ) in flowRecordList\"\r\n                  :key=\"index\"\r\n                  :icon=\"setIcon(item.finishTime)\"\r\n                  :color=\"setColor(item.finishTime)\"\r\n                >\r\n                  <p style=\"font-weight: 700\">{{ item.taskName }}</p>\r\n                  <el-card :body-style=\"{ padding: '10px' }\">\r\n                    <el-descriptions class=\"margin-top\" :column=\"1\" size=\"small\" border>\r\n                      <el-descriptions-item v-if=\"item.assigneeName\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-user\"></i>办理人</template>\r\n                        {{ item.assigneeName }}\r\n                        <el-tag type=\"info\" size=\"mini\">{{ item.deptName }}</el-tag>\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.candidate\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-user\"></i>候选办理</template>\r\n                        {{ item.candidate }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-date\"></i>接收时间</template>\r\n                        {{ item.createTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.finishTime\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-date\"></i>处理时间</template>\r\n                        {{ item.finishTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.duration\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-time\"></i>耗时</template>\r\n                        {{ item.duration }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.comment\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-tickets\"></i>处理意见</template>\r\n                        {{ item.comment.comment }}\r\n                      </el-descriptions-item>\r\n                    </el-descriptions>\r\n                  </el-card>\r\n                </el-timeline-item>\r\n              </el-timeline>\r\n            </div>\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <!--流程图-->\r\n        <el-tab-pane label=\"流程图\" name=\"3\">\r\n          <bpmn-viewer :flowData=\"flowData\" :procInsId=\"taskForm.procInsId\"/>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n      <!--审批任务-->\r\n      <el-dialog :title=\"completeTitle\" :visible.sync=\"completeOpen\" width=\"60%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\">\r\n          <el-form-item prop=\"targetKey\">\r\n            <flow-user v-if=\"checkSendUser\" :checkType=\"checkType\" @handleUserSelect=\"handleUserSelect\"></flow-user>\r\n            <flow-role v-if=\"checkSendRole\" @handleRoleSelect=\"handleRoleSelect\"></flow-role>\r\n          </el-form-item>\r\n          <el-form-item label=\"处理意见\" label-width=\"80px\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入处理意见', trigger: 'blur' }]\">\r\n            <el-input type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入处理意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"completeOpen = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\r\n        </span>\r\n      </el-dialog>\r\n      <!--退回流程-->\r\n      <el-dialog :title=\"returnTitle\" :visible.sync=\"returnOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"退回节点\" prop=\"targetKey\">\r\n            <el-radio-group v-model=\"taskForm.targetKey\">\r\n              <el-radio-button\r\n                v-for=\"item in returnTaskList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.id\"\r\n              >{{ item.name }}\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"退回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"returnOpen = false\">取 消</el-button>\r\n              <el-button type=\"primary\" @click=\"taskReturn\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n      <!--驳回流程-->\r\n      <el-dialog :title=\"rejectTitle\" :visible.sync=\"rejectOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"驳回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"rejectOpen = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"taskReject\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {flowRecord} from \"@/api/flowable/finished\";\r\nimport FlowUser from '@/components/flow/User'\r\nimport FlowRole from '@/components/flow/Role'\r\nimport {flowXmlAndNode} from \"@/api/flowable/definition\";\r\nimport {\r\n  complete,\r\n  rejectTask,\r\n  returnList,\r\n  returnTask,\r\n  getNextFlowNode,\r\n  delegate,\r\n  flowTaskForm\r\n} from \"@/api/flowable/todo\";\r\nimport BpmnViewer from '@/components/Process/viewer';\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Record\",\r\n  components: {\r\n    BpmnViewer,\r\n    FlowUser,\r\n    FlowRole,\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      eventName: \"click\",\r\n      // 流程数据\r\n      flowData: {},\r\n      activeName: '1',\r\n      // 遮罩层\r\n      loading: true,\r\n      flowRecordList: [], // 流程流转数据\r\n      rules: {}, // 表单校验\r\n      taskForm: {\r\n        returnTaskShow: false, // 是否展示回退表单\r\n        delegateTaskShow: false, // 是否展示回退表单\r\n        defaultTaskShow: true, // 默认处理\r\n        comment: \"\", // 意见内容\r\n        procInsId: \"\", // 流程实例编号\r\n        instanceId: \"\", // 流程实例编号\r\n        deployId: \"\",  // 流程定义编号\r\n        taskId: \"\",// 流程任务编号\r\n        procDefId: \"\",  // 流程编号\r\n        targetKey: \"\",\r\n        variables: {},\r\n      },\r\n      returnTaskList: [],  // 回退列表数据\r\n      completeTitle: null,\r\n      completeOpen: false,\r\n      returnTitle: null,\r\n      returnOpen: false,\r\n      rejectOpen: false,\r\n      rejectTitle: null,\r\n      checkSendUser: false, // 是否展示人员选择模块\r\n      checkSendRole: false,// 是否展示角色选择模块\r\n      checkType: 'single', // 选择类型\r\n      taskName: null, // 任务节点\r\n      startUser: null, // 发起人信息,\r\n      multiInstanceVars: '', // 会签节点\r\n      formJson:{},\r\n      activeHistoryNames: [], // 展开的历史节点\r\n      completedFlowRecords: [] // 已完成的流程记录\r\n    };\r\n  },\r\n  created() {\r\n    if (this.$route.query) {\r\n      this.taskName = this.$route.query.taskName;\r\n      this.startUser = this.$route.query.startUser;\r\n      this.taskForm.deployId = this.$route.query.deployId;\r\n      this.taskForm.taskId = this.$route.query.taskId;\r\n      this.taskForm.procInsId = this.$route.query.procInsId;\r\n      this.taskForm.executionId = this.$route.query.executionId;\r\n      this.taskForm.instanceId = this.$route.query.procInsId;\r\n      // 流程任务获取变量信息\r\n      if (this.taskForm.taskId) {\r\n        this.getFlowTaskForm(this.taskForm.taskId);\r\n      }\r\n      this.getFlowRecordList(this.taskForm.procInsId, this.taskForm.deployId);\r\n    }\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      if (tab.name === '3') {\r\n        flowXmlAndNode({procInsId: this.taskForm.procInsId, deployId: this.taskForm.deployId}).then(res => {\r\n          this.flowData = res.data;\r\n        })\r\n      }\r\n    },\r\n    setIcon(val) {\r\n      if (val) {\r\n        return \"el-icon-check\";\r\n      } else {\r\n        return \"el-icon-time\";\r\n      }\r\n    },\r\n    setColor(val) {\r\n      if (val) {\r\n        return \"#2bc418\";\r\n      } else {\r\n        return \"#b3bdbb\";\r\n      }\r\n    },\r\n    // 用户信息选中数据\r\n    handleUserSelect(selection) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.userId.toString());\r\n          if (this.multiInstanceVars) {\r\n            this.$set(this.taskForm.variables, this.multiInstanceVars,  selectVal);\r\n          } else {\r\n            this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n          }\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection.userId.toString());\r\n        }\r\n      }\r\n    },\r\n    // 角色信息选中数据\r\n    handleRoleSelect(selection, roleName) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.roleId.toString());\r\n          this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection);\r\n        }\r\n      }\r\n    },\r\n    /** 流程流转记录 */\r\n    getFlowRecordList(procInsId, deployId) {\r\n      const that = this\r\n      const params = {procInsId: procInsId, deployId: deployId}\r\n      flowRecord(params).then(res => {\r\n        that.flowRecordList = res.data.flowList;\r\n        // 处理历史节点数据\r\n        that.processFlowRecords();\r\n      }).catch(res => {\r\n        this.goBack();\r\n      })\r\n    },\r\n    /** 流程节点表单 */\r\n    getFlowTaskForm(taskId) {\r\n      if (taskId) {\r\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\r\n        flowTaskForm({taskId: taskId}).then(res => {\r\n          // 回显表单\r\n          this.$refs.vFormRef.setFormJson(res.data.formJson);\r\n          this.formJson = res.data.formJson;\r\n          this.$nextTick(() => {\r\n            // 加载表单填写的数据\r\n            this.$refs.vFormRef.setFormData(res.data);\r\n            // this.$nextTick(() => {\r\n            //   // 表单禁用\r\n            //   this.$refs.vFormRef.disableForm();\r\n            // })\r\n          })\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 委派任务 */\r\n    handleDelegate() {\r\n      this.taskForm.delegateTaskShow = true;\r\n      this.taskForm.defaultTaskShow = false;\r\n    },\r\n    handleAssign() {\r\n\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      const obj = { path: \"/task/todo\", query: { t: Date.now()} };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 驳回任务 */\r\n    handleReject() {\r\n      this.rejectOpen = true;\r\n      this.rejectTitle = \"驳回流程\";\r\n    },\r\n    /** 驳回任务 */\r\n    taskReject() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          rejectTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 可退回任务列表 */\r\n    handleReturn() {\r\n      this.returnOpen = true;\r\n      this.returnTitle = \"退回流程\";\r\n      returnList(this.taskForm).then(res => {\r\n        this.returnTaskList = res.data;\r\n      })\r\n    },\r\n    /** 提交退回任务 */\r\n    taskReturn() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          returnTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack()\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelTask() {\r\n      this.taskForm.returnTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 委派任务 */\r\n    submitDeleteTask() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          delegate(this.taskForm).then(response => {\r\n            this.$modal.msgSuccess(response.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelDelegateTask() {\r\n      this.taskForm.delegateTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 加载审批任务弹框 */\r\n    handleComplete() {\r\n      this.completeOpen = true;\r\n      this.completeTitle = \"流程审批\";\r\n      this.submitForm();\r\n    },\r\n    /** 用户审批任务 */\r\n    taskComplete() {\r\n      if (!this.taskForm.variables && this.checkSendUser) {\r\n        this.$modal.msgError(\"请选择流程接收人员!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.variables && this.checkSendRole) {\r\n        this.$modal.msgError(\"请选择流程接收角色组!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.comment) {\r\n        this.$modal.msgError(\"请输入审批意见!\");\r\n        return;\r\n      }\r\n      if (this.taskForm) {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      } else {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      }\r\n    },\r\n    /** 申请流程表单数据提交 */\r\n    submitForm() {\r\n      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况\r\n      const params = {taskId: this.taskForm.taskId}\r\n      getNextFlowNode(params).then(res => {\r\n        this.$refs.vFormRef.getFormData().then(formData => {\r\n          Object.assign(this.taskForm.variables, formData);\r\n          this.taskForm.variables.formJson = this.formJson;\r\n          console.log(this.taskForm, \"流程审批提交表单数据1\")\r\n        }).catch(error => {\r\n          // this.$modal.msgError(error)\r\n        })\r\n        const data = res.data;\r\n        if (data) {\r\n          if (data.dataType === 'dynamic') {\r\n            if (data.type === 'assignee') { // 指定人员\r\n              this.checkSendUser = true;\r\n              this.checkType = \"single\";\r\n            } else if (data.type === 'candidateUsers') {  // 候选人员(多个)\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            } else if (data.type === 'candidateGroups') { // 指定组(所属角色接收任务)\r\n              this.checkSendRole = true;\r\n            } else { // 会签\r\n              // 流程设计指定的 elementVariable 作为会签人员列表\r\n              this.multiInstanceVars = data.vars;\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 动态绑定操作按钮的点击事件\r\n    handleButtonClick(method) {\r\n      this[method]();\r\n    },\r\n\r\n    /** 获取历史节点标题 */\r\n    getHistoryTitle(record) {\r\n      return `${record.taskName} - ${record.assigneeName || '未分配'} - ${record.finishTime || '处理中'}`;\r\n    },\r\n\r\n    /** 获取历史节点图标 */\r\n    getHistoryIcon(record) {\r\n      if (record.finishTime) {\r\n        return 'el-icon-check';\r\n      } else {\r\n        return 'el-icon-time';\r\n      }\r\n    },\r\n\r\n    /** 获取历史节点颜色 */\r\n    getHistoryColor(record) {\r\n      if (record.finishTime) {\r\n        return '#67C23A';\r\n      } else {\r\n        return '#E6A23C';\r\n      }\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(record) {\r\n      if (record.finishTime) {\r\n        return 'success';\r\n      } else {\r\n        return 'warning';\r\n      }\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(record) {\r\n      if (record.finishTime) {\r\n        return '已完成';\r\n      } else {\r\n        return '处理中';\r\n      }\r\n    },\r\n\r\n    /** 处理流程记录数据 */\r\n    processFlowRecords() {\r\n      console.log('原始流程记录数据:', this.flowRecordList);\r\n\r\n      // 默认展开最近的一个节点（如果有数据的话）\r\n      if (this.flowRecordList && this.flowRecordList.length > 0) {\r\n        // 默认展开第一个节点\r\n        this.activeHistoryNames = ['0'];\r\n      }\r\n\r\n      console.log('展开的节点:', this.activeHistoryNames);\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.test-form {\r\n  margin: 15px auto;\r\n  width: 800px;\r\n  padding: 15px;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both\r\n}\r\n\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.my-label {\r\n  background: #E1F3D8;\r\n}\r\n\r\n/* 历史节点样式 */\r\n.history-collapse {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-collapse .el-collapse-item__header {\r\n  background-color: #F5F7FA;\r\n  border-bottom: 1px solid #EBEEF5;\r\n  padding: 0 20px;\r\n  height: 48px;\r\n  line-height: 48px;\r\n}\r\n\r\n.history-collapse .el-collapse-item__content {\r\n  padding: 20px;\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.history-title {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n}\r\n\r\n.history-title .node-name {\r\n  font-weight: 600;\r\n  margin-left: 8px;\r\n  margin-right: 15px;\r\n  color: #303133;\r\n}\r\n\r\n.history-title .assignee-name {\r\n  color: #606266;\r\n  margin-right: 15px;\r\n}\r\n\r\n.history-title .finish-time {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-left: auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.history-content {\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n}\r\n\r\n.comment-content {\r\n  background-color: #F8F9FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  font-style: italic;\r\n  color: #606266;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 16px;\r\n}\r\n\r\n.current-form-card {\r\n  border: 2px solid #409EFF;\r\n}\r\n\r\n.current-form-header {\r\n  color: #409EFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.current-form-header i {\r\n  margin-right: 8px;\r\n}\r\n</style>\r\n"]}]}