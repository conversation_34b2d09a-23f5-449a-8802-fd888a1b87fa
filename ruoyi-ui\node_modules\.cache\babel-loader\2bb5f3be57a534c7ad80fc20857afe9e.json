{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue", "mtime": 1752405854599}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_finished", "require", "_User", "_interopRequireDefault", "_Role", "_definition", "_todo", "_viewer", "name", "components", "BpmnViewer", "FlowUser", "FlowRole", "props", "data", "eventName", "flowData", "activeName", "loading", "flowRecordList", "rules", "taskForm", "returnTaskShow", "delegateTaskShow", "defaultTaskShow", "comment", "procInsId", "instanceId", "deployId", "taskId", "procDefId", "<PERSON><PERSON><PERSON>", "variables", "returnTaskList", "completeTitle", "completeOpen", "returnTitle", "returnOpen", "rejectOpen", "rejectTitle", "checkSendUser", "checkSendRole", "checkType", "taskName", "startUser", "multiInstanceVars", "formJson", "activeHistoryNames", "completedFlowRecords", "formKey", "Date", "now", "created", "$route", "query", "executionId", "getFlowTaskForm", "getFlowRecordList", "methods", "handleClick", "tab", "event", "_this", "flowXmlAndNode", "then", "res", "setIcon", "val", "setColor", "handleUserSelect", "selection", "Array", "selectVal", "map", "item", "userId", "toString", "$set", "join", "handleRoleSelect", "<PERSON><PERSON><PERSON>", "roleId", "_this2", "that", "params", "flowRecord", "flowList", "processFlowRecords", "catch", "goBack", "_this3", "flowTaskForm", "$nextTick", "$refs", "vFormRef", "set<PERSON><PERSON><PERSON><PERSON>", "setFormData", "handleDelegate", "handleAssign", "obj", "path", "t", "$tab", "closeOpenPage", "handleReject", "taskReject", "_this4", "validate", "valid", "rejectTask", "$modal", "msgSuccess", "msg", "handleReturn", "_this5", "returnList", "taskReturn", "_this6", "returnTask", "cancelTask", "submitDeleteTask", "_this7", "delegate", "response", "cancelDelegateTask", "handleComplete", "submitForm", "taskComplete", "_this8", "msgError", "complete", "_this9", "getNextFlowNode", "getFormData", "formData", "Object", "assign", "console", "log", "error", "dataType", "type", "vars", "handleButtonClick", "method", "getHistoryTitle", "record", "concat", "assignee<PERSON>ame", "finishTime", "getHistoryIcon", "getHistoryColor", "getStatusTagType", "getStatusText", "length"], "sources": ["src/views/flowable/task/todo/detail/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">待办任务</span>\r\n        <el-tag style=\"margin-left:10px\">发起人:{{ startUser }}</el-tag>\r\n        <el-tag>任务节点:{{ taskName }}</el-tag>\r\n        <el-button style=\"float: right;\" size=\"mini\" type=\"danger\" @click=\"goBack\">关闭</el-button>\r\n      </div>\r\n      <el-tabs tab-position=\"top\" v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <!--表单信息-->\r\n        <el-tab-pane label=\"表单信息\" name=\"1\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <div v-if=\"flowRecordList && flowRecordList.length > 0\" style=\"margin-bottom: 20px;\">\r\n              <h4 style=\"margin-bottom: 15px; color: #606266;\">\r\n                <i class=\"el-icon-time\"></i> 流程历史记录\r\n              </h4>\r\n              <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\r\n                <el-collapse-item\r\n                  v-for=\"(record, index) in flowRecordList\"\r\n                  :key=\"`history-${index}-${record.taskId || record.id || index}`\"\r\n                  :name=\"`history-${index}`\"\r\n                >\r\n                  <template slot=\"title\">\r\n                    <div class=\"history-title\">\r\n                      <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\r\n                      <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\r\n                      <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\r\n                      <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\r\n                      <el-tag\r\n                        :type=\"getStatusTagType(record)\"\r\n                        size=\"mini\"\r\n                        style=\"margin-left: 10px;\"\r\n                      >\r\n                        {{ getStatusText(record) }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </template>\r\n\r\n                  <div class=\"history-content\">\r\n                    <el-descriptions :column=\"2\" size=\"small\" border>\r\n                      <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\r\n                        <span>{{ record.assigneeName }}</span>\r\n                        <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\r\n                        {{ record.candidate }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\r\n                        {{ record.createTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\r\n                        {{ record.finishTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\r\n                        {{ record.duration }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\r\n                        <div class=\"comment-content\">\r\n                          {{ record.comment.comment }}\r\n                        </div>\r\n                      </el-descriptions-item>\r\n                    </el-descriptions>\r\n                  </div>\r\n                </el-collapse-item>\r\n              </el-collapse>\r\n            </div>\r\n\r\n            <!-- 当前表单 -->\r\n            <el-card class=\"current-form-card\" shadow=\"hover\">\r\n              <div slot=\"header\" class=\"current-form-header\">\r\n                <i class=\"el-icon-edit-outline\"></i>\r\n                <span>当前待处理表单</span>\r\n              </div>\r\n              <v-form-render ref=\"vFormRef\" :key=\"formKey\"/>\r\n            </el-card>\r\n\r\n            <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;margin-top: 20px;\">\r\n              <el-button type=\"primary\" @click=\"handleComplete\">审 批</el-button>\r\n            </div>\r\n          </el-col>\r\n        </el-tab-pane>\r\n\r\n        <!--流程流转记录-->\r\n        <el-tab-pane label=\"流转记录\" name=\"2\">\r\n          <!--flowRecordList-->\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <div class=\"block\">\r\n              <el-timeline>\r\n                <el-timeline-item\r\n                  v-for=\"(item,index ) in flowRecordList\"\r\n                  :key=\"index\"\r\n                  :icon=\"setIcon(item.finishTime)\"\r\n                  :color=\"setColor(item.finishTime)\"\r\n                >\r\n                  <p style=\"font-weight: 700\">{{ item.taskName }}</p>\r\n                  <el-card :body-style=\"{ padding: '10px' }\">\r\n                    <el-descriptions class=\"margin-top\" :column=\"1\" size=\"small\" border>\r\n                      <el-descriptions-item v-if=\"item.assigneeName\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-user\"></i>办理人</template>\r\n                        {{ item.assigneeName }}\r\n                        <el-tag type=\"info\" size=\"mini\">{{ item.deptName }}</el-tag>\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.candidate\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-user\"></i>候选办理</template>\r\n                        {{ item.candidate }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-date\"></i>接收时间</template>\r\n                        {{ item.createTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.finishTime\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-date\"></i>处理时间</template>\r\n                        {{ item.finishTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.duration\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-time\"></i>耗时</template>\r\n                        {{ item.duration }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"item.comment\" label-class-name=\"my-label\">\r\n                        <template slot=\"label\"><i class=\"el-icon-tickets\"></i>处理意见</template>\r\n                        {{ item.comment.comment }}\r\n                      </el-descriptions-item>\r\n                    </el-descriptions>\r\n                  </el-card>\r\n                </el-timeline-item>\r\n              </el-timeline>\r\n            </div>\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <!--流程图-->\r\n        <el-tab-pane label=\"流程图\" name=\"3\">\r\n          <bpmn-viewer :flowData=\"flowData\" :procInsId=\"taskForm.procInsId\"/>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n      <!--审批任务-->\r\n      <el-dialog :title=\"completeTitle\" :visible.sync=\"completeOpen\" width=\"60%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\">\r\n          <el-form-item prop=\"targetKey\">\r\n            <flow-user v-if=\"checkSendUser\" :checkType=\"checkType\" @handleUserSelect=\"handleUserSelect\"></flow-user>\r\n            <flow-role v-if=\"checkSendRole\" @handleRoleSelect=\"handleRoleSelect\"></flow-role>\r\n          </el-form-item>\r\n          <el-form-item label=\"处理意见\" label-width=\"80px\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入处理意见', trigger: 'blur' }]\">\r\n            <el-input type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入处理意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"completeOpen = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\r\n        </span>\r\n      </el-dialog>\r\n      <!--退回流程-->\r\n      <el-dialog :title=\"returnTitle\" :visible.sync=\"returnOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"退回节点\" prop=\"targetKey\">\r\n            <el-radio-group v-model=\"taskForm.targetKey\">\r\n              <el-radio-button\r\n                v-for=\"item in returnTaskList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.id\"\r\n              >{{ item.name }}\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"退回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"returnOpen = false\">取 消</el-button>\r\n              <el-button type=\"primary\" @click=\"taskReturn\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n      <!--驳回流程-->\r\n      <el-dialog :title=\"rejectTitle\" :visible.sync=\"rejectOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"驳回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"rejectOpen = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"taskReject\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {flowRecord} from \"@/api/flowable/finished\";\r\nimport FlowUser from '@/components/flow/User'\r\nimport FlowRole from '@/components/flow/Role'\r\nimport {flowXmlAndNode} from \"@/api/flowable/definition\";\r\nimport {\r\n  complete,\r\n  rejectTask,\r\n  returnList,\r\n  returnTask,\r\n  getNextFlowNode,\r\n  delegate,\r\n  flowTaskForm\r\n} from \"@/api/flowable/todo\";\r\nimport BpmnViewer from '@/components/Process/viewer';\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Record\",\r\n  components: {\r\n    BpmnViewer,\r\n    FlowUser,\r\n    FlowRole,\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      eventName: \"click\",\r\n      // 流程数据\r\n      flowData: {},\r\n      activeName: '1',\r\n      // 遮罩层\r\n      loading: true,\r\n      flowRecordList: [], // 流程流转数据\r\n      rules: {}, // 表单校验\r\n      taskForm: {\r\n        returnTaskShow: false, // 是否展示回退表单\r\n        delegateTaskShow: false, // 是否展示回退表单\r\n        defaultTaskShow: true, // 默认处理\r\n        comment: \"\", // 意见内容\r\n        procInsId: \"\", // 流程实例编号\r\n        instanceId: \"\", // 流程实例编号\r\n        deployId: \"\",  // 流程定义编号\r\n        taskId: \"\",// 流程任务编号\r\n        procDefId: \"\",  // 流程编号\r\n        targetKey: \"\",\r\n        variables: {},\r\n      },\r\n      returnTaskList: [],  // 回退列表数据\r\n      completeTitle: null,\r\n      completeOpen: false,\r\n      returnTitle: null,\r\n      returnOpen: false,\r\n      rejectOpen: false,\r\n      rejectTitle: null,\r\n      checkSendUser: false, // 是否展示人员选择模块\r\n      checkSendRole: false,// 是否展示角色选择模块\r\n      checkType: 'single', // 选择类型\r\n      taskName: null, // 任务节点\r\n      startUser: null, // 发起人信息,\r\n      multiInstanceVars: '', // 会签节点\r\n      formJson:{},\r\n      activeHistoryNames: [], // 展开的历史节点\r\n      completedFlowRecords: [], // 已完成的流程记录\r\n      formKey: Date.now() // VForm组件的唯一key\r\n    };\r\n  },\r\n  created() {\r\n    if (this.$route.query) {\r\n      this.taskName = this.$route.query.taskName;\r\n      this.startUser = this.$route.query.startUser;\r\n      this.taskForm.deployId = this.$route.query.deployId;\r\n      this.taskForm.taskId = this.$route.query.taskId;\r\n      this.taskForm.procInsId = this.$route.query.procInsId;\r\n      this.taskForm.executionId = this.$route.query.executionId;\r\n      this.taskForm.instanceId = this.$route.query.procInsId;\r\n      // 流程任务获取变量信息\r\n      if (this.taskForm.taskId) {\r\n        this.getFlowTaskForm(this.taskForm.taskId);\r\n      }\r\n      this.getFlowRecordList(this.taskForm.procInsId, this.taskForm.deployId);\r\n    }\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      if (tab.name === '3') {\r\n        flowXmlAndNode({procInsId: this.taskForm.procInsId, deployId: this.taskForm.deployId}).then(res => {\r\n          this.flowData = res.data;\r\n        })\r\n      }\r\n    },\r\n    setIcon(val) {\r\n      if (val) {\r\n        return \"el-icon-check\";\r\n      } else {\r\n        return \"el-icon-time\";\r\n      }\r\n    },\r\n    setColor(val) {\r\n      if (val) {\r\n        return \"#2bc418\";\r\n      } else {\r\n        return \"#b3bdbb\";\r\n      }\r\n    },\r\n    // 用户信息选中数据\r\n    handleUserSelect(selection) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.userId.toString());\r\n          if (this.multiInstanceVars) {\r\n            this.$set(this.taskForm.variables, this.multiInstanceVars,  selectVal);\r\n          } else {\r\n            this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n          }\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection.userId.toString());\r\n        }\r\n      }\r\n    },\r\n    // 角色信息选中数据\r\n    handleRoleSelect(selection, roleName) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.roleId.toString());\r\n          this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection);\r\n        }\r\n      }\r\n    },\r\n    /** 流程流转记录 */\r\n    getFlowRecordList(procInsId, deployId) {\r\n      const that = this\r\n      const params = {procInsId: procInsId, deployId: deployId}\r\n      flowRecord(params).then(res => {\r\n        that.flowRecordList = res.data.flowList;\r\n        // 处理历史节点数据\r\n        that.processFlowRecords();\r\n      }).catch(res => {\r\n        this.goBack();\r\n      })\r\n    },\r\n    /** 流程节点表单 */\r\n    getFlowTaskForm(taskId) {\r\n      if (taskId) {\r\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\r\n        flowTaskForm({taskId: taskId}).then(res => {\r\n          // 更新formKey以强制重新渲染VForm组件，避免key冲突\r\n          this.formKey = Date.now();\r\n\r\n          this.$nextTick(() => {\r\n            // 回显表单\r\n            this.$refs.vFormRef.setFormJson(res.data.formJson);\r\n            this.formJson = res.data.formJson;\r\n            this.$nextTick(() => {\r\n              // 加载表单填写的数据\r\n              this.$refs.vFormRef.setFormData(res.data);\r\n              // this.$nextTick(() => {\r\n              //   // 表单禁用\r\n              //   this.$refs.vFormRef.disableForm();\r\n              // })\r\n            })\r\n          })\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 委派任务 */\r\n    handleDelegate() {\r\n      this.taskForm.delegateTaskShow = true;\r\n      this.taskForm.defaultTaskShow = false;\r\n    },\r\n    handleAssign() {\r\n\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      const obj = { path: \"/task/todo\", query: { t: Date.now()} };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 驳回任务 */\r\n    handleReject() {\r\n      this.rejectOpen = true;\r\n      this.rejectTitle = \"驳回流程\";\r\n    },\r\n    /** 驳回任务 */\r\n    taskReject() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          rejectTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 可退回任务列表 */\r\n    handleReturn() {\r\n      this.returnOpen = true;\r\n      this.returnTitle = \"退回流程\";\r\n      returnList(this.taskForm).then(res => {\r\n        this.returnTaskList = res.data;\r\n      })\r\n    },\r\n    /** 提交退回任务 */\r\n    taskReturn() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          returnTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack()\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelTask() {\r\n      this.taskForm.returnTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 委派任务 */\r\n    submitDeleteTask() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          delegate(this.taskForm).then(response => {\r\n            this.$modal.msgSuccess(response.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelDelegateTask() {\r\n      this.taskForm.delegateTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 加载审批任务弹框 */\r\n    handleComplete() {\r\n      this.completeOpen = true;\r\n      this.completeTitle = \"流程审批\";\r\n      this.submitForm();\r\n    },\r\n    /** 用户审批任务 */\r\n    taskComplete() {\r\n      if (!this.taskForm.variables && this.checkSendUser) {\r\n        this.$modal.msgError(\"请选择流程接收人员!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.variables && this.checkSendRole) {\r\n        this.$modal.msgError(\"请选择流程接收角色组!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.comment) {\r\n        this.$modal.msgError(\"请输入审批意见!\");\r\n        return;\r\n      }\r\n      if (this.taskForm) {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      } else {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      }\r\n    },\r\n    /** 申请流程表单数据提交 */\r\n    submitForm() {\r\n      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况\r\n      const params = {taskId: this.taskForm.taskId}\r\n      getNextFlowNode(params).then(res => {\r\n        this.$refs.vFormRef.getFormData().then(formData => {\r\n          Object.assign(this.taskForm.variables, formData);\r\n          this.taskForm.variables.formJson = this.formJson;\r\n          console.log(this.taskForm, \"流程审批提交表单数据1\")\r\n        }).catch(error => {\r\n          // this.$modal.msgError(error)\r\n        })\r\n        const data = res.data;\r\n        if (data) {\r\n          if (data.dataType === 'dynamic') {\r\n            if (data.type === 'assignee') { // 指定人员\r\n              this.checkSendUser = true;\r\n              this.checkType = \"single\";\r\n            } else if (data.type === 'candidateUsers') {  // 候选人员(多个)\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            } else if (data.type === 'candidateGroups') { // 指定组(所属角色接收任务)\r\n              this.checkSendRole = true;\r\n            } else { // 会签\r\n              // 流程设计指定的 elementVariable 作为会签人员列表\r\n              this.multiInstanceVars = data.vars;\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 动态绑定操作按钮的点击事件\r\n    handleButtonClick(method) {\r\n      this[method]();\r\n    },\r\n\r\n    /** 获取历史节点标题 */\r\n    getHistoryTitle(record) {\r\n      return `${record.taskName} - ${record.assigneeName || '未分配'} - ${record.finishTime || '处理中'}`;\r\n    },\r\n\r\n    /** 获取历史节点图标 */\r\n    getHistoryIcon(record) {\r\n      if (record.finishTime) {\r\n        return 'el-icon-check';\r\n      } else {\r\n        return 'el-icon-time';\r\n      }\r\n    },\r\n\r\n    /** 获取历史节点颜色 */\r\n    getHistoryColor(record) {\r\n      if (record.finishTime) {\r\n        return '#67C23A';\r\n      } else {\r\n        return '#E6A23C';\r\n      }\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(record) {\r\n      if (record.finishTime) {\r\n        return 'success';\r\n      } else {\r\n        return 'warning';\r\n      }\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(record) {\r\n      if (record.finishTime) {\r\n        return '已完成';\r\n      } else {\r\n        return '处理中';\r\n      }\r\n    },\r\n\r\n    /** 处理流程记录数据 */\r\n    processFlowRecords() {\r\n      // 默认展开最近的一个节点（如果有数据的话）\r\n      if (this.flowRecordList && this.flowRecordList.length > 0) {\r\n        // 默认展开第一个节点\r\n        this.activeHistoryNames = ['history-0'];\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.test-form {\r\n  margin: 15px auto;\r\n  width: 800px;\r\n  padding: 15px;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both\r\n}\r\n\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.my-label {\r\n  background: #E1F3D8;\r\n}\r\n\r\n/* 历史节点样式 */\r\n.history-collapse {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-collapse .el-collapse-item__header {\r\n  background-color: #F5F7FA;\r\n  border-bottom: 1px solid #EBEEF5;\r\n  padding: 0 20px;\r\n  height: 48px;\r\n  line-height: 48px;\r\n}\r\n\r\n.history-collapse .el-collapse-item__content {\r\n  padding: 20px;\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.history-title {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n}\r\n\r\n.history-title .node-name {\r\n  font-weight: 600;\r\n  margin-left: 8px;\r\n  margin-right: 15px;\r\n  color: #303133;\r\n}\r\n\r\n.history-title .assignee-name {\r\n  color: #606266;\r\n  margin-right: 15px;\r\n}\r\n\r\n.history-title .finish-time {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-left: auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.history-content {\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n}\r\n\r\n.comment-content {\r\n  background-color: #F8F9FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  font-style: italic;\r\n  color: #606266;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 16px;\r\n}\r\n\r\n.current-form-card {\r\n  border: 2px solid #409EFF;\r\n}\r\n\r\n.current-form-header {\r\n  color: #409EFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.current-form-header i {\r\n  margin-right: 8px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAkMA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AASA,IAAAM,OAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,eAAA;IACAC,QAAA,EAAAA,aAAA;IACAC,QAAA,EAAAA;EACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACA;MACAC,QAAA;MACAC,UAAA;MACA;MACAC,OAAA;MACAC,cAAA;MAAA;MACAC,KAAA;MAAA;MACAC,QAAA;QACAC,cAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,eAAA;QAAA;QACAC,OAAA;QAAA;QACAC,SAAA;QAAA;QACAC,UAAA;QAAA;QACAC,QAAA;QAAA;QACAC,MAAA;QAAA;QACAC,SAAA;QAAA;QACAC,SAAA;QACAC,SAAA;MACA;MACAC,cAAA;MAAA;MACAC,aAAA;MACAC,YAAA;MACAC,WAAA;MACAC,UAAA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,aAAA;MAAA;MACAC,SAAA;MAAA;MACAC,QAAA;MAAA;MACAC,SAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,QAAA;MACAC,kBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,OAAA,EAAAC,IAAA,CAAAC,GAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,SAAAC,MAAA,CAAAC,KAAA;MACA,KAAAX,QAAA,QAAAU,MAAA,CAAAC,KAAA,CAAAX,QAAA;MACA,KAAAC,SAAA,QAAAS,MAAA,CAAAC,KAAA,CAAAV,SAAA;MACA,KAAAvB,QAAA,CAAAO,QAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAA1B,QAAA;MACA,KAAAP,QAAA,CAAAQ,MAAA,QAAAwB,MAAA,CAAAC,KAAA,CAAAzB,MAAA;MACA,KAAAR,QAAA,CAAAK,SAAA,QAAA2B,MAAA,CAAAC,KAAA,CAAA5B,SAAA;MACA,KAAAL,QAAA,CAAAkC,WAAA,QAAAF,MAAA,CAAAC,KAAA,CAAAC,WAAA;MACA,KAAAlC,QAAA,CAAAM,UAAA,QAAA0B,MAAA,CAAAC,KAAA,CAAA5B,SAAA;MACA;MACA,SAAAL,QAAA,CAAAQ,MAAA;QACA,KAAA2B,eAAA,MAAAnC,QAAA,CAAAQ,MAAA;MACA;MACA,KAAA4B,iBAAA,MAAApC,QAAA,CAAAK,SAAA,OAAAL,QAAA,CAAAO,QAAA;IACA;EACA;EACA8B,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MAAA,IAAAC,KAAA;MACA,IAAAF,GAAA,CAAApD,IAAA;QACA,IAAAuD,0BAAA;UAAArC,SAAA,OAAAL,QAAA,CAAAK,SAAA;UAAAE,QAAA,OAAAP,QAAA,CAAAO;QAAA,GAAAoC,IAAA,WAAAC,GAAA;UACAH,KAAA,CAAA9C,QAAA,GAAAiD,GAAA,CAAAnD,IAAA;QACA;MACA;IACA;IACAoD,OAAA,WAAAA,QAAAC,GAAA;MACA,IAAAA,GAAA;QACA;MACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAAD,GAAA;MACA,IAAAA,GAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACAE,gBAAA,WAAAA,iBAAAC,SAAA;MACA,IAAAA,SAAA;QACA,IAAAA,SAAA,YAAAC,KAAA;UACA,IAAAC,SAAA,GAAAF,SAAA,CAAAG,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,MAAA,CAAAC,QAAA;UAAA;UACA,SAAA/B,iBAAA;YACA,KAAAgC,IAAA,MAAAxD,QAAA,CAAAW,SAAA,OAAAa,iBAAA,EAAA2B,SAAA;UACA;YACA,KAAAK,IAAA,MAAAxD,QAAA,CAAAW,SAAA,cAAAwC,SAAA,CAAAM,IAAA;UACA;QACA;UACA,KAAAD,IAAA,MAAAxD,QAAA,CAAAW,SAAA,cAAAsC,SAAA,CAAAK,MAAA,CAAAC,QAAA;QACA;MACA;IACA;IACA;IACAG,gBAAA,WAAAA,iBAAAT,SAAA,EAAAU,QAAA;MACA,IAAAV,SAAA;QACA,IAAAA,SAAA,YAAAC,KAAA;UACA,IAAAC,SAAA,GAAAF,SAAA,CAAAG,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAO,MAAA,CAAAL,QAAA;UAAA;UACA,KAAAC,IAAA,MAAAxD,QAAA,CAAAW,SAAA,cAAAwC,SAAA,CAAAM,IAAA;QACA;UACA,KAAAD,IAAA,MAAAxD,QAAA,CAAAW,SAAA,cAAAsC,SAAA;QACA;MACA;IACA;IACA,aACAb,iBAAA,WAAAA,kBAAA/B,SAAA,EAAAE,QAAA;MAAA,IAAAsD,MAAA;MACA,IAAAC,IAAA;MACA,IAAAC,MAAA;QAAA1D,SAAA,EAAAA,SAAA;QAAAE,QAAA,EAAAA;MAAA;MACA,IAAAyD,oBAAA,EAAAD,MAAA,EAAApB,IAAA,WAAAC,GAAA;QACAkB,IAAA,CAAAhE,cAAA,GAAA8C,GAAA,CAAAnD,IAAA,CAAAwE,QAAA;QACA;QACAH,IAAA,CAAAI,kBAAA;MACA,GAAAC,KAAA,WAAAvB,GAAA;QACAiB,MAAA,CAAAO,MAAA;MACA;IACA;IACA,aACAjC,eAAA,WAAAA,gBAAA3B,MAAA;MAAA,IAAA6D,MAAA;MACA,IAAA7D,MAAA;QACA;QACA,IAAA8D,kBAAA;UAAA9D,MAAA,EAAAA;QAAA,GAAAmC,IAAA,WAAAC,GAAA;UACA;UACAyB,MAAA,CAAAzC,OAAA,GAAAC,IAAA,CAAAC,GAAA;UAEAuC,MAAA,CAAAE,SAAA;YACA;YACAF,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAC,WAAA,CAAA9B,GAAA,CAAAnD,IAAA,CAAAgC,QAAA;YACA4C,MAAA,CAAA5C,QAAA,GAAAmB,GAAA,CAAAnD,IAAA,CAAAgC,QAAA;YACA4C,MAAA,CAAAE,SAAA;cACA;cACAF,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAE,WAAA,CAAA/B,GAAA,CAAAnD,IAAA;cACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA,WACAmF,cAAA,WAAAA,eAAA;MACA,KAAA5E,QAAA,CAAAE,gBAAA;MACA,KAAAF,QAAA,CAAAG,eAAA;IACA;IACA0E,YAAA,WAAAA,aAAA,GAEA;IACA,WACAT,MAAA,WAAAA,OAAA;MACA;MACA,IAAAU,GAAA;QAAAC,IAAA;QAAA9C,KAAA;UAAA+C,CAAA,EAAAnD,IAAA,CAAAC,GAAA;QAAA;MAAA;MACA,KAAAmD,IAAA,CAAAC,aAAA,CAAAJ,GAAA;IACA;IACA,WACAK,YAAA,WAAAA,aAAA;MACA,KAAAlE,UAAA;MACA,KAAAC,WAAA;IACA;IACA,WACAkE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAb,KAAA,aAAAc,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,gBAAA,EAAAH,MAAA,CAAArF,QAAA,EAAA2C,IAAA,WAAAC,GAAA;YACAyC,MAAA,CAAAI,MAAA,CAAAC,UAAA,CAAA9C,GAAA,CAAA+C,GAAA;YACAN,MAAA,CAAAjB,MAAA;UACA;QACA;MACA;IACA;IACA,cACAwB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAA7E,UAAA;MACA,KAAAD,WAAA;MACA,IAAA+E,gBAAA,OAAA9F,QAAA,EAAA2C,IAAA,WAAAC,GAAA;QACAiD,MAAA,CAAAjF,cAAA,GAAAgC,GAAA,CAAAnD,IAAA;MACA;IACA;IACA,aACAsG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAxB,KAAA,aAAAc,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAU,gBAAA,EAAAD,MAAA,CAAAhG,QAAA,EAAA2C,IAAA,WAAAC,GAAA;YACAoD,MAAA,CAAAP,MAAA,CAAAC,UAAA,CAAA9C,GAAA,CAAA+C,GAAA;YACAK,MAAA,CAAA5B,MAAA;UACA;QACA;MACA;IACA;IACA,eACA8B,UAAA,WAAAA,WAAA;MACA,KAAAlG,QAAA,CAAAC,cAAA;MACA,KAAAD,QAAA,CAAAG,eAAA;MACA,KAAAS,cAAA;IACA;IACA,WACAuF,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,KAAA5B,KAAA,aAAAc,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAc,cAAA,EAAAD,MAAA,CAAApG,QAAA,EAAA2C,IAAA,WAAA2D,QAAA;YACAF,MAAA,CAAAX,MAAA,CAAAC,UAAA,CAAAY,QAAA,CAAAX,GAAA;YACAS,MAAA,CAAAhC,MAAA;UACA;QACA;MACA;IACA;IACA,eACAmC,kBAAA,WAAAA,mBAAA;MACA,KAAAvG,QAAA,CAAAE,gBAAA;MACA,KAAAF,QAAA,CAAAG,eAAA;MACA,KAAAS,cAAA;IACA;IACA,eACA4F,cAAA,WAAAA,eAAA;MACA,KAAA1F,YAAA;MACA,KAAAD,aAAA;MACA,KAAA4F,UAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,UAAA3G,QAAA,CAAAW,SAAA,SAAAQ,aAAA;QACA,KAAAsE,MAAA,CAAAmB,QAAA;QACA;MACA;MACA,UAAA5G,QAAA,CAAAW,SAAA,SAAAS,aAAA;QACA,KAAAqE,MAAA,CAAAmB,QAAA;QACA;MACA;MACA,UAAA5G,QAAA,CAAAI,OAAA;QACA,KAAAqF,MAAA,CAAAmB,QAAA;QACA;MACA;MACA,SAAA5G,QAAA;QACA,IAAA6G,cAAA,OAAA7G,QAAA,EAAA2C,IAAA,WAAA2D,QAAA;UACAK,MAAA,CAAAlB,MAAA,CAAAC,UAAA,CAAAY,QAAA,CAAAX,GAAA;UACAgB,MAAA,CAAAvC,MAAA;QACA;MACA;QACA,IAAAyC,cAAA,OAAA7G,QAAA,EAAA2C,IAAA,WAAA2D,QAAA;UACAK,MAAA,CAAAlB,MAAA,CAAAC,UAAA,CAAAY,QAAA,CAAAX,GAAA;UACAgB,MAAA,CAAAvC,MAAA;QACA;MACA;IACA;IACA,iBACAqC,UAAA,WAAAA,WAAA;MAAA,IAAAK,MAAA;MACA;MACA,IAAA/C,MAAA;QAAAvD,MAAA,OAAAR,QAAA,CAAAQ;MAAA;MACA,IAAAuG,qBAAA,EAAAhD,MAAA,EAAApB,IAAA,WAAAC,GAAA;QACAkE,MAAA,CAAAtC,KAAA,CAAAC,QAAA,CAAAuC,WAAA,GAAArE,IAAA,WAAAsE,QAAA;UACAC,MAAA,CAAAC,MAAA,CAAAL,MAAA,CAAA9G,QAAA,CAAAW,SAAA,EAAAsG,QAAA;UACAH,MAAA,CAAA9G,QAAA,CAAAW,SAAA,CAAAc,QAAA,GAAAqF,MAAA,CAAArF,QAAA;UACA2F,OAAA,CAAAC,GAAA,CAAAP,MAAA,CAAA9G,QAAA;QACA,GAAAmE,KAAA,WAAAmD,KAAA;UACA;QAAA,CACA;QACA,IAAA7H,IAAA,GAAAmD,GAAA,CAAAnD,IAAA;QACA,IAAAA,IAAA;UACA,IAAAA,IAAA,CAAA8H,QAAA;YACA,IAAA9H,IAAA,CAAA+H,IAAA;cAAA;cACAV,MAAA,CAAA3F,aAAA;cACA2F,MAAA,CAAAzF,SAAA;YACA,WAAA5B,IAAA,CAAA+H,IAAA;cAAA;cACAV,MAAA,CAAA3F,aAAA;cACA2F,MAAA,CAAAzF,SAAA;YACA,WAAA5B,IAAA,CAAA+H,IAAA;cAAA;cACAV,MAAA,CAAA1F,aAAA;YACA;cAAA;cACA;cACA0F,MAAA,CAAAtF,iBAAA,GAAA/B,IAAA,CAAAgI,IAAA;cACAX,MAAA,CAAA3F,aAAA;cACA2F,MAAA,CAAAzF,SAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAqG,iBAAA,WAAAA,kBAAAC,MAAA;MACA,KAAAA,MAAA;IACA;IAEA,eACAC,eAAA,WAAAA,gBAAAC,MAAA;MACA,UAAAC,MAAA,CAAAD,MAAA,CAAAvG,QAAA,SAAAwG,MAAA,CAAAD,MAAA,CAAAE,YAAA,kBAAAD,MAAA,CAAAD,MAAA,CAAAG,UAAA;IACA;IAEA,eACAC,cAAA,WAAAA,eAAAJ,MAAA;MACA,IAAAA,MAAA,CAAAG,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAE,eAAA,WAAAA,gBAAAL,MAAA;MACA,IAAAA,MAAA,CAAAG,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAG,gBAAA,WAAAA,iBAAAN,MAAA;MACA,IAAAA,MAAA,CAAAG,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,aACAI,aAAA,WAAAA,cAAAP,MAAA;MACA,IAAAA,MAAA,CAAAG,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACA9D,kBAAA,WAAAA,mBAAA;MACA;MACA,SAAApE,cAAA,SAAAA,cAAA,CAAAuI,MAAA;QACA;QACA,KAAA3G,kBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}