{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\flowable\\task\\todo\\detail\\index.vue", "mtime": 1752407558498}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_finished", "require", "_User", "_interopRequireDefault", "_Role", "_definition", "_todo", "_viewer", "name", "components", "BpmnViewer", "FlowUser", "FlowRole", "props", "data", "eventName", "flowData", "activeName", "loading", "flowRecordList", "rules", "taskForm", "returnTaskShow", "delegateTaskShow", "defaultTaskShow", "comment", "procInsId", "instanceId", "deployId", "taskId", "procDefId", "<PERSON><PERSON><PERSON>", "variables", "returnTaskList", "completeTitle", "completeOpen", "returnTitle", "returnOpen", "rejectOpen", "rejectTitle", "checkSendUser", "checkSendRole", "checkType", "taskName", "startUser", "multiInstanceVars", "formJson", "activeHistoryNames", "completedFlowRecords", "formKey", "Date", "now", "created", "$route", "query", "executionId", "getFlowTaskForm", "getFlowRecordList", "methods", "handleClick", "tab", "_this", "flowXmlAndNode", "then", "res", "handleUserSelect", "selection", "Array", "selectVal", "map", "item", "userId", "toString", "$set", "join", "handleRoleSelect", "roleId", "_this2", "that", "params", "flowRecord", "flowList", "processFlowRecords", "catch", "goBack", "_this3", "flowTaskForm", "$nextTick", "$refs", "vFormRef", "set<PERSON><PERSON><PERSON><PERSON>", "setFormData", "handleDelegate", "handleAssign", "obj", "path", "t", "$tab", "closeOpenPage", "handleReject", "taskReject", "_this4", "validate", "valid", "rejectTask", "$modal", "msgSuccess", "msg", "handleReturn", "_this5", "returnList", "taskReturn", "_this6", "returnTask", "cancelTask", "submitDeleteTask", "_this7", "delegate", "response", "cancelDelegateTask", "handleComplete", "submitForm", "taskComplete", "_this8", "msgError", "complete", "_this9", "getNextFlowNode", "getFormData", "formData", "Object", "assign", "console", "log", "dataType", "type", "vars", "handleButtonClick", "method", "getHistoryTitle", "record", "concat", "assignee<PERSON>ame", "finishTime", "getHistoryIcon", "getHistoryColor", "getStatusTagType", "getStatusText", "length"], "sources": ["src/views/flowable/task/todo/detail/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span class=\"el-icon-document\">待办任务</span>\r\n        <el-tag style=\"margin-left:10px\">发起人:{{ startUser }}</el-tag>\r\n        <el-tag>任务节点:{{ taskName }}</el-tag>\r\n        <el-button style=\"float: right;\" size=\"mini\" type=\"danger\" @click=\"goBack\">关闭</el-button>\r\n      </div>\r\n      <el-tabs tab-position=\"top\" v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <!--表单信息-->\r\n        <el-tab-pane label=\"表单信息\" name=\"1\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <div v-if=\"flowRecordList && flowRecordList.length > 0\" style=\"margin-bottom: 20px;\">\r\n              <h4 style=\"margin-bottom: 15px; color: #606266;\">\r\n                <i class=\"el-icon-time\"></i> 流程历史记录\r\n              </h4>\r\n              <el-collapse v-model=\"activeHistoryNames\" class=\"history-collapse\">\r\n                <el-collapse-item\r\n                  v-for=\"(record, index) in flowRecordList\"\r\n                  :key=\"`history-${index}-${record.taskId || record.id || index}`\"\r\n                  :name=\"`history-${index}`\"\r\n                >\r\n                  <template slot=\"title\">\r\n                    <div class=\"history-title\">\r\n                      <i :class=\"getHistoryIcon(record)\" :style=\"{ color: getHistoryColor(record) }\"></i>\r\n                      <span class=\"node-name\">{{ record.taskName || '未知节点' }}</span>\r\n                      <span class=\"assignee-name\">{{ record.assigneeName || '未分配' }}</span>\r\n                      <span class=\"finish-time\">{{ record.finishTime || '处理中' }}</span>\r\n                      <el-tag\r\n                        :type=\"getStatusTagType(record)\"\r\n                        size=\"mini\"\r\n                        style=\"margin-left: 10px;\"\r\n                      >\r\n                        {{ getStatusText(record) }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </template>\r\n\r\n                  <div class=\"history-content\">\r\n                    <el-descriptions :column=\"2\" size=\"small\" border>\r\n                      <el-descriptions-item v-if=\"record.assigneeName\" label=\"办理人\">\r\n                        <span>{{ record.assigneeName }}</span>\r\n                        <el-tag v-if=\"record.deptName\" type=\"info\" size=\"mini\" style=\"margin-left: 8px;\">{{ record.deptName }}</el-tag>\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.candidate\" label=\"候选办理\">\r\n                        {{ record.candidate }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.createTime\" label=\"接收时间\">\r\n                        {{ record.createTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.finishTime\" label=\"处理时间\">\r\n                        {{ record.finishTime }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.duration\" label=\"处理耗时\">\r\n                        {{ record.duration }}\r\n                      </el-descriptions-item>\r\n                      <el-descriptions-item v-if=\"record.comment && record.comment.comment\" label=\"处理意见\" :span=\"2\">\r\n                        <div class=\"comment-content\">\r\n                          {{ record.comment.comment }}\r\n                        </div>\r\n                      </el-descriptions-item>\r\n                    </el-descriptions>\r\n                  </div>\r\n                </el-collapse-item>\r\n              </el-collapse>\r\n            </div>\r\n\r\n            <!-- 当前表单 -->\r\n            <el-card class=\"current-form-card\" shadow=\"hover\">\r\n              <div slot=\"header\" class=\"current-form-header\">\r\n                <i class=\"el-icon-edit-outline\"></i>\r\n                <span>当前待处理表单</span>\r\n              </div>\r\n              <v-form-render ref=\"vFormRef\" :key=\"formKey\"/>\r\n            </el-card>\r\n\r\n            <div style=\"margin-left:10%;margin-bottom: 20px;font-size: 14px;margin-top: 20px;\">\r\n              <el-button type=\"primary\" @click=\"handleComplete\">审 批</el-button>\r\n            </div>\r\n          </el-col>\r\n        </el-tab-pane>\r\n\r\n        <!--流程流转记录-->\r\n        <el-tab-pane label=\"流转记录\" name=\"2\">\r\n          <el-col :span=\"16\" :offset=\"4\">\r\n            <!-- 历史节点信息 -->\r\n            <flow-history :flow-record-list=\"flowRecordList\" />\r\n          </el-col>\r\n        </el-tab-pane>\r\n        <!--流程图-->\r\n        <el-tab-pane label=\"流程图\" name=\"3\">\r\n          <bpmn-viewer :flowData=\"flowData\" :procInsId=\"taskForm.procInsId\"/>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n      <!--审批任务-->\r\n      <el-dialog :title=\"completeTitle\" :visible.sync=\"completeOpen\" width=\"60%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\">\r\n          <el-form-item prop=\"targetKey\">\r\n            <flow-user v-if=\"checkSendUser\" :checkType=\"checkType\" @handleUserSelect=\"handleUserSelect\"></flow-user>\r\n            <flow-role v-if=\"checkSendRole\" @handleRoleSelect=\"handleRoleSelect\"></flow-role>\r\n          </el-form-item>\r\n          <el-form-item label=\"处理意见\" label-width=\"80px\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入处理意见', trigger: 'blur' }]\">\r\n            <el-input type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入处理意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"completeOpen = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"taskComplete\">确 定</el-button>\r\n        </span>\r\n      </el-dialog>\r\n      <!--退回流程-->\r\n      <el-dialog :title=\"returnTitle\" :visible.sync=\"returnOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"退回节点\" prop=\"targetKey\">\r\n            <el-radio-group v-model=\"taskForm.targetKey\">\r\n              <el-radio-button\r\n                v-for=\"item in returnTaskList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.id\"\r\n              >{{ item.name }}\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"退回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"returnOpen = false\">取 消</el-button>\r\n              <el-button type=\"primary\" @click=\"taskReturn\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n      <!--驳回流程-->\r\n      <el-dialog :title=\"rejectTitle\" :visible.sync=\"rejectOpen\" width=\"40%\" append-to-body>\r\n        <el-form ref=\"taskForm\" :model=\"taskForm\" label-width=\"80px\">\r\n          <el-form-item label=\"驳回意见\" prop=\"comment\"\r\n                        :rules=\"[{ required: true, message: '请输入意见', trigger: 'blur' }]\">\r\n            <el-input style=\"width: 50%\" type=\"textarea\" v-model=\"taskForm.comment\" placeholder=\"请输入意见\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <span slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"rejectOpen = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"taskReject\">确 定</el-button>\r\n          </span>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {flowRecord} from \"@/api/flowable/finished\";\r\nimport FlowUser from '@/components/flow/User'\r\nimport FlowRole from '@/components/flow/Role'\r\nimport {flowXmlAndNode} from \"@/api/flowable/definition\";\r\nimport {\r\n  complete,\r\n  rejectTask,\r\n  returnList,\r\n  returnTask,\r\n  getNextFlowNode,\r\n  delegate,\r\n  flowTaskForm\r\n} from \"@/api/flowable/todo\";\r\nimport BpmnViewer from '@/components/Process/viewer';\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Record\",\r\n  components: {\r\n    BpmnViewer,\r\n    FlowUser,\r\n    FlowRole,\r\n  },\r\n  props: {},\r\n  data() {\r\n    return {\r\n      eventName: \"click\",\r\n      // 流程数据\r\n      flowData: {},\r\n      activeName: '1',\r\n      // 遮罩层\r\n      loading: true,\r\n      flowRecordList: [], // 流程流转数据\r\n      rules: {}, // 表单校验\r\n      taskForm: {\r\n        returnTaskShow: false, // 是否展示回退表单\r\n        delegateTaskShow: false, // 是否展示回退表单\r\n        defaultTaskShow: true, // 默认处理\r\n        comment: \"\", // 意见内容\r\n        procInsId: \"\", // 流程实例编号\r\n        instanceId: \"\", // 流程实例编号\r\n        deployId: \"\",  // 流程定义编号\r\n        taskId: \"\",// 流程任务编号\r\n        procDefId: \"\",  // 流程编号\r\n        targetKey: \"\",\r\n        variables: {},\r\n      },\r\n      returnTaskList: [],  // 回退列表数据\r\n      completeTitle: null,\r\n      completeOpen: false,\r\n      returnTitle: null,\r\n      returnOpen: false,\r\n      rejectOpen: false,\r\n      rejectTitle: null,\r\n      checkSendUser: false, // 是否展示人员选择模块\r\n      checkSendRole: false,// 是否展示角色选择模块\r\n      checkType: 'single', // 选择类型\r\n      taskName: null, // 任务节点\r\n      startUser: null, // 发起人信息,\r\n      multiInstanceVars: '', // 会签节点\r\n      formJson:{},\r\n      activeHistoryNames: [], // 展开的历史节点\r\n      completedFlowRecords: [], // 已完成的流程记录\r\n      formKey: Date.now() // VForm组件的唯一key\r\n    };\r\n  },\r\n  created() {\r\n    if (this.$route.query) {\r\n      this.taskName = this.$route.query.taskName;\r\n      this.startUser = this.$route.query.startUser;\r\n      this.taskForm.deployId = this.$route.query.deployId;\r\n      this.taskForm.taskId = this.$route.query.taskId;\r\n      this.taskForm.procInsId = this.$route.query.procInsId;\r\n      this.taskForm.executionId = this.$route.query.executionId;\r\n      this.taskForm.instanceId = this.$route.query.procInsId;\r\n      // 流程任务获取变量信息\r\n      if (this.taskForm.taskId) {\r\n        this.getFlowTaskForm(this.taskForm.taskId);\r\n      }\r\n      this.getFlowRecordList(this.taskForm.procInsId, this.taskForm.deployId);\r\n    }\r\n  },\r\n  methods: {\r\n    handleClick(tab) {\r\n      if (tab.name === '3') {\r\n        flowXmlAndNode({procInsId: this.taskForm.procInsId, deployId: this.taskForm.deployId}).then(res => {\r\n          this.flowData = res.data;\r\n        })\r\n      }\r\n    },\r\n    // 用户信息选中数据\r\n    handleUserSelect(selection) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.userId.toString());\r\n          if (this.multiInstanceVars) {\r\n            this.$set(this.taskForm.variables, this.multiInstanceVars,  selectVal);\r\n          } else {\r\n            this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n          }\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection.userId.toString());\r\n        }\r\n      }\r\n    },\r\n    // 角色信息选中数据\r\n    handleRoleSelect(selection) {\r\n      if (selection) {\r\n        if (selection instanceof Array) {\r\n          const selectVal = selection.map(item => item.roleId.toString());\r\n          this.$set(this.taskForm.variables, \"approval\", selectVal.join(','));\r\n        } else {\r\n          this.$set(this.taskForm.variables, \"approval\", selection);\r\n        }\r\n      }\r\n    },\r\n    /** 流程流转记录 */\r\n    getFlowRecordList(procInsId, deployId) {\r\n      const that = this\r\n      const params = {procInsId: procInsId, deployId: deployId}\r\n      flowRecord(params).then(res => {\r\n        that.flowRecordList = res.data.flowList;\r\n        // 处理历史节点数据\r\n        that.processFlowRecords();\r\n      }).catch(() => {\r\n        this.goBack();\r\n      })\r\n    },\r\n    /** 流程节点表单 */\r\n    getFlowTaskForm(taskId) {\r\n      if (taskId) {\r\n        // 提交流程申请时填写的表单存入了流程变量中后续任务处理时需要展示\r\n        flowTaskForm({taskId: taskId}).then(res => {\r\n          // 更新formKey以强制重新渲染VForm组件，避免key冲突\r\n          this.formKey = Date.now();\r\n\r\n          this.$nextTick(() => {\r\n            // 回显表单\r\n            this.$refs.vFormRef.setFormJson(res.data.formJson);\r\n            this.formJson = res.data.formJson;\r\n            this.$nextTick(() => {\r\n              // 加载表单填写的数据\r\n              this.$refs.vFormRef.setFormData(res.data);\r\n              // this.$nextTick(() => {\r\n              //   // 表单禁用\r\n              //   this.$refs.vFormRef.disableForm();\r\n              // })\r\n            })\r\n          })\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 委派任务 */\r\n    handleDelegate() {\r\n      this.taskForm.delegateTaskShow = true;\r\n      this.taskForm.defaultTaskShow = false;\r\n    },\r\n    handleAssign() {\r\n\r\n    },\r\n    /** 返回页面 */\r\n    goBack() {\r\n      // 关闭当前标签页并返回上个页面\r\n      const obj = { path: \"/task/todo\", query: { t: Date.now()} };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n    /** 驳回任务 */\r\n    handleReject() {\r\n      this.rejectOpen = true;\r\n      this.rejectTitle = \"驳回流程\";\r\n    },\r\n    /** 驳回任务 */\r\n    taskReject() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          rejectTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 可退回任务列表 */\r\n    handleReturn() {\r\n      this.returnOpen = true;\r\n      this.returnTitle = \"退回流程\";\r\n      returnList(this.taskForm).then(res => {\r\n        this.returnTaskList = res.data;\r\n      })\r\n    },\r\n    /** 提交退回任务 */\r\n    taskReturn() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          returnTask(this.taskForm).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            this.goBack()\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelTask() {\r\n      this.taskForm.returnTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 委派任务 */\r\n    submitDeleteTask() {\r\n      this.$refs[\"taskForm\"].validate(valid => {\r\n        if (valid) {\r\n          delegate(this.taskForm).then(response => {\r\n            this.$modal.msgSuccess(response.msg);\r\n            this.goBack();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 取消回退任务按钮 */\r\n    cancelDelegateTask() {\r\n      this.taskForm.delegateTaskShow = false;\r\n      this.taskForm.defaultTaskShow = true;\r\n      this.returnTaskList = [];\r\n    },\r\n    /** 加载审批任务弹框 */\r\n    handleComplete() {\r\n      this.completeOpen = true;\r\n      this.completeTitle = \"流程审批\";\r\n      this.submitForm();\r\n    },\r\n    /** 用户审批任务 */\r\n    taskComplete() {\r\n      if (!this.taskForm.variables && this.checkSendUser) {\r\n        this.$modal.msgError(\"请选择流程接收人员!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.variables && this.checkSendRole) {\r\n        this.$modal.msgError(\"请选择流程接收角色组!\");\r\n        return;\r\n      }\r\n      if (!this.taskForm.comment) {\r\n        this.$modal.msgError(\"请输入审批意见!\");\r\n        return;\r\n      }\r\n      if (this.taskForm) {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      } else {\r\n        complete(this.taskForm).then(response => {\r\n          this.$modal.msgSuccess(response.msg);\r\n          this.goBack();\r\n        });\r\n      }\r\n    },\r\n    /** 申请流程表单数据提交 */\r\n    submitForm() {\r\n      // 根据当前任务或者流程设计配置的下一步节点 todo 暂时未涉及到考虑网关、表达式和多节点情况\r\n      const params = {taskId: this.taskForm.taskId}\r\n      getNextFlowNode(params).then(res => {\r\n        this.$refs.vFormRef.getFormData().then(formData => {\r\n          Object.assign(this.taskForm.variables, formData);\r\n          this.taskForm.variables.formJson = this.formJson;\r\n          console.log(this.taskForm, \"流程审批提交表单数据1\")\r\n        }).catch(() => {\r\n          // this.$modal.msgError(error)\r\n        })\r\n        const data = res.data;\r\n        if (data) {\r\n          if (data.dataType === 'dynamic') {\r\n            if (data.type === 'assignee') { // 指定人员\r\n              this.checkSendUser = true;\r\n              this.checkType = \"single\";\r\n            } else if (data.type === 'candidateUsers') {  // 候选人员(多个)\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            } else if (data.type === 'candidateGroups') { // 指定组(所属角色接收任务)\r\n              this.checkSendRole = true;\r\n            } else { // 会签\r\n              // 流程设计指定的 elementVariable 作为会签人员列表\r\n              this.multiInstanceVars = data.vars;\r\n              this.checkSendUser = true;\r\n              this.checkType = \"multiple\";\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 动态绑定操作按钮的点击事件\r\n    handleButtonClick(method) {\r\n      this[method]();\r\n    },\r\n\r\n    /** 获取历史节点标题 */\r\n    getHistoryTitle(record) {\r\n      return `${record.taskName} - ${record.assigneeName || '未分配'} - ${record.finishTime || '处理中'}`;\r\n    },\r\n\r\n    /** 获取历史节点图标 */\r\n    getHistoryIcon(record) {\r\n      if (record.finishTime) {\r\n        return 'el-icon-check';\r\n      } else {\r\n        return 'el-icon-time';\r\n      }\r\n    },\r\n\r\n    /** 获取历史节点颜色 */\r\n    getHistoryColor(record) {\r\n      if (record.finishTime) {\r\n        return '#67C23A';\r\n      } else {\r\n        return '#E6A23C';\r\n      }\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(record) {\r\n      if (record.finishTime) {\r\n        return 'success';\r\n      } else {\r\n        return 'warning';\r\n      }\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(record) {\r\n      if (record.finishTime) {\r\n        return '已完成';\r\n      } else {\r\n        return '处理中';\r\n      }\r\n    },\r\n\r\n    /** 处理流程记录数据 */\r\n    processFlowRecords() {\r\n      // 默认展开最近的一个节点（如果有数据的话）\r\n      if (this.flowRecordList && this.flowRecordList.length > 0) {\r\n        // 默认展开第一个节点\r\n        this.activeHistoryNames = ['history-0'];\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.test-form {\r\n  margin: 15px auto;\r\n  width: 800px;\r\n  padding: 15px;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  display: table;\r\n  content: \"\";\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both\r\n}\r\n\r\n.box-card {\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.my-label {\r\n  background: #E1F3D8;\r\n}\r\n\r\n/* 历史节点样式 */\r\n.history-collapse {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-collapse .el-collapse-item__header {\r\n  background-color: #F5F7FA;\r\n  border-bottom: 1px solid #EBEEF5;\r\n  padding: 0 20px;\r\n  height: 48px;\r\n  line-height: 48px;\r\n}\r\n\r\n.history-collapse .el-collapse-item__content {\r\n  padding: 20px;\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.history-title {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n}\r\n\r\n.history-title .node-name {\r\n  font-weight: 600;\r\n  margin-left: 8px;\r\n  margin-right: 15px;\r\n  color: #303133;\r\n}\r\n\r\n.history-title .assignee-name {\r\n  color: #606266;\r\n  margin-right: 15px;\r\n}\r\n\r\n.history-title .finish-time {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  margin-left: auto;\r\n  margin-right: 10px;\r\n}\r\n\r\n.history-content {\r\n  background-color: white;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n}\r\n\r\n.comment-content {\r\n  background-color: #F8F9FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  font-style: italic;\r\n  color: #606266;\r\n}\r\n\r\n.form-data-section {\r\n  margin-top: 16px;\r\n}\r\n\r\n.current-form-card {\r\n  border: 2px solid #409EFF;\r\n}\r\n\r\n.current-form-header {\r\n  color: #409EFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.current-form-header i {\r\n  margin-right: 8px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AA0JA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AASA,IAAAM,OAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,eAAA;IACAC,QAAA,EAAAA,aAAA;IACAC,QAAA,EAAAA;EACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACA;MACAC,QAAA;MACAC,UAAA;MACA;MACAC,OAAA;MACAC,cAAA;MAAA;MACAC,KAAA;MAAA;MACAC,QAAA;QACAC,cAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,eAAA;QAAA;QACAC,OAAA;QAAA;QACAC,SAAA;QAAA;QACAC,UAAA;QAAA;QACAC,QAAA;QAAA;QACAC,MAAA;QAAA;QACAC,SAAA;QAAA;QACAC,SAAA;QACAC,SAAA;MACA;MACAC,cAAA;MAAA;MACAC,aAAA;MACAC,YAAA;MACAC,WAAA;MACAC,UAAA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,aAAA;MAAA;MACAC,SAAA;MAAA;MACAC,QAAA;MAAA;MACAC,SAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,QAAA;MACAC,kBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,OAAA,EAAAC,IAAA,CAAAC,GAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,SAAAC,MAAA,CAAAC,KAAA;MACA,KAAAX,QAAA,QAAAU,MAAA,CAAAC,KAAA,CAAAX,QAAA;MACA,KAAAC,SAAA,QAAAS,MAAA,CAAAC,KAAA,CAAAV,SAAA;MACA,KAAAvB,QAAA,CAAAO,QAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAA1B,QAAA;MACA,KAAAP,QAAA,CAAAQ,MAAA,QAAAwB,MAAA,CAAAC,KAAA,CAAAzB,MAAA;MACA,KAAAR,QAAA,CAAAK,SAAA,QAAA2B,MAAA,CAAAC,KAAA,CAAA5B,SAAA;MACA,KAAAL,QAAA,CAAAkC,WAAA,QAAAF,MAAA,CAAAC,KAAA,CAAAC,WAAA;MACA,KAAAlC,QAAA,CAAAM,UAAA,QAAA0B,MAAA,CAAAC,KAAA,CAAA5B,SAAA;MACA;MACA,SAAAL,QAAA,CAAAQ,MAAA;QACA,KAAA2B,eAAA,MAAAnC,QAAA,CAAAQ,MAAA;MACA;MACA,KAAA4B,iBAAA,MAAApC,QAAA,CAAAK,SAAA,OAAAL,QAAA,CAAAO,QAAA;IACA;EACA;EACA8B,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,GAAA,CAAApD,IAAA;QACA,IAAAsD,0BAAA;UAAApC,SAAA,OAAAL,QAAA,CAAAK,SAAA;UAAAE,QAAA,OAAAP,QAAA,CAAAO;QAAA,GAAAmC,IAAA,WAAAC,GAAA;UACAH,KAAA,CAAA7C,QAAA,GAAAgD,GAAA,CAAAlD,IAAA;QACA;MACA;IACA;IACA;IACAmD,gBAAA,WAAAA,iBAAAC,SAAA;MACA,IAAAA,SAAA;QACA,IAAAA,SAAA,YAAAC,KAAA;UACA,IAAAC,SAAA,GAAAF,SAAA,CAAAG,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,MAAA,CAAAC,QAAA;UAAA;UACA,SAAA3B,iBAAA;YACA,KAAA4B,IAAA,MAAApD,QAAA,CAAAW,SAAA,OAAAa,iBAAA,EAAAuB,SAAA;UACA;YACA,KAAAK,IAAA,MAAApD,QAAA,CAAAW,SAAA,cAAAoC,SAAA,CAAAM,IAAA;UACA;QACA;UACA,KAAAD,IAAA,MAAApD,QAAA,CAAAW,SAAA,cAAAkC,SAAA,CAAAK,MAAA,CAAAC,QAAA;QACA;MACA;IACA;IACA;IACAG,gBAAA,WAAAA,iBAAAT,SAAA;MACA,IAAAA,SAAA;QACA,IAAAA,SAAA,YAAAC,KAAA;UACA,IAAAC,SAAA,GAAAF,SAAA,CAAAG,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAM,MAAA,CAAAJ,QAAA;UAAA;UACA,KAAAC,IAAA,MAAApD,QAAA,CAAAW,SAAA,cAAAoC,SAAA,CAAAM,IAAA;QACA;UACA,KAAAD,IAAA,MAAApD,QAAA,CAAAW,SAAA,cAAAkC,SAAA;QACA;MACA;IACA;IACA,aACAT,iBAAA,WAAAA,kBAAA/B,SAAA,EAAAE,QAAA;MAAA,IAAAiD,MAAA;MACA,IAAAC,IAAA;MACA,IAAAC,MAAA;QAAArD,SAAA,EAAAA,SAAA;QAAAE,QAAA,EAAAA;MAAA;MACA,IAAAoD,oBAAA,EAAAD,MAAA,EAAAhB,IAAA,WAAAC,GAAA;QACAc,IAAA,CAAA3D,cAAA,GAAA6C,GAAA,CAAAlD,IAAA,CAAAmE,QAAA;QACA;QACAH,IAAA,CAAAI,kBAAA;MACA,GAAAC,KAAA;QACAN,MAAA,CAAAO,MAAA;MACA;IACA;IACA,aACA5B,eAAA,WAAAA,gBAAA3B,MAAA;MAAA,IAAAwD,MAAA;MACA,IAAAxD,MAAA;QACA;QACA,IAAAyD,kBAAA;UAAAzD,MAAA,EAAAA;QAAA,GAAAkC,IAAA,WAAAC,GAAA;UACA;UACAqB,MAAA,CAAApC,OAAA,GAAAC,IAAA,CAAAC,GAAA;UAEAkC,MAAA,CAAAE,SAAA;YACA;YACAF,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAC,WAAA,CAAA1B,GAAA,CAAAlD,IAAA,CAAAgC,QAAA;YACAuC,MAAA,CAAAvC,QAAA,GAAAkB,GAAA,CAAAlD,IAAA,CAAAgC,QAAA;YACAuC,MAAA,CAAAE,SAAA;cACA;cACAF,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAE,WAAA,CAAA3B,GAAA,CAAAlD,IAAA;cACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA,WACA8E,cAAA,WAAAA,eAAA;MACA,KAAAvE,QAAA,CAAAE,gBAAA;MACA,KAAAF,QAAA,CAAAG,eAAA;IACA;IACAqE,YAAA,WAAAA,aAAA,GAEA;IACA,WACAT,MAAA,WAAAA,OAAA;MACA;MACA,IAAAU,GAAA;QAAAC,IAAA;QAAAzC,KAAA;UAAA0C,CAAA,EAAA9C,IAAA,CAAAC,GAAA;QAAA;MAAA;MACA,KAAA8C,IAAA,CAAAC,aAAA,CAAAJ,GAAA;IACA;IACA,WACAK,YAAA,WAAAA,aAAA;MACA,KAAA7D,UAAA;MACA,KAAAC,WAAA;IACA;IACA,WACA6D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAb,KAAA,aAAAc,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,gBAAA,EAAAH,MAAA,CAAAhF,QAAA,EAAA0C,IAAA,WAAAC,GAAA;YACAqC,MAAA,CAAAI,MAAA,CAAAC,UAAA,CAAA1C,GAAA,CAAA2C,GAAA;YACAN,MAAA,CAAAjB,MAAA;UACA;QACA;MACA;IACA;IACA,cACAwB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAxE,UAAA;MACA,KAAAD,WAAA;MACA,IAAA0E,gBAAA,OAAAzF,QAAA,EAAA0C,IAAA,WAAAC,GAAA;QACA6C,MAAA,CAAA5E,cAAA,GAAA+B,GAAA,CAAAlD,IAAA;MACA;IACA;IACA,aACAiG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAxB,KAAA,aAAAc,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAU,gBAAA,EAAAD,MAAA,CAAA3F,QAAA,EAAA0C,IAAA,WAAAC,GAAA;YACAgD,MAAA,CAAAP,MAAA,CAAAC,UAAA,CAAA1C,GAAA,CAAA2C,GAAA;YACAK,MAAA,CAAA5B,MAAA;UACA;QACA;MACA;IACA;IACA,eACA8B,UAAA,WAAAA,WAAA;MACA,KAAA7F,QAAA,CAAAC,cAAA;MACA,KAAAD,QAAA,CAAAG,eAAA;MACA,KAAAS,cAAA;IACA;IACA,WACAkF,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,KAAA5B,KAAA,aAAAc,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAc,cAAA,EAAAD,MAAA,CAAA/F,QAAA,EAAA0C,IAAA,WAAAuD,QAAA;YACAF,MAAA,CAAAX,MAAA,CAAAC,UAAA,CAAAY,QAAA,CAAAX,GAAA;YACAS,MAAA,CAAAhC,MAAA;UACA;QACA;MACA;IACA;IACA,eACAmC,kBAAA,WAAAA,mBAAA;MACA,KAAAlG,QAAA,CAAAE,gBAAA;MACA,KAAAF,QAAA,CAAAG,eAAA;MACA,KAAAS,cAAA;IACA;IACA,eACAuF,cAAA,WAAAA,eAAA;MACA,KAAArF,YAAA;MACA,KAAAD,aAAA;MACA,KAAAuF,UAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,UAAAtG,QAAA,CAAAW,SAAA,SAAAQ,aAAA;QACA,KAAAiE,MAAA,CAAAmB,QAAA;QACA;MACA;MACA,UAAAvG,QAAA,CAAAW,SAAA,SAAAS,aAAA;QACA,KAAAgE,MAAA,CAAAmB,QAAA;QACA;MACA;MACA,UAAAvG,QAAA,CAAAI,OAAA;QACA,KAAAgF,MAAA,CAAAmB,QAAA;QACA;MACA;MACA,SAAAvG,QAAA;QACA,IAAAwG,cAAA,OAAAxG,QAAA,EAAA0C,IAAA,WAAAuD,QAAA;UACAK,MAAA,CAAAlB,MAAA,CAAAC,UAAA,CAAAY,QAAA,CAAAX,GAAA;UACAgB,MAAA,CAAAvC,MAAA;QACA;MACA;QACA,IAAAyC,cAAA,OAAAxG,QAAA,EAAA0C,IAAA,WAAAuD,QAAA;UACAK,MAAA,CAAAlB,MAAA,CAAAC,UAAA,CAAAY,QAAA,CAAAX,GAAA;UACAgB,MAAA,CAAAvC,MAAA;QACA;MACA;IACA;IACA,iBACAqC,UAAA,WAAAA,WAAA;MAAA,IAAAK,MAAA;MACA;MACA,IAAA/C,MAAA;QAAAlD,MAAA,OAAAR,QAAA,CAAAQ;MAAA;MACA,IAAAkG,qBAAA,EAAAhD,MAAA,EAAAhB,IAAA,WAAAC,GAAA;QACA8D,MAAA,CAAAtC,KAAA,CAAAC,QAAA,CAAAuC,WAAA,GAAAjE,IAAA,WAAAkE,QAAA;UACAC,MAAA,CAAAC,MAAA,CAAAL,MAAA,CAAAzG,QAAA,CAAAW,SAAA,EAAAiG,QAAA;UACAH,MAAA,CAAAzG,QAAA,CAAAW,SAAA,CAAAc,QAAA,GAAAgF,MAAA,CAAAhF,QAAA;UACAsF,OAAA,CAAAC,GAAA,CAAAP,MAAA,CAAAzG,QAAA;QACA,GAAA8D,KAAA;UACA;QAAA,CACA;QACA,IAAArE,IAAA,GAAAkD,GAAA,CAAAlD,IAAA;QACA,IAAAA,IAAA;UACA,IAAAA,IAAA,CAAAwH,QAAA;YACA,IAAAxH,IAAA,CAAAyH,IAAA;cAAA;cACAT,MAAA,CAAAtF,aAAA;cACAsF,MAAA,CAAApF,SAAA;YACA,WAAA5B,IAAA,CAAAyH,IAAA;cAAA;cACAT,MAAA,CAAAtF,aAAA;cACAsF,MAAA,CAAApF,SAAA;YACA,WAAA5B,IAAA,CAAAyH,IAAA;cAAA;cACAT,MAAA,CAAArF,aAAA;YACA;cAAA;cACA;cACAqF,MAAA,CAAAjF,iBAAA,GAAA/B,IAAA,CAAA0H,IAAA;cACAV,MAAA,CAAAtF,aAAA;cACAsF,MAAA,CAAApF,SAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA+F,iBAAA,WAAAA,kBAAAC,MAAA;MACA,KAAAA,MAAA;IACA;IAEA,eACAC,eAAA,WAAAA,gBAAAC,MAAA;MACA,UAAAC,MAAA,CAAAD,MAAA,CAAAjG,QAAA,SAAAkG,MAAA,CAAAD,MAAA,CAAAE,YAAA,kBAAAD,MAAA,CAAAD,MAAA,CAAAG,UAAA;IACA;IAEA,eACAC,cAAA,WAAAA,eAAAJ,MAAA;MACA,IAAAA,MAAA,CAAAG,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAE,eAAA,WAAAA,gBAAAL,MAAA;MACA,IAAAA,MAAA,CAAAG,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAG,gBAAA,WAAAA,iBAAAN,MAAA;MACA,IAAAA,MAAA,CAAAG,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,aACAI,aAAA,WAAAA,cAAAP,MAAA;MACA,IAAAA,MAAA,CAAAG,UAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACA7D,kBAAA,WAAAA,mBAAA;MACA;MACA,SAAA/D,cAAA,SAAAA,cAAA,CAAAiI,MAAA;QACA;QACA,KAAArG,kBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}