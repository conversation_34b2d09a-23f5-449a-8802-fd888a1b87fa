import request from '@/utils/request'

// 查询NPI流程定义列表（用于发起）
export function listNPIProcessDefinitions(query) {
  return request({
    url: '/npi/flow/definition/list',
    method: 'get',
    params: query
  })
}

// 查询NPI流程发起页面数据
export function listNPIProcessStart(query) {
  return request({
    url: '/npi/flow/start',
    method: 'get',
    params: query
  })
}

// 启动NPI流程
export function startNPIProcess(processDefinitionKey, variables) {
  return request({
    url: '/npi/flow/start/' + processDefinitionKey,
    method: 'post',
    data: variables,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取NPI流程图
export function getNPIProcessImage(processDefinitionId) {
  return request({
    url: '/npi/flow/image/' + processDefinitionId,
    method: 'get'
  })
}
