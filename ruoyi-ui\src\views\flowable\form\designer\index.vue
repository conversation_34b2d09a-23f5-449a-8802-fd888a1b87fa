<template>
  <div class="form-designer-page">
    <div class="page-header">
      <div class="header-content">
        <h2>
          <i class="el-icon-edit-outline"></i>
          流程表单设计器
        </h2>
        <p>为不同的流程节点设计专属的表单</p>
      </div>
    </div>

    <div class="page-content">
      <el-card>
        <div slot="header" class="card-header">
          <span>表单设计</span>
          <div class="header-actions">
            <el-button type="text" @click="showHelp">
              <i class="el-icon-question"></i>
              使用帮助
            </el-button>
          </div>
        </div>

        <node-form-manager 
          v-model="formConfig" 
          @save="handleSave"
        />
      </el-card>
    </div>

    <!-- 使用帮助对话框 -->
    <el-dialog title="使用帮助" :visible.sync="showHelpDialog" width="600px">
      <div class="help-content">
        <h4>🎯 功能介绍</h4>
        <p>这个表单设计器允许您为流程的不同节点创建专属的表单，每个节点可以有不同的字段和配置。</p>
        
        <h4>📋 使用步骤</h4>
        <ol>
          <li><strong>添加节点表单</strong>：点击"添加节点表单"创建新的节点</li>
          <li><strong>配置节点信息</strong>：设置节点名称和类型</li>
          <li><strong>设计表单字段</strong>：为每个节点添加所需的表单字段</li>
          <li><strong>预览和保存</strong>：预览表单效果并保存配置</li>
        </ol>
        
        <h4>🔧 支持的字段类型</h4>
        <ul>
          <li><strong>单行文本</strong>：适用于姓名、标题等短文本</li>
          <li><strong>多行文本</strong>：适用于描述、备注等长文本</li>
          <li><strong>数字</strong>：适用于金额、数量等数值</li>
          <li><strong>下拉选择</strong>：适用于部门、类型等固定选项</li>
          <li><strong>日期</strong>：适用于申请日期、截止日期等</li>
          <li><strong>开关</strong>：适用于是否同意等布尔值</li>
          <li><strong>单选框</strong>：适用于性别、优先级等单选</li>
          <li><strong>复选框</strong>：适用于技能、兴趣等多选</li>
        </ul>
        
        <h4>💡 最佳实践</h4>
        <ul>
          <li>为每个节点设置清晰的名称，便于识别</li>
          <li>合理选择字段类型，提升用户体验</li>
          <li>为选择类字段配置完整的选项</li>
          <li>定期导出配置文件作为备份</li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import NodeFormManager from '@/components/NodeFormManager'

export default {
  name: 'FormDesigner',
  components: {
    NodeFormManager
  },
  data() {
    return {
      formConfig: [],
      showHelpDialog: false
    }
  },
  created() {
    this.loadFormConfig();
  },
  methods: {
    /** 加载表单配置 */
    loadFormConfig() {
      // 这里可以从后端加载已保存的配置
      // 暂时使用本地存储
      const saved = localStorage.getItem('flowable_form_config');
      if (saved) {
        try {
          const config = JSON.parse(saved);
          this.formConfig = config.nodeForms || [];
        } catch (error) {
          console.warn('加载表单配置失败:', error);
        }
      }
    },

    /** 处理保存 */
    handleSave(config) {
      // 保存到本地存储
      localStorage.setItem('flowable_form_config', JSON.stringify(config));
      
      // 这里可以调用API保存到后端
      // saveFormConfig(config).then(() => {
      //   this.$message.success('保存成功');
      // }).catch(() => {
      //   this.$message.error('保存失败');
      // });
    },

    /** 显示帮助 */
    showHelp() {
      this.showHelpDialog = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.form-designer-page {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 20px;
  
  .header-content {
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.page-content {
  .el-card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  span {
    font-weight: 600;
    color: #303133;
  }
}

.help-content {
  h4 {
    color: #303133;
    margin: 16px 0 8px 0;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  p, li {
    color: #606266;
    line-height: 1.6;
  }
  
  ul, ol {
    padding-left: 20px;
  }
  
  li {
    margin-bottom: 4px;
  }
  
  strong {
    color: #303133;
  }
}
</style>
