import request from '@/utils/request'

// 查询NPI待办任务列表
export function listNPITodoTasks(query) {
  return request({
    url: '/npi/flow/todo/list',
    method: 'get',
    params: query
  })
}

// 查询NPI已办任务列表
export function listNPIFinishedTasks(query) {
  return request({
    url: '/npi/flow/finished/list',
    method: 'get',
    params: query
  })
}

// 查询NPI我的流程列表
export function listNPIMyProcesses(query) {
  return request({
    url: '/npi/flow/myProcess/list',
    method: 'get',
    params: query
  })
}

// 查询NPI流程定义列表
export function listNPIProcessDefinitions(query) {
  return request({
    url: '/npi/flow/definition/list',
    method: 'get',
    params: query
  })
}

// 完成NPI任务
export function completeNPITask(taskId, comment) {
  return request({
    url: '/npi/flow/complete/' + taskId,
    method: 'post',
    data: comment
  })
}

// 启动NPI流程
export function startNPIProcess(processDefinitionKey, variables) {
  return request({
    url: '/npi/flow/start/' + processDefinitionKey,
    method: 'post',
    data: variables
  })
}
