<template>
  <div class="node-form-manager">
    <div class="manager-header">
      <h3>
        <i class="el-icon-s-order"></i>
        流程节点表单管理
      </h3>
      <el-button type="primary" @click="addNodeForm">
        <i class="el-icon-plus"></i>
        添加节点表单
      </el-button>
    </div>

    <div class="node-forms-list">
      <el-collapse v-model="activeNodes" accordion>
        <el-collapse-item 
          v-for="(nodeForm, index) in nodeForms" 
          :key="nodeForm.id"
          :name="nodeForm.id"
        >
          <template slot="title">
            <div class="node-title">
              <i class="el-icon-document"></i>
              <span class="node-name">{{ nodeForm.nodeName }}</span>
              <el-tag :type="getNodeTypeTag(nodeForm.nodeType)" size="mini">
                {{ getNodeTypeText(nodeForm.nodeType) }}
              </el-tag>
              <span class="field-count">{{ nodeForm.fields.length }} 个字段</span>
            </div>
          </template>

          <div class="node-form-content">
            <div class="node-config">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="节点名称">
                    <el-input v-model="nodeForm.nodeName" placeholder="请输入节点名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="节点类型">
                    <el-select v-model="nodeForm.nodeType" style="width: 100%">
                      <el-option label="开始节点" value="start" />
                      <el-option label="用户任务" value="userTask" />
                      <el-option label="审批节点" value="approval" />
                      <el-option label="结束节点" value="end" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="操作">
                    <el-button type="danger" size="small" @click="removeNodeForm(index)">
                      删除节点
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div class="form-designer">
              <node-form 
                v-model="nodeForm.fields" 
                :title="`${nodeForm.nodeName} - 表单设计`"
              />
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>

      <div v-if="nodeForms.length === 0" class="empty-state">
        <i class="el-icon-document-add"></i>
        <p>暂无节点表单，点击"添加节点表单"开始创建</p>
      </div>
    </div>

    <div class="manager-footer">
      <el-button @click="previewForms">预览表单</el-button>
      <el-button type="primary" @click="saveForms">保存配置</el-button>
      <el-button @click="exportForms">导出配置</el-button>
      <el-button @click="importForms">导入配置</el-button>
    </div>

    <!-- 预览对话框 -->
    <el-dialog title="表单预览" :visible.sync="showPreview" width="80%">
      <div class="preview-content">
        <el-tabs v-model="previewActiveTab" type="card">
          <el-tab-pane 
            v-for="nodeForm in nodeForms" 
            :key="nodeForm.id"
            :label="nodeForm.nodeName"
            :name="nodeForm.id"
          >
            <node-form 
              :value="nodeForm.fields" 
              :title="nodeForm.nodeName"
              :readonly="true"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 导入配置 -->
    <input 
      ref="fileInput" 
      type="file" 
      accept=".json" 
      style="display: none" 
      @change="handleFileImport"
    />
  </div>
</template>

<script>
import NodeForm from '@/components/NodeForm'

export default {
  name: 'NodeFormManager',
  components: {
    NodeForm
  },
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      nodeForms: [],
      activeNodes: '',
      showPreview: false,
      previewActiveTab: ''
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.nodeForms = newVal || [];
      },
      immediate: true,
      deep: true
    },
    nodeForms: {
      handler(newVal) {
        this.$emit('input', newVal);
      },
      deep: true
    }
  },
  methods: {
    /** 添加节点表单 */
    addNodeForm() {
      const newNodeForm = {
        id: `node_${Date.now()}`,
        nodeName: `节点${this.nodeForms.length + 1}`,
        nodeType: 'userTask',
        fields: []
      };
      
      this.nodeForms.push(newNodeForm);
      this.activeNodes = newNodeForm.id;
    },

    /** 删除节点表单 */
    removeNodeForm(index) {
      this.$confirm('确定要删除这个节点表单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.nodeForms.splice(index, 1);
        this.$message.success('删除成功');
      }).catch(() => {});
    },

    /** 获取节点类型标签 */
    getNodeTypeTag(type) {
      const tagMap = {
        start: 'success',
        userTask: 'primary',
        approval: 'warning',
        end: 'info'
      };
      return tagMap[type] || 'primary';
    },

    /** 获取节点类型文本 */
    getNodeTypeText(type) {
      const textMap = {
        start: '开始节点',
        userTask: '用户任务',
        approval: '审批节点',
        end: '结束节点'
      };
      return textMap[type] || type;
    },

    /** 预览表单 */
    previewForms() {
      if (this.nodeForms.length === 0) {
        this.$message.warning('暂无表单可预览');
        return;
      }
      this.previewActiveTab = this.nodeForms[0].id;
      this.showPreview = true;
    },

    /** 保存配置 */
    saveForms() {
      // 这里可以调用API保存到后端
      const config = {
        nodeForms: this.nodeForms,
        createTime: new Date().toISOString(),
        version: '1.0'
      };
      
      console.log('保存表单配置:', config);
      this.$message.success('保存成功');
      
      // 触发保存事件
      this.$emit('save', config);
    },

    /** 导出配置 */
    exportForms() {
      const config = {
        nodeForms: this.nodeForms,
        createTime: new Date().toISOString(),
        version: '1.0'
      };
      
      const blob = new Blob([JSON.stringify(config, null, 2)], { 
        type: 'application/json' 
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `node-forms-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      this.$message.success('导出成功');
    },

    /** 导入配置 */
    importForms() {
      this.$refs.fileInput.click();
    },

    /** 处理文件导入 */
    handleFileImport(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const config = JSON.parse(e.target.result);
          if (config.nodeForms && Array.isArray(config.nodeForms)) {
            this.nodeForms = config.nodeForms;
            this.$message.success('导入成功');
          } else {
            this.$message.error('文件格式不正确');
          }
        } catch (error) {
          this.$message.error('文件解析失败');
        }
      };
      reader.readAsText(file);
      
      // 清空文件输入
      event.target.value = '';
    },

    /** 根据节点类型获取表单 */
    getFormByNodeType(nodeType) {
      return this.nodeForms.find(form => form.nodeType === nodeType);
    },

    /** 根据节点名称获取表单 */
    getFormByNodeName(nodeName) {
      return this.nodeForms.find(form => form.nodeName === nodeName);
    }
  }
}
</script>

<style lang="scss" scoped>
.node-form-manager {
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
}

.manager-header {
  background-color: #F5F7FA;
  padding: 16px 20px;
  border-bottom: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    color: #303133;
    font-size: 16px;

    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
}

.node-forms-list {
  padding: 20px;
}

.node-title {
  display: flex;
  align-items: center;
  width: 100%;

  i {
    margin-right: 8px;
    color: #409EFF;
  }

  .node-name {
    font-weight: 600;
    margin-right: 12px;
  }

  .field-count {
    margin-left: auto;
    color: #909399;
    font-size: 12px;
  }
}

.node-form-content {
  padding: 16px 0;
}

.node-config {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #FAFAFA;
  border-radius: 4px;
}

.form-designer {
  margin-top: 16px;
}

.manager-footer {
  padding: 16px 20px;
  border-top: 1px solid #EBEEF5;
  background-color: #FAFAFA;
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;

  i {
    font-size: 64px;
    margin-bottom: 16px;
    display: block;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.preview-content {
  min-height: 400px;
}
</style>
