{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\system\\form\\designer.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\views\\system\\form\\designer.vue", "mtime": 1752386648060}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBGb3JtRGVzaWduZXIgZnJvbSAnQC9jb21wb25lbnRzL0Zvcm1EZXNpZ25lci9pbmRleC52dWUnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0Zvcm1EZXNpZ25lclBhZ2UnLAogIGNvbXBvbmVudHM6IHsKICAgIEZvcm1EZXNpZ25lcgogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZvcm1EYXRhOiB7CiAgICAgICAgZm9ybUlkOiBudWxsLAogICAgICAgIGZvcm1OYW1lOiAn5paw5bu66KGo5Y2VJywKICAgICAgICBmb3JtQ29udGVudDogJycsCiAgICAgICAgcmVtYXJrOiAnJwogICAgICB9CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgLy8g5aaC5p6c5pyJ5Lyg5YWl55qE6KGo5Y2VSUTvvIzliJnliqDovb3ooajljZXmlbDmja4KICAgIGNvbnN0IGZvcm1JZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmZvcm1JZAogICAgaWYgKGZvcm1JZCkgewogICAgICB0aGlzLmxvYWRGb3JtRGF0YShmb3JtSWQpCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBsb2FkRm9ybURhdGEoZm9ybUlkKSB7CiAgICAgIC8vIOi/memHjOWPr+S7peiwg+eUqEFQSeWKoOi9veihqOWNleaVsOaNrgogICAgICAvLyBnZXRGb3JtKGZvcm1JZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgIC8vICAgdGhpcy5mb3JtRGF0YSA9IHJlc3BvbnNlLmRhdGEKICAgICAgLy8gfSkKICAgIH0sCiAgICAKICAgIGhhbmRsZVNhdmUoZm9ybURhdGEpIHsKICAgICAgY29uc29sZS5sb2coJ+S/neWtmOihqOWNleaVsOaNrjonLCBmb3JtRGF0YSkKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfooajljZXkv53lrZjmiJDlip/vvIEnKQogICAgICAvLyDov5nph4zlj6/ku6XosIPnlKhBUEnkv53lrZjooajljZUKICAgICAgLy8gaWYgKGZvcm1EYXRhLmZvcm1JZCkgewogICAgICAvLyAgIHVwZGF0ZUZvcm0oZm9ybURhdGEpLnRoZW4oKCkgPT4gewogICAgICAvLyAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfooajljZXmm7TmlrDmiJDlip8nKQogICAgICAvLyAgICAgdGhpcy4kcm91dGVyLmdvKC0xKQogICAgICAvLyAgIH0pCiAgICAgIC8vIH0gZWxzZSB7CiAgICAgIC8vICAgYWRkRm9ybShmb3JtRGF0YSkudGhlbigoKSA9PiB7CiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+ihqOWNleWIm+W7uuaIkOWKnycpCiAgICAgIC8vICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpCiAgICAgIC8vICAgfSkKICAgICAgLy8gfQogICAgfSwKICAgIAogICAgaGFuZGxlQ2FuY2VsKCkgewogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["designer.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "designer.vue", "sourceRoot": "src/views/system/form", "sourcesContent": ["<template>\n  <div class=\"form-designer-page\">\n    <form-designer \n      :form-data=\"formData\"\n      @save=\"handleSave\"\n      @cancel=\"handleCancel\"\n    />\n  </div>\n</template>\n\n<script>\nimport FormDesigner from '@/components/FormDesigner/index.vue'\n\nexport default {\n  name: 'FormDesignerPage',\n  components: {\n    FormDesigner\n  },\n  data() {\n    return {\n      formData: {\n        formId: null,\n        formName: '新建表单',\n        formContent: '',\n        remark: ''\n      }\n    }\n  },\n  created() {\n    // 如果有传入的表单ID，则加载表单数据\n    const formId = this.$route.query.formId\n    if (formId) {\n      this.loadFormData(formId)\n    }\n  },\n  methods: {\n    loadFormData(formId) {\n      // 这里可以调用API加载表单数据\n      // getForm(formId).then(response => {\n      //   this.formData = response.data\n      // })\n    },\n    \n    handleSave(formData) {\n      console.log('保存表单数据:', formData)\n      this.$message.success('表单保存成功！')\n      // 这里可以调用API保存表单\n      // if (formData.formId) {\n      //   updateForm(formData).then(() => {\n      //     this.$message.success('表单更新成功')\n      //     this.$router.go(-1)\n      //   })\n      // } else {\n      //   addForm(formData).then(() => {\n      //     this.$message.success('表单创建成功')\n      //     this.$router.go(-1)\n      //   })\n      // }\n    },\n    \n    handleCancel() {\n      this.$router.go(-1)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-designer-page {\n  height: 100vh;\n  overflow: hidden;\n}\n</style>\n"]}]}