{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormItemWrapper.vue?vue&type=style&index=0&id=5158b843&lang=scss&scoped=true", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\FormDesigner\\components\\FormItemWrapper.vue", "mtime": 1752386433114}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1752199742129}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1752199741452}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1752199741996}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1752199745184}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmZvcm0taXRlbS13cmFwcGVyIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgbWFyZ2luOiA1cHggMDsKICBwYWRkaW5nOiA1cHg7CiAgYm9yZGVyOiAycHggZGFzaGVkIHRyYW5zcGFyZW50OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7CgogICY6aG92ZXIgewogICAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOwogICAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZjsKICB9CgogICYuc2VsZWN0ZWQgewogICAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOwogICAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZjsKICB9CgogICYubGF5b3V0LWl0ZW0gewogICAgLmxheW91dC1jb21wb25lbnQgewogICAgICBtaW4taGVpZ2h0OiAzMHB4OwogICAgfQogIH0KCiAgLml0ZW0tYWN0aW9ucyB7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IC0xNXB4OwogICAgcmlnaHQ6IDA7CiAgICB6LWluZGV4OiAxMDsKICB9CgogIC5mb3JtLWl0ZW0gewogICAgbWFyZ2luLWJvdHRvbTogMDsKICB9CgogIC51cGxvYWQtZHJhZy1hcmVhIHsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIHBhZGRpbmc6IDIwcHg7CiAgICBib3JkZXI6IDFweCBkYXNoZWQgI2Q5ZDlkOTsKICAgIGJvcmRlci1yYWRpdXM6IDZweDsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIAogICAgJjpob3ZlciB7CiAgICAgIGJvcmRlci1jb2xvcjogIzQwOUVGRjsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["FormItemWrapper.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0XA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "FormItemWrapper.vue", "sourceRoot": "src/components/FormDesigner/components", "sourcesContent": ["<template>\n  <div \n    class=\"form-item-wrapper\"\n    :class=\"{ 'selected': selected, 'layout-item': isLayoutItem }\"\n    @click.stop=\"handleSelect\"\n  >\n    <!-- 操作按钮 -->\n    <div class=\"item-actions\" v-show=\"selected\">\n      <el-button-group>\n        <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-copy-document\" @click.stop=\"handleClone\" title=\"复制\"></el-button>\n        <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click.stop=\"handleDelete\" title=\"删除\"></el-button>\n      </el-button-group>\n    </div>\n\n    <!-- 表单项内容 -->\n    <el-col :span=\"item.span || 24\">\n      <!-- 普通表单项 -->\n      <el-form-item \n        v-if=\"!isLayoutItem\"\n        :label=\"item.label\"\n        :label-width=\"item.labelWidth\"\n        :required=\"item.required\"\n        class=\"form-item\"\n      >\n        <!-- 单行文本 -->\n        <el-input\n          v-if=\"item.type === 'input'\"\n          :placeholder=\"item.placeholder\"\n          :clearable=\"item.clearable\"\n          :disabled=\"item.disabled\"\n          :readonly=\"item.readonly\"\n          :maxlength=\"item.maxlength\"\n          :show-word-limit=\"item.showWordLimit\"\n          :prefix-icon=\"item.prefixIcon\"\n          :suffix-icon=\"item.suffixIcon\"\n          :style=\"item.style\"\n          value=\"\"\n        />\n\n        <!-- 多行文本 -->\n        <el-input\n          v-else-if=\"item.type === 'textarea'\"\n          type=\"textarea\"\n          :placeholder=\"item.placeholder\"\n          :rows=\"item.rows\"\n          :autosize=\"item.autosize\"\n          :disabled=\"item.disabled\"\n          :readonly=\"item.readonly\"\n          :maxlength=\"item.maxlength\"\n          :show-word-limit=\"item.showWordLimit\"\n          :style=\"item.style\"\n          value=\"\"\n        />\n\n        <!-- 数字输入 -->\n        <el-input-number\n          v-else-if=\"item.type === 'number'\"\n          :placeholder=\"item.placeholder\"\n          :min=\"item.min\"\n          :max=\"item.max\"\n          :step=\"item.step\"\n          :precision=\"item.precision\"\n          :disabled=\"item.disabled\"\n          :controls=\"item.controls\"\n          :controls-position=\"item.controlsPosition\"\n          :style=\"item.style\"\n          :value=\"0\"\n        />\n\n        <!-- 密码输入 -->\n        <el-input\n          v-else-if=\"item.type === 'password'\"\n          type=\"password\"\n          :placeholder=\"item.placeholder\"\n          :show-password=\"item.showPassword\"\n          :disabled=\"item.disabled\"\n          :readonly=\"item.readonly\"\n          :maxlength=\"item.maxlength\"\n          :style=\"item.style\"\n          value=\"\"\n        />\n\n        <!-- 下拉选择 -->\n        <el-select\n          v-else-if=\"item.type === 'select'\"\n          :placeholder=\"item.placeholder\"\n          :multiple=\"item.multiple\"\n          :disabled=\"item.disabled\"\n          :clearable=\"item.clearable\"\n          :filterable=\"item.filterable\"\n          :allow-create=\"item.allowCreate\"\n          :style=\"item.style\"\n          value=\"\"\n        >\n          <el-option\n            v-for=\"option in item.options\"\n            :key=\"option.value\"\n            :label=\"option.label\"\n            :value=\"option.value\"\n          />\n        </el-select>\n\n        <!-- 单选框 -->\n        <el-radio-group\n          v-else-if=\"item.type === 'radio'\"\n          :disabled=\"item.disabled\"\n          :size=\"item.size\"\n          :text-color=\"item.textColor\"\n          :fill=\"item.fill\"\n          value=\"\"\n        >\n          <el-radio\n            v-for=\"option in item.options\"\n            :key=\"option.value\"\n            :label=\"option.value\"\n          >\n            {{ option.label }}\n          </el-radio>\n        </el-radio-group>\n\n        <!-- 多选框 -->\n        <el-checkbox-group\n          v-else-if=\"item.type === 'checkbox'\"\n          :disabled=\"item.disabled\"\n          :size=\"item.size\"\n          :text-color=\"item.textColor\"\n          :fill=\"item.fill\"\n          :min=\"item.min\"\n          :max=\"item.max\"\n          :value=\"[]\"\n        >\n          <el-checkbox\n            v-for=\"option in item.options\"\n            :key=\"option.value\"\n            :label=\"option.value\"\n          >\n            {{ option.label }}\n          </el-checkbox>\n        </el-checkbox-group>\n\n        <!-- 开关 -->\n        <el-switch\n          v-else-if=\"item.type === 'switch'\"\n          :disabled=\"item.disabled\"\n          :width=\"item.width\"\n          :active-text=\"item.activeText\"\n          :inactive-text=\"item.inactiveText\"\n          :active-value=\"item.activeValue\"\n          :inactive-value=\"item.inactiveValue\"\n          :active-color=\"item.activeColor\"\n          :inactive-color=\"item.inactiveColor\"\n          :value=\"false\"\n        />\n\n        <!-- 滑块 -->\n        <el-slider\n          v-else-if=\"item.type === 'slider'\"\n          :min=\"item.min\"\n          :max=\"item.max\"\n          :step=\"item.step\"\n          :disabled=\"item.disabled\"\n          :show-input=\"item.showInput\"\n          :show-input-controls=\"item.showInputControls\"\n          :show-stops=\"item.showStops\"\n          :show-tooltip=\"item.showTooltip\"\n          :range=\"item.range\"\n          :vertical=\"item.vertical\"\n          :height=\"item.height\"\n          :value=\"0\"\n        />\n\n        <!-- 日期选择 -->\n        <el-date-picker\n          v-else-if=\"item.type === 'date'\"\n          :type=\"item.type\"\n          :placeholder=\"item.placeholder\"\n          :disabled=\"item.disabled\"\n          :clearable=\"item.clearable\"\n          :readonly=\"item.readonly\"\n          :editable=\"item.editable\"\n          :format=\"item.format\"\n          :value-format=\"item.valueFormat\"\n          :start-placeholder=\"item.startPlaceholder\"\n          :end-placeholder=\"item.endPlaceholder\"\n          :style=\"item.style\"\n          value=\"\"\n        />\n\n        <!-- 时间选择 -->\n        <el-time-picker\n          v-else-if=\"item.type === 'time'\"\n          :placeholder=\"item.placeholder\"\n          :disabled=\"item.disabled\"\n          :clearable=\"item.clearable\"\n          :readonly=\"item.readonly\"\n          :editable=\"item.editable\"\n          :format=\"item.format\"\n          :value-format=\"item.valueFormat\"\n          :style=\"item.style\"\n          value=\"\"\n        />\n\n        <!-- 评分 -->\n        <el-rate\n          v-else-if=\"item.type === 'rate'\"\n          :max=\"item.max\"\n          :disabled=\"item.disabled\"\n          :allow-half=\"item.allowHalf\"\n          :low-threshold=\"item.lowThreshold\"\n          :high-threshold=\"item.highThreshold\"\n          :colors=\"item.colors\"\n          :void-color=\"item.voidColor\"\n          :disabled-void-color=\"item.disabledVoidColor\"\n          :icon-classes=\"item.iconClasses\"\n          :void-icon-class=\"item.voidIconClass\"\n          :disabled-void-icon-class=\"item.disabledVoidIconClass\"\n          :show-text=\"item.showText\"\n          :show-score=\"item.showScore\"\n          :text-color=\"item.textColor\"\n          :texts=\"item.texts\"\n          :score-template=\"item.scoreTemplate\"\n          :value=\"0\"\n        />\n\n        <!-- 级联选择 -->\n        <el-cascader\n          v-else-if=\"item.type === 'cascader'\"\n          :placeholder=\"item.placeholder\"\n          :disabled=\"item.disabled\"\n          :clearable=\"item.clearable\"\n          :show-all-levels=\"item.showAllLevels\"\n          :collapse-tags=\"item.collapseTags\"\n          :separator=\"item.separator\"\n          :filterable=\"item.filterable\"\n          :options=\"item.options\"\n          :props=\"item.props\"\n          :style=\"item.style\"\n          :value=\"[]\"\n        />\n\n        <!-- 颜色选择 -->\n        <el-color-picker\n          v-else-if=\"item.type === 'color'\"\n          :disabled=\"item.disabled\"\n          :size=\"item.size\"\n          :show-alpha=\"item.showAlpha\"\n          :color-format=\"item.colorFormat\"\n          :popper-class=\"item.popperClass\"\n          :predefine=\"item.predefine\"\n          value=\"\"\n        />\n\n        <!-- 文件上传 -->\n        <el-upload\n          v-else-if=\"item.type === 'upload'\"\n          :action=\"item.action\"\n          :multiple=\"item.multiple\"\n          :disabled=\"item.disabled\"\n          :accept=\"item.accept\"\n          :list-type=\"item.listType\"\n          :auto-upload=\"item.autoUpload\"\n          :show-file-list=\"item.showFileList\"\n          :drag=\"item.drag\"\n          :limit=\"item.limit\"\n        >\n          <el-button v-if=\"!item.drag\" size=\"small\" type=\"primary\">{{ item.buttonText }}</el-button>\n          <div v-else class=\"upload-drag-area\">\n            <i class=\"el-icon-upload\"></i>\n            <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n          </div>\n          <div v-if=\"item.tip\" slot=\"tip\" class=\"el-upload__tip\">{{ item.tip }}</div>\n        </el-upload>\n      </el-form-item>\n\n      <!-- 布局组件 -->\n      <div v-else class=\"layout-component\">\n        <!-- 分割线 -->\n        <el-divider\n          v-if=\"item.type === 'divider'\"\n          :direction=\"item.direction\"\n          :content-position=\"item.contentPosition\"\n        >\n          {{ item.text }}\n        </el-divider>\n\n        <!-- 文本 -->\n        <div\n          v-else-if=\"item.type === 'text'\"\n          :style=\"{\n            textAlign: item.textAlign,\n            fontSize: item.fontSize,\n            color: item.color,\n            fontWeight: item.fontWeight\n          }\"\n        >\n          {{ item.text }}\n        </div>\n\n        <!-- HTML -->\n        <div\n          v-else-if=\"item.type === 'html'\"\n          v-html=\"item.html\"\n        ></div>\n\n        <!-- 按钮 -->\n        <el-button\n          v-else-if=\"item.type === 'button'\"\n          :type=\"item.type\"\n          :size=\"item.size\"\n          :plain=\"item.plain\"\n          :round=\"item.round\"\n          :circle=\"item.circle\"\n          :disabled=\"item.disabled\"\n          :icon=\"item.icon\"\n          :loading=\"item.loading\"\n          :style=\"item.style\"\n        >\n          {{ item.text }}\n        </el-button>\n\n        <!-- 警告提示 -->\n        <el-alert\n          v-else-if=\"item.type === 'alert'\"\n          :title=\"item.title\"\n          :type=\"item.type\"\n          :description=\"item.description\"\n          :closable=\"item.closable\"\n          :center=\"item.center\"\n          :close-text=\"item.closeText\"\n          :show-icon=\"item.showIcon\"\n          :effect=\"item.effect\"\n        />\n      </div>\n    </el-col>\n  </div>\n</template>\n\n<script>\nimport { isLayoutComponent } from '../utils/index.js'\n\nexport default {\n  name: 'FormItemWrapper',\n  props: {\n    item: {\n      type: Object,\n      required: true\n    },\n    index: {\n      type: Number,\n      required: true\n    },\n    selected: {\n      type: Boolean,\n      default: false\n    }\n  },\n  computed: {\n    isLayoutItem() {\n      return isLayoutComponent(this.item.type)\n    }\n  },\n  methods: {\n    handleSelect() {\n      this.$emit('select', this.item)\n    },\n\n    handleDelete() {\n      this.$emit('delete', this.index)\n    },\n\n    handleClone() {\n      this.$emit('clone', this.item)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.form-item-wrapper {\n  position: relative;\n  margin: 5px 0;\n  padding: 5px;\n  border: 2px dashed transparent;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    border-color: #409EFF;\n    background-color: #f0f9ff;\n  }\n\n  &.selected {\n    border-color: #409EFF;\n    background-color: #f0f9ff;\n  }\n\n  &.layout-item {\n    .layout-component {\n      min-height: 30px;\n    }\n  }\n\n  .item-actions {\n    position: absolute;\n    top: -15px;\n    right: 0;\n    z-index: 10;\n  }\n\n  .form-item {\n    margin-bottom: 0;\n  }\n\n  .upload-drag-area {\n    text-align: center;\n    padding: 20px;\n    border: 1px dashed #d9d9d9;\n    border-radius: 6px;\n    cursor: pointer;\n    \n    &:hover {\n      border-color: #409EFF;\n    }\n  }\n}\n</style>\n"]}]}