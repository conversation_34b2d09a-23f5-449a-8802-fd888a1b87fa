{"remainingRequest": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeFormManager\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\RuoYi-flowable\\ruoyi-ui\\src\\components\\NodeFormManager\\index.vue", "mtime": 1752410071006}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\babel.config.js", "mtime": 1752196621229}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1752199743224}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1752199743474}, {"path": "D:\\RuoYi-flowable\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1752199741394}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_NodeForm", "_interopRequireDefault", "require", "name", "components", "NodeForm", "props", "value", "type", "Array", "default", "data", "nodeForms", "activeNodes", "showPreview", "previewActiveTab", "watch", "handler", "newVal", "immediate", "deep", "$emit", "methods", "addNodeForm", "newNodeForm", "id", "concat", "Date", "now", "nodeName", "length", "nodeType", "fields", "push", "removeNodeForm", "index", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "then", "splice", "$message", "success", "catch", "getNodeTypeTag", "tagMap", "start", "userTask", "approval", "end", "getNodeTypeText", "textMap", "previewForms", "warning", "saveForms", "config", "createTime", "toISOString", "version", "console", "log", "exportForms", "blob", "Blob", "JSON", "stringify", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "click", "revokeObjectURL", "importForms", "$refs", "fileInput", "handleFileImport", "event", "_this2", "file", "target", "files", "reader", "FileReader", "onload", "e", "parse", "result", "isArray", "error", "readAsText", "getFormByNodeType", "find", "form", "getFormByNodeName"], "sources": ["src/components/NodeFormManager/index.vue"], "sourcesContent": ["<template>\n  <div class=\"node-form-manager\">\n    <div class=\"manager-header\">\n      <h3>\n        <i class=\"el-icon-s-order\"></i>\n        流程节点表单管理\n      </h3>\n      <el-button type=\"primary\" @click=\"addNodeForm\">\n        <i class=\"el-icon-plus\"></i>\n        添加节点表单\n      </el-button>\n    </div>\n\n    <div class=\"node-forms-list\">\n      <el-collapse v-model=\"activeNodes\" accordion>\n        <el-collapse-item \n          v-for=\"(nodeForm, index) in nodeForms\" \n          :key=\"nodeForm.id\"\n          :name=\"nodeForm.id\"\n        >\n          <template slot=\"title\">\n            <div class=\"node-title\">\n              <i class=\"el-icon-document\"></i>\n              <span class=\"node-name\">{{ nodeForm.nodeName }}</span>\n              <el-tag :type=\"getNodeTypeTag(nodeForm.nodeType)\" size=\"mini\">\n                {{ getNodeTypeText(nodeForm.nodeType) }}\n              </el-tag>\n              <span class=\"field-count\">{{ nodeForm.fields.length }} 个字段</span>\n            </div>\n          </template>\n\n          <div class=\"node-form-content\">\n            <div class=\"node-config\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"8\">\n                  <el-form-item label=\"节点名称\">\n                    <el-input v-model=\"nodeForm.nodeName\" placeholder=\"请输入节点名称\" />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"节点类型\">\n                    <el-select v-model=\"nodeForm.nodeType\" style=\"width: 100%\">\n                      <el-option label=\"开始节点\" value=\"start\" />\n                      <el-option label=\"用户任务\" value=\"userTask\" />\n                      <el-option label=\"审批节点\" value=\"approval\" />\n                      <el-option label=\"结束节点\" value=\"end\" />\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"操作\">\n                    <el-button type=\"danger\" size=\"small\" @click=\"removeNodeForm(index)\">\n                      删除节点\n                    </el-button>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </div>\n\n            <div class=\"form-designer\">\n              <node-form \n                v-model=\"nodeForm.fields\" \n                :title=\"`${nodeForm.nodeName} - 表单设计`\"\n              />\n            </div>\n          </div>\n        </el-collapse-item>\n      </el-collapse>\n\n      <div v-if=\"nodeForms.length === 0\" class=\"empty-state\">\n        <i class=\"el-icon-document-add\"></i>\n        <p>暂无节点表单，点击\"添加节点表单\"开始创建</p>\n      </div>\n    </div>\n\n    <div class=\"manager-footer\">\n      <el-button @click=\"previewForms\">预览表单</el-button>\n      <el-button type=\"primary\" @click=\"saveForms\">保存配置</el-button>\n      <el-button @click=\"exportForms\">导出配置</el-button>\n      <el-button @click=\"importForms\">导入配置</el-button>\n    </div>\n\n    <!-- 预览对话框 -->\n    <el-dialog title=\"表单预览\" :visible.sync=\"showPreview\" width=\"80%\">\n      <div class=\"preview-content\">\n        <el-tabs v-model=\"previewActiveTab\" type=\"card\">\n          <el-tab-pane \n            v-for=\"nodeForm in nodeForms\" \n            :key=\"nodeForm.id\"\n            :label=\"nodeForm.nodeName\"\n            :name=\"nodeForm.id\"\n          >\n            <node-form \n              :value=\"nodeForm.fields\" \n              :title=\"nodeForm.nodeName\"\n              :readonly=\"true\"\n            />\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n\n    <!-- 导入配置 -->\n    <input \n      ref=\"fileInput\" \n      type=\"file\" \n      accept=\".json\" \n      style=\"display: none\" \n      @change=\"handleFileImport\"\n    />\n  </div>\n</template>\n\n<script>\nimport NodeForm from '@/components/NodeForm'\n\nexport default {\n  name: 'NodeFormManager',\n  components: {\n    NodeForm\n  },\n  props: {\n    value: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      nodeForms: [],\n      activeNodes: '',\n      showPreview: false,\n      previewActiveTab: ''\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        this.nodeForms = newVal || [];\n      },\n      immediate: true,\n      deep: true\n    },\n    nodeForms: {\n      handler(newVal) {\n        this.$emit('input', newVal);\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 添加节点表单 */\n    addNodeForm() {\n      const newNodeForm = {\n        id: `node_${Date.now()}`,\n        nodeName: `节点${this.nodeForms.length + 1}`,\n        nodeType: 'userTask',\n        fields: []\n      };\n      \n      this.nodeForms.push(newNodeForm);\n      this.activeNodes = newNodeForm.id;\n    },\n\n    /** 删除节点表单 */\n    removeNodeForm(index) {\n      this.$confirm('确定要删除这个节点表单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.nodeForms.splice(index, 1);\n        this.$message.success('删除成功');\n      }).catch(() => {});\n    },\n\n    /** 获取节点类型标签 */\n    getNodeTypeTag(type) {\n      const tagMap = {\n        start: 'success',\n        userTask: 'primary',\n        approval: 'warning',\n        end: 'info'\n      };\n      return tagMap[type] || 'primary';\n    },\n\n    /** 获取节点类型文本 */\n    getNodeTypeText(type) {\n      const textMap = {\n        start: '开始节点',\n        userTask: '用户任务',\n        approval: '审批节点',\n        end: '结束节点'\n      };\n      return textMap[type] || type;\n    },\n\n    /** 预览表单 */\n    previewForms() {\n      if (this.nodeForms.length === 0) {\n        this.$message.warning('暂无表单可预览');\n        return;\n      }\n      this.previewActiveTab = this.nodeForms[0].id;\n      this.showPreview = true;\n    },\n\n    /** 保存配置 */\n    saveForms() {\n      // 这里可以调用API保存到后端\n      const config = {\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      console.log('保存表单配置:', config);\n      this.$message.success('保存成功');\n      \n      // 触发保存事件\n      this.$emit('save', config);\n    },\n\n    /** 导出配置 */\n    exportForms() {\n      const config = {\n        nodeForms: this.nodeForms,\n        createTime: new Date().toISOString(),\n        version: '1.0'\n      };\n      \n      const blob = new Blob([JSON.stringify(config, null, 2)], { \n        type: 'application/json' \n      });\n      \n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `node-forms-${Date.now()}.json`;\n      a.click();\n      URL.revokeObjectURL(url);\n      \n      this.$message.success('导出成功');\n    },\n\n    /** 导入配置 */\n    importForms() {\n      this.$refs.fileInput.click();\n    },\n\n    /** 处理文件导入 */\n    handleFileImport(event) {\n      const file = event.target.files[0];\n      if (!file) return;\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const config = JSON.parse(e.target.result);\n          if (config.nodeForms && Array.isArray(config.nodeForms)) {\n            this.nodeForms = config.nodeForms;\n            this.$message.success('导入成功');\n          } else {\n            this.$message.error('文件格式不正确');\n          }\n        } catch (error) {\n          this.$message.error('文件解析失败');\n        }\n      };\n      reader.readAsText(file);\n      \n      // 清空文件输入\n      event.target.value = '';\n    },\n\n    /** 根据节点类型获取表单 */\n    getFormByNodeType(nodeType) {\n      return this.nodeForms.find(form => form.nodeType === nodeType);\n    },\n\n    /** 根据节点名称获取表单 */\n    getFormByNodeName(nodeName) {\n      return this.nodeForms.find(form => form.nodeName === nodeName);\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.node-form-manager {\n  background-color: white;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.manager-header {\n  background-color: #F5F7FA;\n  padding: 16px 20px;\n  border-bottom: 1px solid #EBEEF5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h3 {\n    margin: 0;\n    color: #303133;\n    font-size: 16px;\n\n    i {\n      margin-right: 8px;\n      color: #409EFF;\n    }\n  }\n}\n\n.node-forms-list {\n  padding: 20px;\n}\n\n.node-title {\n  display: flex;\n  align-items: center;\n  width: 100%;\n\n  i {\n    margin-right: 8px;\n    color: #409EFF;\n  }\n\n  .node-name {\n    font-weight: 600;\n    margin-right: 12px;\n  }\n\n  .field-count {\n    margin-left: auto;\n    color: #909399;\n    font-size: 12px;\n  }\n}\n\n.node-form-content {\n  padding: 16px 0;\n}\n\n.node-config {\n  margin-bottom: 20px;\n  padding: 16px;\n  background-color: #FAFAFA;\n  border-radius: 4px;\n}\n\n.form-designer {\n  margin-top: 16px;\n}\n\n.manager-footer {\n  padding: 16px 20px;\n  border-top: 1px solid #EBEEF5;\n  background-color: #FAFAFA;\n  text-align: right;\n\n  .el-button {\n    margin-left: 8px;\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n\n  i {\n    font-size: 64px;\n    margin-bottom: 16px;\n    display: block;\n  }\n\n  p {\n    margin: 0;\n    font-size: 14px;\n  }\n}\n\n.preview-content {\n  min-height: 400px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAkHA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA;EACA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,WAAA;MACAC,WAAA;MACAC,gBAAA;IACA;EACA;EACAC,KAAA;IACAT,KAAA;MACAU,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAAN,SAAA,GAAAM,MAAA;MACA;MACAC,SAAA;MACAC,IAAA;IACA;IACAR,SAAA;MACAK,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAAG,KAAA,UAAAH,MAAA;MACA;MACAE,IAAA;IACA;EACA;EACAE,OAAA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,IAAAC,WAAA;QACAC,EAAA,UAAAC,MAAA,CAAAC,IAAA,CAAAC,GAAA;QACAC,QAAA,iBAAAH,MAAA,MAAAd,SAAA,CAAAkB,MAAA;QACAC,QAAA;QACAC,MAAA;MACA;MAEA,KAAApB,SAAA,CAAAqB,IAAA,CAAAT,WAAA;MACA,KAAAX,WAAA,GAAAW,WAAA,CAAAC,EAAA;IACA;IAEA,aACAS,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA/B,IAAA;MACA,GAAAgC,IAAA;QACAJ,KAAA,CAAAxB,SAAA,CAAA6B,MAAA,CAAAN,KAAA;QACAC,KAAA,CAAAM,QAAA,CAAAC,OAAA;MACA,GAAAC,KAAA;IACA;IAEA,eACAC,cAAA,WAAAA,eAAArC,IAAA;MACA,IAAAsC,MAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,GAAA;MACA;MACA,OAAAJ,MAAA,CAAAtC,IAAA;IACA;IAEA,eACA2C,eAAA,WAAAA,gBAAA3C,IAAA;MACA,IAAA4C,OAAA;QACAL,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,GAAA;MACA;MACA,OAAAE,OAAA,CAAA5C,IAAA,KAAAA,IAAA;IACA;IAEA,WACA6C,YAAA,WAAAA,aAAA;MACA,SAAAzC,SAAA,CAAAkB,MAAA;QACA,KAAAY,QAAA,CAAAY,OAAA;QACA;MACA;MACA,KAAAvC,gBAAA,QAAAH,SAAA,IAAAa,EAAA;MACA,KAAAX,WAAA;IACA;IAEA,WACAyC,SAAA,WAAAA,UAAA;MACA;MACA,IAAAC,MAAA;QACA5C,SAAA,OAAAA,SAAA;QACA6C,UAAA,MAAA9B,IAAA,GAAA+B,WAAA;QACAC,OAAA;MACA;MAEAC,OAAA,CAAAC,GAAA,YAAAL,MAAA;MACA,KAAAd,QAAA,CAAAC,OAAA;;MAEA;MACA,KAAAtB,KAAA,SAAAmC,MAAA;IACA;IAEA,WACAM,WAAA,WAAAA,YAAA;MACA,IAAAN,MAAA;QACA5C,SAAA,OAAAA,SAAA;QACA6C,UAAA,MAAA9B,IAAA,GAAA+B,WAAA;QACAC,OAAA;MACA;MAEA,IAAAI,IAAA,OAAAC,IAAA,EAAAC,IAAA,CAAAC,SAAA,CAAAV,MAAA;QACAhD,IAAA;MACA;MAEA,IAAA2D,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAN,IAAA;MACA,IAAAO,CAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,CAAA,CAAAG,IAAA,GAAAN,GAAA;MACAG,CAAA,CAAAI,QAAA,iBAAAhD,MAAA,CAAAC,IAAA,CAAAC,GAAA;MACA0C,CAAA,CAAAK,KAAA;MACAP,GAAA,CAAAQ,eAAA,CAAAT,GAAA;MAEA,KAAAzB,QAAA,CAAAC,OAAA;IACA;IAEA,WACAkC,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA,CAAAC,SAAA,CAAAJ,KAAA;IACA;IAEA,aACAK,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,KAAA,CAAAG,MAAA,CAAAC,KAAA;MACA,KAAAF,IAAA;MAEA,IAAAG,MAAA,OAAAC,UAAA;MACAD,MAAA,CAAAE,MAAA,aAAAC,CAAA;QACA;UACA,IAAAjC,MAAA,GAAAS,IAAA,CAAAyB,KAAA,CAAAD,CAAA,CAAAL,MAAA,CAAAO,MAAA;UACA,IAAAnC,MAAA,CAAA5C,SAAA,IAAAH,KAAA,CAAAmF,OAAA,CAAApC,MAAA,CAAA5C,SAAA;YACAsE,MAAA,CAAAtE,SAAA,GAAA4C,MAAA,CAAA5C,SAAA;YACAsE,MAAA,CAAAxC,QAAA,CAAAC,OAAA;UACA;YACAuC,MAAA,CAAAxC,QAAA,CAAAmD,KAAA;UACA;QACA,SAAAA,KAAA;UACAX,MAAA,CAAAxC,QAAA,CAAAmD,KAAA;QACA;MACA;MACAP,MAAA,CAAAQ,UAAA,CAAAX,IAAA;;MAEA;MACAF,KAAA,CAAAG,MAAA,CAAA7E,KAAA;IACA;IAEA,iBACAwF,iBAAA,WAAAA,kBAAAhE,QAAA;MACA,YAAAnB,SAAA,CAAAoF,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAlE,QAAA,KAAAA,QAAA;MAAA;IACA;IAEA,iBACAmE,iBAAA,WAAAA,kBAAArE,QAAA;MACA,YAAAjB,SAAA,CAAAoF,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAApE,QAAA,KAAAA,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}